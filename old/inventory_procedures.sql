-- Add inventory adjustment procedures

-- Procedure: sp_create_inventory_adjustment
-- Creates an inventory adjustment record
CREATE OR REPLACE PROCEDURE sp_create_inventory_adjustment(
    p_tenant_id UUID,
    p_batch_id UUID,
    p_employee_id UUID,
    p_adjustment_type_id UUID,
    p_quantity_change NUMERIC,
    p_reason TEXT,
    INOUT p_new_adjustment_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_adjustment_type_action VARCHAR(20);
    v_current_quantity NUMERIC;
    v_new_quantity NUMERIC;
    v_medicine_id UUID;
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate batch
    IF NOT EXISTS (
        SELECT 1 FROM batches
        WHERE batch_id = p_batch_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Batch ID not found.';
        RETURN;
    END IF;

    -- Validate employee
    IF NOT EXISTS (
        SELECT 1 FROM employees
        WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Employee ID not found.';
        RETURN;
    END IF;

    -- Validate adjustment type
    IF NOT EXISTS (
        SELECT 1 FROM inventory_adjustment_types
        WHERE adjustment_type_id = p_adjustment_type_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Adjustment type ID not found.';
        RETURN;
    END IF;

    -- Get adjustment type action (INCREASE or DECREASE)
    SELECT action INTO v_adjustment_type_action
    FROM inventory_adjustment_types
    WHERE adjustment_type_id = p_adjustment_type_id AND tenant_id = p_tenant_id;

    -- Get current batch quantity
    SELECT quantity_remaining, medicine_id INTO v_current_quantity, v_medicine_id
    FROM batches
    WHERE batch_id = p_batch_id AND tenant_id = p_tenant_id;

    -- Validate quantity change based on adjustment type
    IF (v_adjustment_type_action = 'DECREASE' AND p_quantity_change > 0) OR
       (v_adjustment_type_action = 'INCREASE' AND p_quantity_change < 0) THEN
        p_message := 'Quantity change direction does not match adjustment type action.';
        RETURN;
    END IF;

    -- Calculate new quantity
    v_new_quantity := v_current_quantity + p_quantity_change;

    -- Validate new quantity
    IF v_new_quantity < 0 THEN
        p_message := 'Adjustment would result in negative inventory.';
        RETURN;
    END IF;

    -- Create inventory adjustment record
    INSERT INTO inventory_adjustments (
        tenant_id,
        batch_id,
        employee_id,
        adjustment_type_id,
        quantity_change,
        reason
    ) VALUES (
        p_tenant_id,
        p_batch_id,
        p_employee_id,
        p_adjustment_type_id,
        p_quantity_change,
        p_reason
    ) RETURNING adjustment_id INTO p_new_adjustment_id;

    -- Update batch quantity
    UPDATE batches
    SET
        quantity_remaining = v_new_quantity,
        updated_at = CURRENT_TIMESTAMP
    WHERE
        batch_id = p_batch_id AND
        tenant_id = p_tenant_id;

    p_message := 'Inventory adjustment created successfully with ID ' || p_new_adjustment_id::TEXT || '.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error creating inventory adjustment: ' || SQLERRM;
        p_new_adjustment_id := NULL;
END;
$$;

-- Procedure: sp_add_invoice_payment
-- Adds a payment to a sales transaction
CREATE OR REPLACE PROCEDURE sp_add_invoice_payment(
    p_tenant_id UUID,
    p_sale_transaction_id UUID,
    p_payment_method_id UUID,
    p_amount_paid NUMERIC,
    p_reference_number VARCHAR(100) DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    INOUT p_new_payment_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_transaction_final_amount NUMERIC;
    v_total_payments_before NUMERIC;
    v_total_payments_after NUMERIC;
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate transaction
    IF NOT EXISTS (
        SELECT 1 FROM sales_transactions
        WHERE sale_transaction_id = p_sale_transaction_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Transaction ID not found.';
        RETURN;
    END IF;

    -- Validate payment method
    IF NOT EXISTS (
        SELECT 1 FROM payment_methods
        WHERE payment_method_id = p_payment_method_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Payment method ID not found.';
        RETURN;
    END IF;

    -- Validate amount
    IF p_amount_paid <= 0 THEN
        p_message := 'Payment amount must be greater than zero.';
        RETURN;
    END IF;

    -- Get transaction final amount
    SELECT final_amount INTO v_transaction_final_amount
    FROM sales_transactions
    WHERE sale_transaction_id = p_sale_transaction_id AND tenant_id = p_tenant_id;

    -- Get total payments made so far
    SELECT COALESCE(SUM(amount_paid), 0) INTO v_total_payments_before
    FROM invoice_payments
    WHERE sale_transaction_id = p_sale_transaction_id AND tenant_id = p_tenant_id;

    -- Insert payment
    INSERT INTO invoice_payments (
        tenant_id,
        sale_transaction_id,
        payment_method_id,
        amount_paid,
        reference_number,
        notes
    ) VALUES (
        p_tenant_id,
        p_sale_transaction_id,
        p_payment_method_id,
        p_amount_paid,
        p_reference_number,
        p_notes
    ) RETURNING payment_id INTO p_new_payment_id;

    -- Calculate total payments after this payment
    v_total_payments_after := v_total_payments_before + p_amount_paid;

    -- Check if payment completes the transaction
    IF v_total_payments_after >= v_transaction_final_amount THEN
        -- Update transaction to mark as fully paid
        UPDATE sales_transactions
        SET is_fully_paid = TRUE
        WHERE sale_transaction_id = p_sale_transaction_id AND tenant_id = p_tenant_id;
    END IF;

    p_message := 'Payment added successfully with ID ' || p_new_payment_id::TEXT || '.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error adding payment: ' || SQLERRM;
        p_new_payment_id := NULL;
END;
$$;
