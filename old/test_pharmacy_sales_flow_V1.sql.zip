-- <PERSON><PERSON><PERSON> bản test cho quy trình bán hàng Pharmacy Management
-- <PERSON><PERSON>a dữ liệu cũ để đảm bảo test chạy trên môi trường sạch (nếu cần và cẩn thận)
-- TRUNCATE TABLE sales_transaction_details, sales_transactions, inventory_adjustments, batches, import_invoice_details, import_invoices, medicines, categories, customers, employees, tenants CASCADE;

DO $$
DECLARE
    -- Tenant
    v_tenant_id UUID;
    v_tenant_name TEXT := 'Nhà Thuốc An Khang Test';

    -- Employee
    v_employee_id UUID;
    v_employee_name TEXT := 'Nguyễn Văn A (Dược Sĩ Test)';

    -- Categories
    v_category_id_khang_sinh UUID;
    v_category_id_giam_dau UUID;

    -- Medicines & Packaging Units
    v_medicine_id_amox UUID;
    v_medicine_name_amox TEXT := 'Amoxicillin 500mg Test';
    v_packaging_id_amox_hop UUID;

    v_medicine_id_para UUID;
    v_medicine_name_para TEXT := 'Paracetamol 250mg Test';
    v_packaging_id_para_vi UUID;

    -- Supplier
    v_supplier_id UUID;
    v_supplier_name TEXT := 'Công Ty Dược Hậu Giang Test';

    -- Customer
    v_customer_id UUID;
    v_customer_name TEXT := 'Trần Thị B (Khách Test)';

    -- Payment Methods
    v_payment_method_id_cash UUID;
    v_payment_method_id_card UUID;

    -- Transaction Statuses
    v_status_id_completed UUID;
    v_status_id_pending UUID;
    v_status_id_cancelled UUID;

    -- Import Invoice & Details
    v_import_invoice_id UUID;
    v_import_detail_id_amox UUID;
    v_import_detail_id_para UUID;

    -- Batches
    v_batch_id_amox UUID;
    v_batch_id_para UUID;

    -- Sales Transaction
    v_sale_transaction_id UUID;
    v_sale_message TEXT;

    -- Stock Check
    v_stock_amox NUMERIC;
    v_stock_para NUMERIC;

    -- Inventory Adjustment Types (nếu cần cho các test khác, ở đây chưa dùng trực tiếp)
    v_adj_type_nhap UUID;
    v_adj_type_xuat UUID;

BEGIN
    RAISE NOTICE '========== STARTING PHARMACY SALES FLOW TEST =========';

    -- 1. SETUP BASIC DATA
    RAISE NOTICE '---- 1. Setting up Basic Data ----';

    -- Tenant
    RAISE NOTICE '  Creating Tenant: %', v_tenant_name;
    INSERT INTO tenants (tenant_name, email, phone_number, address, subscription_start_date, is_active)
    VALUES (v_tenant_name, '<EMAIL>', '0901234567', '123 Đường Test, Q.Test, TP.Test', CURRENT_DATE, TRUE)
    RETURNING tenant_id INTO v_tenant_id;
    RAISE NOTICE '    Tenant ID: %', v_tenant_id;

    -- Employee
    RAISE NOTICE '  Creating Employee: % for Tenant ID: %', v_employee_name, v_tenant_id;
    INSERT INTO employees (tenant_id, full_name, email, phone_number, is_active)
    VALUES (v_tenant_id, v_employee_name, '<EMAIL>', '0907654321', TRUE)
    RETURNING employee_id INTO v_employee_id;
    RAISE NOTICE '    Employee ID: %', v_employee_id;

    -- Categories
    RAISE NOTICE '  Creating Categories...';
    INSERT INTO categories (tenant_id, category_name, description) VALUES (v_tenant_id, 'Kháng Sinh Test', 'Nhóm thuốc kháng sinh') RETURNING category_id INTO v_category_id_khang_sinh;
    INSERT INTO categories (tenant_id, category_name, description) VALUES (v_tenant_id, 'Giảm Đau Hạ Sốt Test', 'Nhóm thuốc giảm đau, hạ sốt') RETURNING category_id INTO v_category_id_giam_dau;
    RAISE NOTICE '    Category Kháng Sinh ID: %', v_category_id_khang_sinh;
    RAISE NOTICE '    Category Giảm Đau ID: %', v_category_id_giam_dau;

    -- Medicine 1: Amoxicillin
    RAISE NOTICE '  Adding Medicine: %', v_medicine_name_amox;
    CALL sp_add_medicine(
        p_tenant_id                 => v_tenant_id,
        p_medicine_name             => v_medicine_name_amox,
        p_description               => 'Kháng sinh phổ rộng', 
        p_category_id               => v_category_id_khang_sinh,
        p_manufacturer_name         => 'Manufacturer A Test',
        p_base_unit_of_measure      => 'Viên',
        p_is_prescription_required  => TRUE,
        p_new_medicine_id           => v_medicine_id_amox -- OUT
    );
    RAISE NOTICE '    Amoxicillin Medicine ID: %', v_medicine_id_amox;
    IF v_medicine_id_amox IS NULL THEN RAISE EXCEPTION 'Failed to add Amoxicillin'; END IF;

    RAISE NOTICE '    Adding Packaging Unit for Amoxicillin (Hộp 100 viên)...';
    CALL sp_add_medicine_packaging_unit(
        p_tenant_id                 => v_tenant_id,
        p_medicine_id               => v_medicine_id_amox,
        p_unit_name                 => 'Hộp',
        p_quantity_per_unit         => 100,
        p_is_default_sale_unit      => TRUE,
        p_new_packaging_unit_id     => v_packaging_id_amox_hop -- OUT
    );
    RAISE NOTICE '      Amoxicillin Packaging (Hộp) ID: %', v_packaging_id_amox_hop;
    IF v_packaging_id_amox_hop IS NULL THEN RAISE EXCEPTION 'Failed to add Amoxicillin packaging unit'; END IF;

    -- Medicine 2: Paracetamol
    RAISE NOTICE '  Adding Medicine: %', v_medicine_name_para;
    CALL sp_add_medicine(
        p_tenant_id                 => v_tenant_id,
        p_medicine_name             => v_medicine_name_para,
        p_description               => 'Giảm đau, hạ sốt',
        p_category_id               => v_category_id_giam_dau,
        p_manufacturer_name         => 'Manufacturer B Test',
        p_base_unit_of_measure      => 'Viên',
        p_is_prescription_required  => FALSE,
        p_new_medicine_id           => v_medicine_id_para -- OUT
    );
    RAISE NOTICE '    Paracetamol Medicine ID: %', v_medicine_id_para;
    IF v_medicine_id_para IS NULL THEN RAISE EXCEPTION 'Failed to add Paracetamol'; END IF;

    RAISE NOTICE '    Adding Packaging Unit for Paracetamol (Vỉ 10 viên)...';
    CALL sp_add_medicine_packaging_unit(
        p_tenant_id                 => v_tenant_id,
        p_medicine_id               => v_medicine_id_para,
        p_unit_name                 => 'Vỉ',
        p_quantity_per_unit         => 10,
        p_is_default_sale_unit      => TRUE,
        p_new_packaging_unit_id     => v_packaging_id_para_vi -- OUT
    );
    RAISE NOTICE '      Paracetamol Packaging (Vỉ) ID: %', v_packaging_id_para_vi;
    IF v_packaging_id_para_vi IS NULL THEN RAISE EXCEPTION 'Failed to add Paracetamol packaging unit'; END IF;

    -- Supplier
    RAISE NOTICE '  Creating Supplier: %', v_supplier_name;
    INSERT INTO suppliers (tenant_id, supplier_name, contact_person, email, phone_number)
    VALUES (v_tenant_id, v_supplier_name, 'Ms. Supplier Test', '<EMAIL>', '0912345678')
    RETURNING supplier_id INTO v_supplier_id;
    RAISE NOTICE '    Supplier ID: %', v_supplier_id;

    -- Customer
    RAISE NOTICE '  Creating Customer: %', v_customer_name;
    INSERT INTO customers (tenant_id, full_name, phone_number, email, address)
    VALUES (v_tenant_id, v_customer_name, '0987654321', '<EMAIL>', '456 Đường Khách, P.Test, Q.Test')
    RETURNING customer_id INTO v_customer_id;
    RAISE NOTICE '    Customer ID: %', v_customer_id;

    -- Payment Methods
    RAISE NOTICE '  Creating Payment Methods...';
    INSERT INTO payment_methods (tenant_id, method_name, is_active) VALUES (v_tenant_id, 'Tiền Mặt Test', TRUE) RETURNING payment_method_id INTO v_payment_method_id_cash;
    INSERT INTO payment_methods (tenant_id, method_name, is_active) VALUES (v_tenant_id, 'Thẻ Test', TRUE) RETURNING payment_method_id INTO v_payment_method_id_card;
    RAISE NOTICE '    Payment Method Cash ID: %', v_payment_method_id_cash;
    RAISE NOTICE '    Payment Method Card ID: %', v_payment_method_id_card;

    -- Transaction Statuses
    RAISE NOTICE '  Creating Transaction Statuses...';
    INSERT INTO transaction_statuses (tenant_id, status_name, description) VALUES (v_tenant_id, 'Hoàn Thành Test', 'Giao dịch đã hoàn tất') RETURNING transaction_status_id INTO v_status_id_completed;
    INSERT INTO transaction_statuses (tenant_id, status_name, description) VALUES (v_tenant_id, 'Đang Chờ Test', 'Giao dịch đang chờ xử lý') RETURNING transaction_status_id INTO v_status_id_pending;
    INSERT INTO transaction_statuses (tenant_id, status_name, description) VALUES (v_tenant_id, 'Đã Hủy Test', 'Giao dịch đã bị hủy') RETURNING transaction_status_id INTO v_status_id_cancelled;
    RAISE NOTICE '    Status Completed ID: %', v_status_id_completed;
    RAISE NOTICE '    Status Pending ID: %', v_status_id_pending;
    RAISE NOTICE '    Status Cancelled ID: %', v_status_id_cancelled;
    
    -- Inventory Adjustment Types (for completeness, not directly used in this sale flow but good for system setup)
    RAISE NOTICE '  Creating Inventory Adjustment Types...';
    INSERT INTO inventory_adjustment_types (tenant_id, type_name, description, action)
    VALUES (v_tenant_id, 'Nhập Kho Test', 'Điều chỉnh tăng do nhập hàng', 'INCREASE')
    RETURNING adjustment_type_id INTO v_adj_type_nhap;

    INSERT INTO inventory_adjustment_types (tenant_id, type_name, description, action)
    VALUES (v_tenant_id, 'Xuất Kho Test (Hủy hàng)', 'Điều chỉnh giảm do hủy hàng', 'DECREASE')
    RETURNING adjustment_type_id INTO v_adj_type_xuat;
    RAISE NOTICE '    Adjustment Type Nhập ID: %', v_adj_type_nhap;
    RAISE NOTICE '    Adjustment Type Xuất ID: %', v_adj_type_xuat;

    -- 2. IMPORT PROCESS
    RAISE NOTICE '---- 2. Starting Import Process ----';
    RAISE NOTICE '  Creating Import Invoice...';
    CALL sp_create_import_invoice(
        p_tenant_id         => v_tenant_id,
        p_supplier_id       => v_supplier_id,
        p_employee_id       => v_employee_id,
        p_invoice_number    => 'INV20250519-001-TEST',
        p_import_date       => CURRENT_DATE, -- Added missing parameter
        p_notes             => 'Phiếu nhập hàng test đầu tiên',
        p_new_import_invoice_id => v_import_invoice_id -- OUT parameter
    );
    RAISE NOTICE '    Import Invoice ID: %', v_import_invoice_id;
    IF v_import_invoice_id IS NULL THEN RAISE EXCEPTION 'Failed to create import invoice'; END IF;

    RAISE NOTICE '    Adding Amoxicillin to Import Invoice...';
    CALL sp_add_import_invoice_detail(
        p_tenant_id             => v_tenant_id,
        p_import_invoice_id     => v_import_invoice_id,
        p_medicine_id           => v_medicine_id_amox,
        p_packaging_unit_id     => v_packaging_id_amox_hop,     -- Nhập theo Hộp
        p_quantity_imported_in_unit => 5,                     -- 5 Hộp
        p_price_per_import_unit => 80000,                 -- 80,000 VND / Hộp
        p_new_import_invoice_detail_id => v_import_detail_id_amox -- OUT
    );
    RAISE NOTICE '      Amoxicillin Import Detail ID: % (5 Hộp @ 80,000 VND)', v_import_detail_id_amox;
    IF v_import_detail_id_amox IS NULL THEN RAISE EXCEPTION 'Failed to add Amoxicillin to import invoice'; END IF;

    RAISE NOTICE '    Adding Paracetamol to Import Invoice...';
    CALL sp_add_import_invoice_detail(
        p_tenant_id             => v_tenant_id,
        p_import_invoice_id     => v_import_invoice_id,
        p_medicine_id           => v_medicine_id_para,
        p_packaging_unit_id     => v_packaging_id_para_vi,      -- Nhập theo Vỉ
        p_quantity_imported_in_unit => 10,                    -- 10 Vỉ
        p_price_per_import_unit => 12000,                 -- 12,000 VND / Vỉ
        p_new_import_invoice_detail_id => v_import_detail_id_para -- OUT
    );
    RAISE NOTICE '      Paracetamol Import Detail ID: % (10 Vỉ @ 12,000 VND)', v_import_detail_id_para;
    IF v_import_detail_id_para IS NULL THEN RAISE EXCEPTION 'Failed to add Paracetamol to import invoice'; END IF;

    RAISE NOTICE '  Adding Batches from Import Details...';
    -- Batch for Amoxicillin (5 Hộp * 100 viên/hộp = 500 viên)
    RAISE NOTICE '    Adding Batch for Amoxicillin...';
    CALL sp_add_medicine_batch_from_import(
        p_tenant_id                     => v_tenant_id,
        p_import_invoice_detail_id      => v_import_detail_id_amox,
        p_batch_number                  => 'AMOXTEST001',
        p_expiry_date                   => (CURRENT_DATE + INTERVAL '2 year')::TIMESTAMP WITHOUT TIME ZONE,
        p_selling_price_per_base_unit   => 1000 -- 1,000 VND / viên (base unit)
    );
    -- Lấy batch_id vừa tạo (cần truy vấn lại vì procedure không trả OUT batch_id)
    SELECT b.batch_id INTO v_batch_id_amox FROM batches b 
    JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id 
    WHERE iid.import_invoice_detail_id = v_import_detail_id_amox LIMIT 1;
    RAISE NOTICE '      Batch ID for Amoxicillin import detail %: %', v_import_detail_id_amox, v_batch_id_amox;
    IF v_batch_id_amox IS NULL THEN RAISE EXCEPTION 'Failed to retrieve batch for Amoxicillin'; END IF;

    -- Batch for Paracetamol (20 Vỉ * 10 viên/vỉ = 200 viên)
    RAISE NOTICE '    Adding Batch for Paracetamol...';
    CALL sp_add_medicine_batch_from_import(
        p_tenant_id                     => v_tenant_id,
        p_import_invoice_detail_id      => v_import_detail_id_para,
        p_batch_number                  => 'PARATEST001',
        p_expiry_date                   => (CURRENT_DATE + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE,
        p_selling_price_per_base_unit   => 600 -- 600 VND / viên (base unit)
    );
    SELECT b.batch_id INTO v_batch_id_para FROM batches b 
    JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id 
    WHERE iid.import_invoice_detail_id = v_import_detail_id_para LIMIT 1;
    RAISE NOTICE '      Batch ID for Paracetamol import detail %: %', v_import_detail_id_para, v_batch_id_para;
    IF v_batch_id_para IS NULL THEN RAISE EXCEPTION 'Failed to retrieve batch for Paracetamol'; END IF;

    -- 3. CHECK INITIAL STOCK
    RAISE NOTICE '---- 3. Checking Initial Stock ----';
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '  Available Amoxicillin (base units): %', v_stock_amox; -- Expected: 500
    RAISE NOTICE '  Available Paracetamol (base units): %', v_stock_para; -- Expected: 200
    IF v_stock_amox <> 500 THEN RAISE WARNING 'Initial Amoxicillin stock is %, expected 500', v_stock_amox; END IF;
    IF v_stock_para <> 200 THEN RAISE WARNING 'Initial Paracetamol stock is %, expected 200', v_stock_para; END IF;

    -- 4. SALES PROCESS (SUCCESSFUL)
    RAISE NOTICE '---- 4. Processing Successful Sale ----';
    RAISE NOTICE '  Selling 50 Amoxicillin viên and 20 Paracetamol viên...';
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => v_customer_id,
        p_prescription_id       => NULL, -- Giả sử không có toa
        p_payment_method_id     => v_payment_method_id_cash,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 50),
            jsonb_build_object('medicine_id', v_medicine_id_para, 'quantity_requested_in_base_unit', 20)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes                 => 'Khách hàng mua lẻ, thanh toán tiền mặt.',
        p_new_transaction_id    => v_sale_transaction_id, -- OUT
        p_message               => v_sale_message       -- OUT
    );
    RAISE NOTICE '    Sale Transaction ID: %', v_sale_transaction_id;
    RAISE NOTICE '    Sale Message: %', v_sale_message;
    IF v_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'Successful sale failed: %', v_sale_message; END IF;

    RAISE NOTICE '  Verifying stock after successful sale...';
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '    Available Amoxicillin (base units): %', v_stock_amox; -- Expected: 500 - 50 = 450
    RAISE NOTICE '    Available Paracetamol (base units): %', v_stock_para; -- Expected: 200 - 20 = 180
    IF v_stock_amox <> 450 THEN RAISE WARNING 'Amoxicillin stock after sale is %, expected 450', v_stock_amox; END IF;
    IF v_stock_para <> 180 THEN RAISE WARNING 'Paracetamol stock after sale is %, expected 180', v_stock_para; END IF;

    -- 5. SALES PROCESS (ATTEMPT FAILED - INSUFFICIENT STOCK)
    RAISE NOTICE '---- 5. Attempting Sale with Insufficient Stock ----';
    RAISE NOTICE '  Attempting to sell 1000 Amoxicillin viên (available: %)...', v_stock_amox;
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => NULL, -- Walk-in customer
        p_prescription_id       => NULL,
        p_payment_method_id     => v_payment_method_id_card,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 1000)
        ),
        p_transaction_status_id => v_status_id_pending, -- Should not complete
        p_notes                 => 'Test bán quá số lượng tồn.',
        p_new_transaction_id    => v_sale_transaction_id, -- OUT
        p_message               => v_sale_message       -- OUT
    );
    RAISE NOTICE '    Sale Transaction ID (should be NULL or different): %', v_sale_transaction_id;
    RAISE NOTICE '    Sale Message (should indicate error): %', v_sale_message;
    IF v_sale_transaction_id IS NOT NULL AND v_sale_message NOT LIKE '%Không đủ hàng%' AND v_sale_message NOT LIKE '%Insufficient stock%' THEN 
        RAISE WARNING 'Sale with insufficient stock might have partially succeeded or error message is not as expected. Transaction ID: %, Message: %', v_sale_transaction_id, v_sale_message;
    ELSE
        RAISE NOTICE '    Sale attempt with insufficient stock handled as expected.';
    END IF;

    RAISE NOTICE '  Verifying stock after FAILED sale attempt (should be unchanged from last successful sale)...';
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    Available Amoxicillin (base units): %', v_stock_amox; -- Expected: 450 (unchanged)
    IF v_stock_amox <> 450 THEN RAISE WARNING 'Amoxicillin stock changed after FAILED sale attempt. Expected 450, got %', v_stock_amox; END IF;

    RAISE NOTICE '========== PHARMACY SALES FLOW TEST COMPLETED =========';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An unexpected error occurred during the test script: %', SQLERRM;
        RAISE NOTICE 'SQLSTATE: %', SQLSTATE;
        RAISE NOTICE '========== TEST FAILED DUE TO UNEXPECTED ERROR =========';
        RAISE; -- Re-raise the exception to make the test fail clearly
END $$;
