-- Add missing prescription-related stored procedures

-- Procedure: sp_create_prescription
-- Creates a new prescription record
CREATE OR REPLACE PROCEDURE sp_create_prescription(
    p_tenant_id UUID,
    p_customer_id UUID,
    p_doctor_name VARCHAR(255),
    p_clinic_address TEXT,
    p_diagnosis TEXT,
    p_notes TEXT,
    p_employee_id UUID,
    INOUT p_new_prescription_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate customer if provided
    IF p_customer_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM customers 
        WHERE customer_id = p_customer_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Customer ID not found.';
        RETURN;
    END IF;

    -- Validate employee if provided
    IF p_employee_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM employees 
        WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Employee ID not found.';
        RETURN;
    END IF;

    -- Insert new prescription
    INSERT INTO prescriptions (
        tenant_id,
        customer_id,
        doctor_name,
        clinic_address,
        diagnosis,
        notes,
        employee_id
    ) VALUES (
        p_tenant_id,
        p_customer_id,
        p_doctor_name,
        p_clinic_address,
        p_diagnosis,
        p_notes,
        p_employee_id
    ) RETURNING prescription_id INTO p_new_prescription_id;

    p_message := 'Prescription created successfully with ID ' || p_new_prescription_id::TEXT || '.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error creating prescription: ' || SQLERRM;
        p_new_prescription_id := NULL;
END;
$$;

-- Procedure: sp_add_prescription_detail
-- Adds a medicine to a prescription
CREATE OR REPLACE PROCEDURE sp_add_prescription_detail(
    p_tenant_id UUID,
    p_prescription_id UUID,
    p_medicine_id UUID,
    p_dosage TEXT,
    p_quantity_prescribed NUMERIC,
    p_notes TEXT,
    INOUT p_new_prescription_detail_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate prescription
    IF NOT EXISTS (
        SELECT 1 FROM prescriptions 
        WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Prescription ID not found.';
        RETURN;
    END IF;

    -- Validate medicine
    IF NOT EXISTS (
        SELECT 1 FROM medicines 
        WHERE medicine_id = p_medicine_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Medicine ID not found.';
        RETURN;
    END IF;

    -- Validate quantity
    IF p_quantity_prescribed <= 0 THEN
        p_message := 'Quantity prescribed must be greater than zero.';
        RETURN;
    END IF;

    -- Insert prescription detail
    INSERT INTO prescription_details (
        tenant_id,
        prescription_id,
        medicine_id,
        dosage,
        quantity_prescribed,
        notes
    ) VALUES (
        p_tenant_id,
        p_prescription_id,
        p_medicine_id,
        p_dosage,
        p_quantity_prescribed,
        p_notes
    ) RETURNING prescription_detail_id INTO p_new_prescription_detail_id;

    p_message := 'Prescription detail added successfully with ID ' || p_new_prescription_detail_id::TEXT || '.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error adding prescription detail: ' || SQLERRM;
        p_new_prescription_detail_id := NULL;
END;
$$;

-- Procedure: sp_update_prescription_dispensed_status
-- Updates the dispensed status of a prescription
CREATE OR REPLACE PROCEDURE sp_update_prescription_dispensed_status(
    p_tenant_id UUID,
    p_prescription_id UUID,
    p_is_dispensed BOOLEAN,
    INOUT p_success BOOLEAN DEFAULT FALSE,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Validate prescription
    IF NOT EXISTS (
        SELECT 1 FROM prescriptions 
        WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Prescription ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Update prescription dispensed status
    UPDATE prescriptions
    SET 
        is_dispensed = p_is_dispensed,
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        prescription_id = p_prescription_id AND 
        tenant_id = p_tenant_id;

    p_message := 'Prescription dispensed status updated successfully.';
    p_success := TRUE;
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error updating prescription dispensed status: ' || SQLERRM;
        p_success := FALSE;
END;
$$;
