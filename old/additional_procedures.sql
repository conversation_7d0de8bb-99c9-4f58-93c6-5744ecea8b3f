-- Add additional missing procedures needed for the test

-- Procedure: sp_process_sale_transaction_with_discount
-- Processes a sale transaction with a discount
CREATE OR REPLACE PROCEDURE sp_process_sale_transaction_with_discount(
    p_tenant_id UUID,
    p_employee_id UUID,
    p_customer_id UUID,
    p_prescription_id UUID,
    p_payment_method_id UUID,
    p_sale_items JSONB,
    p_transaction_status_id UUID,
    p_discount_amount NUMERIC,
    p_notes TEXT DEFAULT NULL,
    INOUT p_new_transaction_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_total_amount NUMERIC := 0;
    v_final_amount NUMERIC := 0;
    v_item JSONB;
    v_medicine_id UUID;
    v_quantity_requested NUMERIC;
    v_unit_price_override NUMERIC;
    v_batch_id UUID;
    v_detail_id UUID;
    v_subtotal NUMERIC;
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate employee
    IF NOT EXISTS (SELECT 1 FROM employees WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id) THEN
        p_message := 'Employee ID not found.';
        RETURN;
    END IF;

    -- Validate customer if provided
    IF p_customer_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM customers WHERE customer_id = p_customer_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Customer ID not found.';
        RETURN;
    END IF;

    -- Validate prescription if provided
    IF p_prescription_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM prescriptions WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Prescription ID not found.';
        RETURN;
    END IF;

    -- Validate payment method if provided
    IF p_payment_method_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM payment_methods WHERE payment_method_id = p_payment_method_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Payment method ID not found.';
        RETURN;
    END IF;

    -- Validate transaction status
    IF NOT EXISTS (
        SELECT 1 FROM transaction_statuses WHERE transaction_status_id = p_transaction_status_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Transaction status ID not found.';
        RETURN;
    END IF;

    -- Validate discount amount
    IF p_discount_amount < 0 THEN
        p_message := 'Discount amount cannot be negative.';
        RETURN;
    END IF;

    -- Create transaction record
    INSERT INTO sales_transactions (
        tenant_id,
        employee_id,
        customer_id,
        prescription_id,
        payment_method_id,
        transaction_status_id,
        total_amount,
        discount_amount,
        final_amount,
        notes
    ) VALUES (
        p_tenant_id,
        p_employee_id,
        p_customer_id,
        p_prescription_id,
        p_payment_method_id,
        p_transaction_status_id,
        0, -- Will update after adding details
        p_discount_amount,
        0, -- Will update after adding details
        p_notes
    ) RETURNING sale_transaction_id INTO p_new_transaction_id;

    -- Process each sale item
    FOR i IN 0..jsonb_array_length(p_sale_items) - 1 LOOP
        v_item := p_sale_items->i;
        v_medicine_id := (v_item->>'medicine_id')::UUID;
        v_quantity_requested := (v_item->>'quantity_requested_in_base_unit')::NUMERIC;
        v_unit_price_override := (v_item->>'unit_price_override')::NUMERIC;

        -- Validate medicine
        IF NOT EXISTS (SELECT 1 FROM medicines WHERE medicine_id = v_medicine_id AND tenant_id = p_tenant_id) THEN
            p_message := 'Medicine ID ' || v_medicine_id::TEXT || ' not found or not active.';
            -- Continue with other items instead of returning
            CONTINUE;
        END IF;

        -- Find best batch using FEFO (First Expiry, First Out)
        SELECT batch_id INTO v_batch_id
        FROM batches
        WHERE medicine_id = v_medicine_id
          AND tenant_id = p_tenant_id
          AND quantity_remaining >= v_quantity_requested
          AND expiry_date > CURRENT_DATE
        ORDER BY expiry_date ASC
        LIMIT 1;

        IF v_batch_id IS NULL THEN
            p_message := p_message || ' Insufficient stock or no suitable batches available for medicine ID ' || v_medicine_id::TEXT || '.';
            -- Continue with other items
            CONTINUE;
        END IF;

        -- Get unit price (either override or from batch)
        IF v_unit_price_override IS NOT NULL THEN
            v_subtotal := v_quantity_requested * v_unit_price_override;
        ELSE
            SELECT v_quantity_requested * selling_price_per_base_unit INTO v_subtotal
            FROM batches
            WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;
        END IF;

        -- Add transaction detail
        INSERT INTO sales_transaction_details (
            tenant_id,
            sale_transaction_id,
            medicine_id,
            batch_id,
            quantity_sold_in_base_unit,
            unit_price,
            subtotal
        ) VALUES (
            p_tenant_id,
            p_new_transaction_id,
            v_medicine_id,
            v_batch_id,
            v_quantity_requested,
            COALESCE(v_unit_price_override, (SELECT selling_price_per_base_unit FROM batches WHERE batch_id = v_batch_id)),
            v_subtotal
        ) RETURNING sale_transaction_detail_id INTO v_detail_id;

        -- Update batch quantity
        UPDATE batches
        SET quantity_remaining = quantity_remaining - v_quantity_requested
        WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;

        -- Add to total amount
        v_total_amount := v_total_amount + v_subtotal;
    END LOOP;

    -- Calculate final amount after discount
    v_final_amount := v_total_amount - p_discount_amount;
    IF v_final_amount < 0 THEN
        v_final_amount := 0;
    END IF;

    -- Update transaction with total and final amounts
    UPDATE sales_transactions
    SET
        total_amount = v_total_amount,
        final_amount = v_final_amount
    WHERE
        sale_transaction_id = p_new_transaction_id AND
        tenant_id = p_tenant_id;

    -- Mark prescription as dispensed if applicable
    IF p_prescription_id IS NOT NULL THEN
        UPDATE prescriptions
        SET is_dispensed = TRUE
        WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id;
    END IF;

    p_message := 'Sales transaction ' || p_new_transaction_id::TEXT || ' processed successfully.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error processing sale transaction: ' || SQLERRM;
        -- Attempt to rollback by deleting the transaction if it was created
        IF p_new_transaction_id IS NOT NULL THEN
            DELETE FROM sales_transactions
            WHERE sale_transaction_id = p_new_transaction_id AND tenant_id = p_tenant_id;
        END IF;
        p_new_transaction_id := NULL;
END;
$$;

-- Procedure: sp_create_loyalty_tier
-- Creates a new loyalty tier
CREATE OR REPLACE PROCEDURE sp_create_loyalty_tier(
    p_tenant_id UUID,
    p_tier_name VARCHAR(100),
    p_min_points_required NUMERIC,
    p_discount_percentage NUMERIC,
    p_other_benefits TEXT,
    INOUT p_new_loyalty_tier_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate tier name
    IF p_tier_name IS NULL OR p_tier_name = '' THEN
        p_message := 'Tier name cannot be empty.';
        RETURN;
    END IF;

    -- Validate min points
    IF p_min_points_required < 0 THEN
        p_message := 'Minimum points required cannot be negative.';
        RETURN;
    END IF;

    -- Validate discount percentage
    IF p_discount_percentage < 0 OR p_discount_percentage > 100 THEN
        p_message := 'Discount percentage must be between 0 and 100.';
        RETURN;
    END IF;

    -- Insert new loyalty tier
    INSERT INTO loyalty_tiers (
        tenant_id,
        tier_name,
        min_points_required,
        discount_percentage,
        other_benefits
    ) VALUES (
        p_tenant_id,
        p_tier_name,
        p_min_points_required,
        p_discount_percentage,
        p_other_benefits
    ) RETURNING loyalty_tier_id INTO p_new_loyalty_tier_id;

    p_message := 'Loyalty tier created successfully with ID ' || p_new_loyalty_tier_id::TEXT || '.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error creating loyalty tier: ' || SQLERRM;
        p_new_loyalty_tier_id := NULL;
END;
$$;

-- Procedure: sp_process_sale_transaction_with_loyalty
-- Processes a sale transaction with loyalty points
CREATE OR REPLACE PROCEDURE sp_process_sale_transaction_with_loyalty(
    p_tenant_id UUID,
    p_employee_id UUID,
    p_customer_id UUID,
    p_prescription_id UUID,
    p_payment_method_id UUID,
    p_sale_items JSONB,
    p_transaction_status_id UUID,
    p_points_rate NUMERIC,
    p_notes TEXT DEFAULT NULL,
    INOUT p_new_transaction_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_total_amount NUMERIC := 0;
    v_final_amount NUMERIC := 0;
    v_points_earned NUMERIC := 0;
    v_item JSONB;
    v_medicine_id UUID;
    v_quantity_requested NUMERIC;
    v_unit_price_override NUMERIC;
    v_batch_id UUID;
    v_detail_id UUID;
    v_subtotal NUMERIC;
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate employee
    IF NOT EXISTS (SELECT 1 FROM employees WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id) THEN
        p_message := 'Employee ID not found.';
        RETURN;
    END IF;

    -- Validate customer (required for loyalty)
    IF p_customer_id IS NULL THEN
        p_message := 'Customer ID is required for loyalty transactions.';
        RETURN;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM customers WHERE customer_id = p_customer_id AND tenant_id = p_tenant_id) THEN
        p_message := 'Customer ID not found.';
        RETURN;
    END IF;

    -- Validate prescription if provided
    IF p_prescription_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM prescriptions WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Prescription ID not found.';
        RETURN;
    END IF;

    -- Validate payment method if provided
    IF p_payment_method_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM payment_methods WHERE payment_method_id = p_payment_method_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Payment method ID not found.';
        RETURN;
    END IF;

    -- Validate transaction status
    IF NOT EXISTS (
        SELECT 1 FROM transaction_statuses WHERE transaction_status_id = p_transaction_status_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Transaction status ID not found.';
        RETURN;
    END IF;

    -- Validate points rate
    IF p_points_rate < 0 THEN
        p_message := 'Points rate cannot be negative.';
        RETURN;
    END IF;

    -- Create transaction record
    INSERT INTO sales_transactions (
        tenant_id,
        employee_id,
        customer_id,
        prescription_id,
        payment_method_id,
        transaction_status_id,
        total_amount,
        final_amount,
        notes
    ) VALUES (
        p_tenant_id,
        p_employee_id,
        p_customer_id,
        p_prescription_id,
        p_payment_method_id,
        p_transaction_status_id,
        0, -- Will update after adding details
        0, -- Will update after adding details
        p_notes
    ) RETURNING sale_transaction_id INTO p_new_transaction_id;

    -- Process each sale item
    FOR i IN 0..jsonb_array_length(p_sale_items) - 1 LOOP
        v_item := p_sale_items->i;
        v_medicine_id := (v_item->>'medicine_id')::UUID;
        v_quantity_requested := (v_item->>'quantity_requested_in_base_unit')::NUMERIC;
        v_unit_price_override := (v_item->>'unit_price_override')::NUMERIC;

        -- Validate medicine
        IF NOT EXISTS (SELECT 1 FROM medicines WHERE medicine_id = v_medicine_id AND tenant_id = p_tenant_id) THEN
            p_message := 'Medicine ID ' || v_medicine_id::TEXT || ' not found or not active.';
            -- Continue with other items instead of returning
            CONTINUE;
        END IF;

        -- Find best batch using FEFO (First Expiry, First Out)
        SELECT batch_id INTO v_batch_id
        FROM batches
        WHERE medicine_id = v_medicine_id
          AND tenant_id = p_tenant_id
          AND quantity_remaining >= v_quantity_requested
          AND expiry_date > CURRENT_DATE
        ORDER BY expiry_date ASC
        LIMIT 1;

        IF v_batch_id IS NULL THEN
            p_message := p_message || ' Insufficient stock or no suitable batches available for medicine ID ' || v_medicine_id::TEXT || '.';
            -- Continue with other items
            CONTINUE;
        END IF;

        -- Get unit price (either override or from batch)
        IF v_unit_price_override IS NOT NULL THEN
            v_subtotal := v_quantity_requested * v_unit_price_override;
        ELSE
            SELECT v_quantity_requested * selling_price_per_base_unit INTO v_subtotal
            FROM batches
            WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;
        END IF;

        -- Add transaction detail
        INSERT INTO sales_transaction_details (
            tenant_id,
            sale_transaction_id,
            medicine_id,
            batch_id,
            quantity_sold_in_base_unit,
            unit_price,
            subtotal
        ) VALUES (
            p_tenant_id,
            p_new_transaction_id,
            v_medicine_id,
            v_batch_id,
            v_quantity_requested,
            COALESCE(v_unit_price_override, (SELECT selling_price_per_base_unit FROM batches WHERE batch_id = v_batch_id)),
            v_subtotal
        ) RETURNING sale_transaction_detail_id INTO v_detail_id;

        -- Update batch quantity
        UPDATE batches
        SET quantity_remaining = quantity_remaining - v_quantity_requested
        WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;

        -- Add to total amount
        v_total_amount := v_total_amount + v_subtotal;
    END LOOP;

    -- Calculate points earned
    v_points_earned := ROUND(v_total_amount * p_points_rate, 2);

    -- Update transaction with total amount, final amount, and points earned
    UPDATE sales_transactions
    SET
        total_amount = v_total_amount,
        final_amount = v_total_amount,
        points_earned = v_points_earned
    WHERE
        sale_transaction_id = p_new_transaction_id AND
        tenant_id = p_tenant_id;

    -- Update customer loyalty points
    UPDATE customers
    SET loyalty_points = loyalty_points + v_points_earned
    WHERE customer_id = p_customer_id AND tenant_id = p_tenant_id;

    -- Mark prescription as dispensed if applicable
    IF p_prescription_id IS NOT NULL THEN
        UPDATE prescriptions
        SET is_dispensed = TRUE
        WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id;
    END IF;

    p_message := 'Sales transaction ' || p_new_transaction_id::TEXT || ' processed successfully.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error processing sale transaction: ' || SQLERRM;
        -- Attempt to rollback by deleting the transaction if it was created
        IF p_new_transaction_id IS NOT NULL THEN
            DELETE FROM sales_transactions
            WHERE sale_transaction_id = p_new_transaction_id AND tenant_id = p_tenant_id;
        END IF;
        p_new_transaction_id := NULL;
END;
$$;

-- Procedure: sp_cancel_sale_transaction
-- Cancels a sale transaction and restores inventory
CREATE OR REPLACE PROCEDURE sp_cancel_sale_transaction(
    p_tenant_id UUID,
    p_sale_transaction_id UUID,
    p_employee_id UUID,
    p_cancellation_reason TEXT,
    INOUT p_success BOOLEAN DEFAULT FALSE,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_detail RECORD;
    v_status_id_cancelled UUID;
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Validate transaction
    IF NOT EXISTS (
        SELECT 1 FROM sales_transactions
        WHERE sale_transaction_id = p_sale_transaction_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Transaction ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Validate employee
    IF NOT EXISTS (SELECT 1 FROM employees WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id) THEN
        p_message := 'Employee ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Get cancelled status ID
    SELECT transaction_status_id INTO v_status_id_cancelled
    FROM transaction_statuses
    WHERE status_name = 'Đã hủy' AND tenant_id = p_tenant_id;

    IF v_status_id_cancelled IS NULL THEN
        p_message := 'Cancelled status not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Restore inventory for each transaction detail
    FOR v_detail IN
        SELECT std.batch_id, std.quantity_sold_in_base_unit
        FROM sales_transaction_details std
        WHERE std.sale_transaction_id = p_sale_transaction_id AND std.tenant_id = p_tenant_id
    LOOP
        -- Update batch quantity
        UPDATE batches
        SET quantity_remaining = quantity_remaining + v_detail.quantity_sold_in_base_unit
        WHERE batch_id = v_detail.batch_id AND tenant_id = p_tenant_id;
    END LOOP;

    -- Update transaction status
    UPDATE sales_transactions
    SET
        transaction_status_id = v_status_id_cancelled,
        cancellation_reason = p_cancellation_reason,
        cancelled_by_employee_id = p_employee_id,
        cancelled_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE
        sale_transaction_id = p_sale_transaction_id AND
        tenant_id = p_tenant_id;

    p_message := 'Transaction cancelled successfully.';
    p_success := TRUE;
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error cancelling transaction: ' || SQLERRM;
        p_success := FALSE;
END;
$$;

-- Procedure: sp_update_transaction_status
-- Updates the status of a transaction
CREATE OR REPLACE PROCEDURE sp_update_transaction_status(
    p_tenant_id UUID,
    p_sale_transaction_id UUID,
    p_new_status_id UUID,
    p_employee_id UUID,
    p_notes TEXT DEFAULT NULL,
    INOUT p_success BOOLEAN DEFAULT FALSE,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Validate transaction
    IF NOT EXISTS (
        SELECT 1 FROM sales_transactions
        WHERE sale_transaction_id = p_sale_transaction_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Transaction ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Validate status
    IF NOT EXISTS (
        SELECT 1 FROM transaction_statuses
        WHERE transaction_status_id = p_new_status_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Status ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Validate employee
    IF NOT EXISTS (SELECT 1 FROM employees WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id) THEN
        p_message := 'Employee ID not found.';
        p_success := FALSE;
        RETURN;
    END IF;

    -- Update transaction status
    UPDATE sales_transactions
    SET
        transaction_status_id = p_new_status_id,
        updated_at = CURRENT_TIMESTAMP,
        notes = CASE
            WHEN p_notes IS NOT NULL THEN
                COALESCE(notes, '') || E'\n' || p_notes
            ELSE
                notes
            END
    WHERE
        sale_transaction_id = p_sale_transaction_id AND
        tenant_id = p_tenant_id;

    p_message := 'Transaction status updated successfully.';
    p_success := TRUE;
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error updating transaction status: ' || SQLERRM;
        p_success := FALSE;
END;
$$;

-- Procedure: sp_process_sale_transaction_with_batch_selection
-- Processes a sale transaction with specific batch selection
CREATE OR REPLACE PROCEDURE sp_process_sale_transaction_with_batch_selection(
    p_tenant_id UUID,
    p_employee_id UUID,
    p_customer_id UUID,
    p_prescription_id UUID,
    p_payment_method_id UUID,
    p_sale_items JSONB,
    p_transaction_status_id UUID,
    p_notes TEXT DEFAULT NULL,
    INOUT p_new_transaction_id UUID DEFAULT NULL,
    INOUT p_message TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_total_amount NUMERIC := 0;
    v_final_amount NUMERIC := 0;
    v_item JSONB;
    v_medicine_id UUID;
    v_batch_id UUID;
    v_quantity_requested NUMERIC;
    v_unit_price_override NUMERIC;
    v_detail_id UUID;
    v_subtotal NUMERIC;
    v_batch_quantity NUMERIC;
BEGIN
    -- Validate tenant
    IF NOT EXISTS (SELECT 1 FROM tenants WHERE tenant_id = p_tenant_id) THEN
        p_message := 'Tenant ID not found.';
        RETURN;
    END IF;

    -- Validate employee
    IF NOT EXISTS (SELECT 1 FROM employees WHERE employee_id = p_employee_id AND tenant_id = p_tenant_id) THEN
        p_message := 'Employee ID not found.';
        RETURN;
    END IF;

    -- Validate customer if provided
    IF p_customer_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM customers WHERE customer_id = p_customer_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Customer ID not found.';
        RETURN;
    END IF;

    -- Validate prescription if provided
    IF p_prescription_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM prescriptions WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Prescription ID not found.';
        RETURN;
    END IF;

    -- Validate payment method if provided
    IF p_payment_method_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM payment_methods WHERE payment_method_id = p_payment_method_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Payment method ID not found.';
        RETURN;
    END IF;

    -- Validate transaction status
    IF NOT EXISTS (
        SELECT 1 FROM transaction_statuses WHERE transaction_status_id = p_transaction_status_id AND tenant_id = p_tenant_id
    ) THEN
        p_message := 'Transaction status ID not found.';
        RETURN;
    END IF;

    -- Create transaction record
    INSERT INTO sales_transactions (
        tenant_id,
        employee_id,
        customer_id,
        prescription_id,
        payment_method_id,
        transaction_status_id,
        total_amount,
        final_amount,
        notes
    ) VALUES (
        p_tenant_id,
        p_employee_id,
        p_customer_id,
        p_prescription_id,
        p_payment_method_id,
        p_transaction_status_id,
        0, -- Will update after adding details
        0, -- Will update after adding details
        p_notes
    ) RETURNING sale_transaction_id INTO p_new_transaction_id;

    -- Process each sale item
    FOR i IN 0..jsonb_array_length(p_sale_items) - 1 LOOP
        v_item := p_sale_items->i;
        v_medicine_id := (v_item->>'medicine_id')::UUID;
        v_batch_id := (v_item->>'batch_id')::UUID;
        v_quantity_requested := (v_item->>'quantity_requested_in_base_unit')::NUMERIC;
        v_unit_price_override := (v_item->>'unit_price_override')::NUMERIC;

        -- Validate medicine
        IF NOT EXISTS (SELECT 1 FROM medicines WHERE medicine_id = v_medicine_id AND tenant_id = p_tenant_id) THEN
            p_message := 'Medicine ID ' || v_medicine_id::TEXT || ' not found or not active.';
            -- Continue with other items instead of returning
            CONTINUE;
        END IF;

        -- Validate batch
        IF NOT EXISTS (
            SELECT 1 FROM batches
            WHERE batch_id = v_batch_id
              AND medicine_id = v_medicine_id
              AND tenant_id = p_tenant_id
        ) THEN
            p_message := 'Batch ID ' || v_batch_id::TEXT || ' not found for medicine ID ' || v_medicine_id::TEXT || '.';
            -- Continue with other items
            CONTINUE;
        END IF;

        -- Check batch quantity
        SELECT quantity_remaining INTO v_batch_quantity
        FROM batches
        WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;

        IF v_batch_quantity < v_quantity_requested THEN
            p_message := 'Insufficient quantity in batch ID ' || v_batch_id::TEXT || '. Available: ' || v_batch_quantity::TEXT || ', Requested: ' || v_quantity_requested::TEXT || '.';
            -- Continue with other items
            CONTINUE;
        END IF;

        -- Get unit price (either override or from batch)
        IF v_unit_price_override IS NOT NULL THEN
            v_subtotal := v_quantity_requested * v_unit_price_override;
        ELSE
            SELECT v_quantity_requested * selling_price_per_base_unit INTO v_subtotal
            FROM batches
            WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;
        END IF;

        -- Add transaction detail
        INSERT INTO sales_transaction_details (
            tenant_id,
            sale_transaction_id,
            medicine_id,
            batch_id,
            quantity_sold_in_base_unit,
            unit_price,
            subtotal
        ) VALUES (
            p_tenant_id,
            p_new_transaction_id,
            v_medicine_id,
            v_batch_id,
            v_quantity_requested,
            COALESCE(v_unit_price_override, (SELECT selling_price_per_base_unit FROM batches WHERE batch_id = v_batch_id)),
            v_subtotal
        ) RETURNING sale_transaction_detail_id INTO v_detail_id;

        -- Update batch quantity
        UPDATE batches
        SET quantity_remaining = quantity_remaining - v_quantity_requested
        WHERE batch_id = v_batch_id AND tenant_id = p_tenant_id;

        -- Add to total amount
        v_total_amount := v_total_amount + v_subtotal;
    END LOOP;

    -- Update transaction with total and final amounts
    UPDATE sales_transactions
    SET
        total_amount = v_total_amount,
        final_amount = v_total_amount
    WHERE
        sale_transaction_id = p_new_transaction_id AND
        tenant_id = p_tenant_id;

    -- Mark prescription as dispensed if applicable
    IF p_prescription_id IS NOT NULL THEN
        UPDATE prescriptions
        SET is_dispensed = TRUE
        WHERE prescription_id = p_prescription_id AND tenant_id = p_tenant_id;
    END IF;

    p_message := 'Sales transaction ' || p_new_transaction_id::TEXT || ' processed successfully.';
EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error processing sale transaction: ' || SQLERRM;
        -- Attempt to rollback by deleting the transaction if it was created
        IF p_new_transaction_id IS NOT NULL THEN
            DELETE FROM sales_transactions
            WHERE sale_transaction_id = p_new_transaction_id AND tenant_id = p_tenant_id;
        END IF;
        p_new_transaction_id := NULL;
END;
$$;