-- <PERSON><PERSON><PERSON> bản test cho quy trình bán hàng Pharmacy Management
-- <PERSON><PERSON>a dữ liệu cũ để đảm bảo test chạy trên môi trường sạch (nếu cần và cẩn thận)
-- TRUNCATE TABLE sales_transaction_details, sales_transactions, inventory_adjustments, batches, import_invoice_details, import_invoices, medicines, categories, customers, employees, tenants CASCADE;

DO $$
DECLARE
    -- Tenant
    v_tenant_id UUID;
    v_tenant_name TEXT := 'Nhà Thuốc An Khang Test';

    -- Employee
    v_employee_id UUID;
    v_employee_name TEXT := 'Nguyễn Văn A (Dược Sĩ Test)';

    -- Categories
    v_category_id_khang_sinh UUID;
    v_category_id_giam_dau UUID;

    -- Medicines & Packaging Units
    v_medicine_id_amox UUID;
    v_medicine_name_amox TEXT := 'Amoxicillin 500mg Test';
    v_packaging_id_amox_hop UUID;

    v_medicine_id_para UUID;
    v_medicine_name_para TEXT := 'Paracetamol 250mg Test';
    v_packaging_id_para_vi UUID;

    -- Supplier
    v_supplier_id UUID;
    v_supplier_name TEXT := 'Công Ty Dược Hậu Giang Test';
    v_sp_message TEXT:='---'; -- General purpose message variable for SP calls

    -- Customer
    v_customer_id UUID;
    v_customer_name TEXT := 'Trần Thị B (Khách Test)';

    -- Payment Methods
    v_payment_method_id_cash UUID;
    v_payment_method_id_card UUID;

    -- Transaction Statuses
    v_status_id_completed UUID;
    v_status_id_pending UUID;
    v_status_id_cancelled UUID;

    -- Import Invoice & Details
    v_import_invoice_id UUID;
    v_import_detail_id_amox UUID;
    v_import_detail_id_para UUID;

    -- Batches
    v_batch_id_amox UUID;
    v_batch_id_para UUID;

    -- Sales Transaction (for SC1)
    v_sc1_sale_transaction_id UUID;
    v_sc1_sale_message TEXT;

    -- Stock Check
    v_stock_amox NUMERIC;
    v_stock_para NUMERIC;

    -- Inventory Adjustment Types (nếu cần cho các test khác, ở đây chưa dùng trực tiếp)
    v_adj_type_nhap UUID;
    v_adj_type_xuat UUID;

    -- New variables for SC2 (FEFO Test)
    v_medicine_id_fefo UUID;
    v_category_id_fefo UUID;
    v_packaging_id_fefo_box UUID;
    v_import_invoice_id_fefo UUID;
    v_import_detail_id_fefo_b1 UUID;
    v_import_detail_id_fefo_b2 UUID;
    -- No specific batch IDs needed to be stored as variables for FEFO test, they are handled by fn_find_best_batch_for_sale

    -- Sales Transaction for FEFO test (SC2)
    v_sc2_sale_transaction_id UUID;
    v_sc2_sale_message TEXT;

    -- Stock Check for FEFO
    v_stock_fefo NUMERIC;

    -- Variable for SC3 (Non-existent medicine test)
    v_non_existent_medicine_id UUID := uuid_generate_v4();

    -- -- Variable to hold messages from stored procedures
    -- v_sp_message TEXT;

    -- -- Variable to hold messages from FEFO test
    -- v_sp_message_fefo TEXT;
    v_transaction_total_amount NUMERIC;
    v_transaction_final_amount NUMERIC;
    v_calculated_details_subtotal NUMERIC;

BEGIN
    RAISE NOTICE '========== STARTING PHARMACY SALES FLOW TEST =========';

    -- 1. SETUP BASIC DATA
    RAISE NOTICE '---- 1. Setting up Basic Data ----';

    -- 1.1. Create Tenant
    RAISE NOTICE '        1.1. Creating Tenant...';
    CALL sp_create_tenant(
        p_tenant_name           => v_tenant_name,
        p_new_tenant_id         => v_tenant_id, -- OUT
        p_message               => v_sp_message -- OUT
    );
    RAISE NOTICE '            Tenant Message: %', v_sp_message;
    IF v_tenant_id IS NULL THEN RAISE EXCEPTION 'Failed to create tenant. %', v_sp_message; END IF;

    -- 1.1.A Create Supplier (NEW STEP)
    RAISE NOTICE '        1.1.A. Creating Supplier...';
    CALL sp_create_supplier(
        p_tenant_id        => v_tenant_id,
        p_supplier_name    => v_supplier_name,
        p_contact_person   => 'Người Đại Diện Test',
        p_email            => '<EMAIL>',
        p_phone_number     => '0987654321',
        p_address          => '123 Đường Test, Q. Test, TP. Test',
        p_new_supplier_id  => v_supplier_id, -- OUT
        p_message          => v_sp_message   -- OUT
    );
    RAISE NOTICE '            Supplier Message: %', v_sp_message;
    IF v_supplier_id IS NULL THEN RAISE EXCEPTION 'Failed to create supplier. %', v_sp_message; END IF;

    -- 1.2. Create Employee
    RAISE NOTICE '        1.2. Creating Employee (Pharmacist)...';
    CALL sp_create_employee(
        p_tenant_id => v_tenant_id,
        p_full_name => v_employee_name,
        p_email => '<EMAIL>',
        p_phone_number => '0907654321',
        p_is_active => TRUE,
        p_new_employee_id => v_employee_id, -- OUT
        p_message => v_sp_message -- OUT
    );
    RAISE NOTICE '            Employee Message: %', v_sp_message;
    IF v_employee_id IS NULL THEN RAISE EXCEPTION 'Failed to create employee. %', v_sp_message; END IF;

    -- Categories
    RAISE NOTICE '  Creating Categories...';
    CALL sp_create_category(v_tenant_id, 'Kháng Sinh Test', 'Nhóm thuốc kháng sinh', v_category_id_khang_sinh, v_sp_message);
    RAISE NOTICE '    Category Kháng Sinh ID: %', v_category_id_khang_sinh;
    IF v_category_id_khang_sinh IS NULL THEN RAISE EXCEPTION 'Failed to create category Kháng Sinh. %', v_sp_message; END IF;

    CALL sp_create_category(v_tenant_id, 'Giảm Đau Hạ Sốt Test', 'Nhóm thuốc giảm đau, hạ sốt', v_category_id_giam_dau, v_sp_message);
    RAISE NOTICE '    Category Giảm Đau ID: %', v_category_id_giam_dau;
    IF v_category_id_giam_dau IS NULL THEN RAISE EXCEPTION 'Failed to create category Giảm Đau. %', v_sp_message; END IF;

    -- Medicine 1: Amoxicillin
    RAISE NOTICE '  Adding Medicine: %', v_medicine_name_amox;
    CALL sp_create_medicine(
        p_tenant_id           => v_tenant_id,
        p_medicine_name       => v_medicine_name_amox,
        p_description         => 'Kháng sinh Amoxicillin 500mg (Test)',
        p_registration_number => 'AMXTEST001',
        p_category_id         => v_category_id_khang_sinh,
        p_base_unit           => 'Viên',
        p_new_medicine_id     => v_medicine_id_amox, -- OUT
        p_message             => v_sp_message        -- OUT
    );
    RAISE NOTICE '    Amoxicillin Medicine ID: %', v_medicine_id_amox;
    IF v_medicine_id_amox IS NULL THEN RAISE EXCEPTION 'Failed to create medicine %. %', v_medicine_name_amox, v_sp_message; END IF;

    RAISE NOTICE '    Adding Packaging Unit for Amoxicillin (Hộp 100 viên)...';
    CALL sp_add_medicine_packaging_unit(
        p_tenant_id         => v_tenant_id,
        p_medicine_id       => v_medicine_id_amox,
        p_unit_name         => 'Hộp 100 viên',
        p_quantity_per_unit => 100, -- 1 hộp = 100 viên (viên là base_unit)
        p_is_default_sale_unit => TRUE,
        p_is_default_import_unit => TRUE,
        p_new_packaging_unit_id => v_packaging_id_amox_hop, -- OUT
        p_message           => v_sp_message               -- OUT
    );
    RAISE NOTICE '      Amoxicillin Packaging (Hộp) ID: %', v_packaging_id_amox_hop;
    IF v_packaging_id_amox_hop IS NULL THEN RAISE EXCEPTION 'Failed to add packaging unit for %. %', v_medicine_name_amox, v_sp_message; END IF;

    -- Medicine 2: Paracetamol
    RAISE NOTICE '  Adding Medicine: %', v_medicine_name_para;
    CALL sp_create_medicine(
        p_tenant_id           => v_tenant_id,
        p_medicine_name       => v_medicine_name_para,
        p_description         => 'Giảm đau, hạ sốt',
        p_registration_number => 'PARATEST001',
        p_category_id         => v_category_id_giam_dau,
        p_base_unit           => 'Viên',
        p_new_medicine_id     => v_medicine_id_para, -- OUT
        p_message             => v_sp_message        -- OUT
    );
    RAISE NOTICE '    Paracetamol Medicine ID: %', v_medicine_id_para;
    IF v_medicine_id_para IS NULL THEN RAISE EXCEPTION 'Failed to create medicine %. %', v_medicine_name_para, v_sp_message; END IF;

    RAISE NOTICE '    Adding Packaging Unit for Paracetamol (Vỉ 10 viên)...';
    CALL sp_add_medicine_packaging_unit(
        p_tenant_id         => v_tenant_id,
        p_medicine_id       => v_medicine_id_para,
        p_unit_name         => 'Vỉ',
        p_quantity_per_unit => 10, -- 1 vỉ = 10 viên (viên là base_unit)
        p_is_default_sale_unit => TRUE,
        p_is_default_import_unit => TRUE,
        p_new_packaging_unit_id => v_packaging_id_para_vi, -- OUT
        p_message           => v_sp_message               -- OUT
    );
    RAISE NOTICE '      Paracetamol Packaging (Vỉ) ID: %', v_packaging_id_para_vi;
    IF v_packaging_id_para_vi IS NULL THEN RAISE EXCEPTION 'Failed to add packaging unit for %. %', v_medicine_name_para, v_sp_message; END IF;

    -- Customer
    RAISE NOTICE '  Creating Customer: %', v_customer_name;
    CALL sp_create_customer(
        p_tenant_id => v_tenant_id,
        p_full_name => v_customer_name,
        p_phone_number => '0987654321',
        p_email => '<EMAIL>',
        p_address => '456 Đường Khách, P.Test, Q.Test',
        p_new_customer_id => v_customer_id, -- OUT
        p_message => v_sp_message -- OUT
    );
    RAISE NOTICE '    Customer ID: %', v_customer_id;
    IF v_customer_id IS NULL THEN RAISE EXCEPTION 'Failed to create customer. %', v_sp_message; END IF;

    -- Payment Methods
    RAISE NOTICE '  Creating Payment Methods...';
    CALL sp_create_payment_method(v_tenant_id, 'Tiền Mặt Test', 'Thanh toán bằng tiền mặt', TRUE, v_payment_method_id_cash, v_sp_message);
    RAISE NOTICE '    Payment Method Cash ID: %', v_payment_method_id_cash;
    IF v_payment_method_id_cash IS NULL THEN RAISE EXCEPTION 'Failed to create Cash payment method. %', v_sp_message; END IF;

    CALL sp_create_payment_method(v_tenant_id, 'Thẻ Test', 'Thanh toán bằng thẻ tín dụng/ghi nợ', TRUE, v_payment_method_id_card, v_sp_message);
    RAISE NOTICE '    Payment Method Card ID: %', v_payment_method_id_card;
    IF v_payment_method_id_card IS NULL THEN RAISE EXCEPTION 'Failed to create Card payment method. %', v_sp_message; END IF;

    -- Transaction Statuses
    RAISE NOTICE '  Creating Transaction Statuses...';
    CALL sp_create_transaction_status(
        p_tenant_id => v_tenant_id,
        p_status_name => 'Hoàn thành',
        p_description => 'Giao dịch đã hoàn tất thành công.',
        p_new_transaction_status_id => v_status_id_completed,
        p_message => v_sp_message
    );
    RAISE NOTICE '    Status Completed ID: %', v_status_id_completed;
    IF v_status_id_completed IS NULL THEN RAISE EXCEPTION 'Failed to create Completed transaction status. %', v_sp_message; END IF;

    CALL sp_create_transaction_status(
        p_tenant_id => v_tenant_id,
        p_status_name => 'Đang chờ xử lý',
        p_description => 'Giao dịch đang chờ xử lý thêm.',
        p_new_transaction_status_id => v_status_id_pending,
        p_message => v_sp_message
    );
    RAISE NOTICE '    Status Pending ID: %', v_status_id_pending;
    IF v_status_id_pending IS NULL THEN RAISE EXCEPTION 'Failed to create Pending transaction status. %', v_sp_message; END IF;

    CALL sp_create_transaction_status(
        p_tenant_id => v_tenant_id,
        p_status_name => 'Đã hủy',
        p_description => 'Giao dịch đã bị hủy.',
        p_new_transaction_status_id => v_status_id_cancelled,
        p_message => v_sp_message
    );
    RAISE NOTICE '    Status Cancelled ID: %', v_status_id_cancelled;
    IF v_status_id_cancelled IS NULL THEN RAISE EXCEPTION 'Failed to create Cancelled transaction status. %', v_sp_message; END IF;

    -- Inventory Adjustment Types (for completeness, not directly used in this sale flow but good for system setup)
    RAISE NOTICE '  Creating Inventory Adjustment Types...';
    CALL sp_create_inventory_adjustment_type(
        p_tenant_id => v_tenant_id,
        p_type_name => 'Nhập Kho Test',
        p_description => 'Điều chỉnh tăng do nhập hàng',
        p_action => 'INCREASE',
        p_new_adjustment_type_id => v_adj_type_nhap,
        p_message => v_sp_message
    );
    RAISE NOTICE '    Adjustment Type Nhập ID: %', v_adj_type_nhap;
    IF v_adj_type_nhap IS NULL THEN RAISE EXCEPTION 'Failed to create Adjustment Type Nhập. %', v_sp_message; END IF;

    CALL sp_create_inventory_adjustment_type(
        p_tenant_id => v_tenant_id,
        p_type_name => 'Xuất Kho Test (Hủy hàng)',
        p_description => 'Điều chỉnh giảm do hủy hàng',
        p_action => 'DECREASE',
        p_new_adjustment_type_id => v_adj_type_xuat,
        p_message => v_sp_message
    );
    RAISE NOTICE '    Adjustment Type Xuất ID: %', v_adj_type_xuat;
    IF v_adj_type_xuat IS NULL THEN RAISE EXCEPTION 'Failed to create Adjustment Type Xuất. %', v_sp_message; END IF;

    -- 2. IMPORT PROCESS
    RAISE NOTICE '---- 2. Starting Import Process ----';
    RAISE NOTICE '  Creating Import Invoice...';
    CALL sp_create_import_invoice(
        p_tenant_id         => v_tenant_id,
        p_supplier_id       => v_supplier_id,
        p_employee_id       => v_employee_id,
        p_invoice_number    => 'INV20250519-001-TEST',
        p_import_date       => CURRENT_DATE,
        p_new_import_invoice_id => v_import_invoice_id, -- OUT
        p_message           => v_sp_message, -- OUT
        p_notes             => 'Phiếu nhập hàng test đầu tiên'
    );
    RAISE NOTICE '    Import Invoice ID: %', v_import_invoice_id;
    IF v_import_invoice_id IS NULL THEN RAISE EXCEPTION 'Failed to create import invoice'; END IF;

    RAISE NOTICE '    Adding Amoxicillin to Import Invoice...';
    CALL sp_add_import_invoice_detail(
        p_tenant_id             => v_tenant_id,
        p_import_invoice_id     => v_import_invoice_id,
        p_medicine_id           => v_medicine_id_amox,
        p_packaging_unit_id     => v_packaging_id_amox_hop,     -- Nhập theo Hộp
        p_quantity_imported_in_unit => 5,                     -- 5 Hộp
        p_price_per_import_unit => 80000,                 -- 80,000 VND / Hộp
        p_new_import_invoice_detail_id => v_import_detail_id_amox, -- OUT
        p_message               => v_sp_message -- OUT
    );
    RAISE NOTICE '      Amoxicillin Import Detail ID: % (5 Hộp @ 80,000 VND)', v_import_detail_id_amox;
    IF v_import_detail_id_amox IS NULL THEN RAISE EXCEPTION 'Failed to add Amoxicillin to import invoice'; END IF;

    RAISE NOTICE '    Adding Paracetamol to Import Invoice...';
    CALL sp_add_import_invoice_detail(
        p_tenant_id             => v_tenant_id,
        p_import_invoice_id     => v_import_invoice_id,
        p_medicine_id           => v_medicine_id_para,
        p_packaging_unit_id     => v_packaging_id_para_vi,      -- Nhập theo Vỉ
        p_quantity_imported_in_unit => 10,                    -- 10 Vỉ
        p_price_per_import_unit => 12000,                 -- 12,000 VND / Vỉ
        p_new_import_invoice_detail_id => v_import_detail_id_para, -- OUT
        p_message               => v_sp_message -- OUT
    );
    RAISE NOTICE '      Paracetamol Import Detail ID: % (10 Vỉ @ 12,000 VND)', v_import_detail_id_para;
    IF v_import_detail_id_para IS NULL THEN RAISE EXCEPTION 'Failed to add Paracetamol to import invoice'; END IF;

    RAISE NOTICE '  Adding Batches from Import Details...';
    -- Batch for Amoxicillin (5 Hộp * 100 viên/hộp = 500 viên)
    RAISE NOTICE '    Adding Batch for Amoxicillin...';
    CALL sp_add_medicine_batch_from_import_alt(
        p_tenant_id                     => v_tenant_id,
        p_import_invoice_detail_id      => v_import_detail_id_amox,
        p_batch_number                  => 'AMOXTEST001'::VARCHAR,
        p_expiry_date                   => (CURRENT_DATE + INTERVAL '2 year')::TIMESTAMP WITHOUT TIME ZONE,
        p_selling_price_per_base_unit   => 1000::INTEGER, -- 1,000 VND / viên (base unit)
        p_message                       => v_sp_message
    );
    -- Lấy batch_id vừa tạo (cần truy vấn lại vì procedure không trả OUT batch_id)
    SELECT b.batch_id INTO v_batch_id_amox FROM batches b
    JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id
    WHERE iid.import_invoice_detail_id = v_import_detail_id_amox LIMIT 1;
    RAISE NOTICE '      Batch ID for Amoxicillin import detail %: %', v_import_detail_id_amox, v_batch_id_amox;
    IF v_batch_id_amox IS NULL THEN RAISE EXCEPTION 'Failed to retrieve batch for Amoxicillin'; END IF;

    -- Batch for Paracetamol (20 Vỉ * 10 viên/vỉ = 200 viên)
    RAISE NOTICE '    Adding Batch for Paracetamol...';
    CALL sp_add_medicine_batch_from_import_alt(
        p_tenant_id                     => v_tenant_id,
        p_import_invoice_detail_id      => v_import_detail_id_para,
        p_batch_number                  => 'PARATEST001'::VARCHAR,
        p_expiry_date                   => (CURRENT_DATE + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE,
        p_selling_price_per_base_unit   => 600::INTEGER, -- 600 VND / viên (base unit)
        p_message                       => v_sp_message
    );
    SELECT b.batch_id INTO v_batch_id_para FROM batches b
    JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id
    WHERE iid.import_invoice_detail_id = v_import_detail_id_para LIMIT 1;
    RAISE NOTICE '      Batch ID for Paracetamol import detail %: %', v_import_detail_id_para, v_batch_id_para;
    IF v_batch_id_para IS NULL THEN RAISE EXCEPTION 'Failed to retrieve batch for Paracetamol'; END IF;

    -- 3. CHECK INITIAL STOCK
    RAISE NOTICE '---- 3. Checking Initial Stock ----';
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '  Available Amoxicillin (base units): %', v_stock_amox; -- Expected: 500
    RAISE NOTICE '  Available Paracetamol (base units): %', v_stock_para; -- Expected: 200
    IF v_stock_amox <> 500 THEN RAISE WARNING 'Initial Amoxicillin stock is %, expected 500', v_stock_amox; END IF;
    IF v_stock_para <> 200 THEN RAISE WARNING 'Initial Paracetamol stock is %, expected 200', v_stock_para; END IF;

    -- 4. SALES PROCESS (SUCCESSFUL)
    RAISE NOTICE '---- 4. Processing Successful Sale ----';
    RAISE NOTICE '  Selling 50 Amoxicillin viên and 20 Paracetamol viên...';
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => v_customer_id,
        p_prescription_id       => NULL, -- Giả sử không có toa
        p_payment_method_id     => v_payment_method_id_cash,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 50),
            jsonb_build_object('medicine_id', v_medicine_id_para, 'quantity_requested_in_base_unit', 20)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes                 => 'Khách hàng mua lẻ, thanh toán tiền mặt.',
        p_new_transaction_id    => v_sc1_sale_transaction_id, -- OUT
        p_message               => v_sc1_sale_message       -- OUT
    );
    RAISE NOTICE '    Sale Transaction ID: %', v_sc1_sale_transaction_id;
    RAISE NOTICE '    Sale Message: %', v_sc1_sale_message;
    IF v_sc1_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'Successful sale failed: %', v_sc1_sale_message; END IF;

    RAISE NOTICE '  Verifying stock after successful sale...';
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '    Available Amoxicillin (base units): %', v_stock_amox; -- Expected: 500 - 50 = 450
    RAISE NOTICE '    Available Paracetamol (base units): %', v_stock_para; -- Expected: 200 - 20 = 180
    IF v_stock_amox <> 450 THEN RAISE WARNING 'Amoxicillin stock after sale is %, expected 450', v_stock_amox; END IF;
    IF v_stock_para <> 180 THEN RAISE WARNING 'Paracetamol stock after sale is %, expected 180', v_stock_para; END IF;

    -- SC6: Verify transaction amounts for the original successful sale (Section 4)
    RAISE NOTICE '    Verifying transaction amounts for transaction ID % (Original Sale)...', v_sc1_sale_transaction_id;
    SELECT total_amount, final_amount INTO v_transaction_total_amount, v_transaction_final_amount
    FROM sales_transactions
    WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;

    SELECT SUM(subtotal) INTO v_calculated_details_subtotal
    FROM sales_transaction_details
    WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;

    RAISE NOTICE '      Transaction Total Amount: %, Calculated Details Subtotal: %', v_transaction_total_amount, v_calculated_details_subtotal;
    RAISE NOTICE '      Transaction Final Amount: %', v_transaction_final_amount;

    IF v_transaction_total_amount <> v_calculated_details_subtotal THEN
        RAISE WARNING 'Original Sale: Transaction total_amount % does not match sum of details subtotal % for transaction %', v_transaction_total_amount, v_calculated_details_subtotal, v_sc1_sale_transaction_id;
    END IF;
    IF v_transaction_final_amount <> v_transaction_total_amount THEN -- Assuming discount_applied is 0
        RAISE WARNING 'Original Sale: Transaction final_amount % does not match total_amount % for transaction %', v_transaction_final_amount, v_transaction_total_amount, v_sc1_sale_transaction_id;
    END IF;

    -- 5. SALES PROCESS (ATTEMPT FAILED - INSUFFICIENT STOCK)
    RAISE NOTICE '---- 5. Attempting Sale with Insufficient Stock ----';
    RAISE NOTICE '  Attempting to sell 1000 Amoxicillin viên (available: %)...', v_stock_amox;
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => NULL, -- Walk-in customer
        p_prescription_id       => NULL,
        p_payment_method_id     => v_payment_method_id_card,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 1000)
        ),
        p_transaction_status_id => v_status_id_pending, -- Should not complete
        p_notes                 => 'Test bán quá số lượng tồn.',
        p_new_transaction_id    => v_sc1_sale_transaction_id, -- OUT
        p_message               => v_sc1_sale_message       -- OUT
    );
    RAISE NOTICE '    Sale Transaction ID (should be NULL or different): %', v_sc1_sale_transaction_id;
    RAISE NOTICE '    Sale Message (should indicate error): %', v_sc1_sale_message;
    IF v_sc1_sale_transaction_id IS NOT NULL AND v_sc1_sale_message NOT LIKE '%Không đủ hàng%' AND v_sc1_sale_message NOT LIKE '%Insufficient stock%' THEN
        RAISE WARNING 'Sale with insufficient stock might have partially succeeded or error message is not as expected. Transaction ID: %, Message: %', v_sc1_sale_transaction_id, v_sc1_sale_message;
    ELSE
        RAISE NOTICE '    Sale attempt with insufficient stock handled as expected.';
    END IF;

    RAISE NOTICE '  Verifying stock after FAILED sale attempt (should be unchanged from last successful sale)...';
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    Available Amoxicillin (base units): %', v_stock_amox; -- Expected: 450 (unchanged)
    IF v_stock_amox <> 450 THEN RAISE WARNING 'Amoxicillin stock changed after FAILED sale attempt. Expected 450, got %', v_stock_amox; END IF;

    -- ---- 6. SC1: Sales Process with Unit Price Override ----
    RAISE NOTICE '---- 6. SC1: Sales Process with Unit Price Override ----';
    RAISE NOTICE '  Selling 10 Amoxicillin viên with price override (700 VND/viên)...';
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => v_customer_id,
        p_prescription_id       => NULL,
        p_payment_method_id     => v_payment_method_id_card,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 10, 'unit_price_override', 700)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes                 => 'SC1: Test price override.',
        p_new_transaction_id    => v_sc1_sale_transaction_id, -- OUT
        p_message               => v_sc1_sale_message       -- OUT
    );
    RAISE NOTICE '    SC1: Sale Transaction ID: %', v_sc1_sale_transaction_id;
    RAISE NOTICE '    SC1: Sale Message: %', v_sc1_sale_message;
    IF v_sc1_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC1: Sale with price override failed: %', v_sc1_sale_message; END IF;

    RAISE NOTICE '    SC1: Verifying transaction details and stock...';
    DECLARE
        v_detail_unit_price NUMERIC;
        v_detail_subtotal NUMERIC;
    BEGIN
        SELECT unit_price, subtotal INTO v_detail_unit_price, v_detail_subtotal
        FROM sales_transaction_details
        WHERE sale_transaction_id = v_sc1_sale_transaction_id AND medicine_id = v_medicine_id_amox AND tenant_id = v_tenant_id;
        RAISE NOTICE '      SC1: Detail Unit Price: %, Expected: 700', v_detail_unit_price;
        RAISE NOTICE '      SC1: Detail Subtotal: %, Expected: 7000 (10*700)', v_detail_subtotal;
        IF v_detail_unit_price <> 700 THEN RAISE WARNING 'SC1: Unit price in details is %, expected 700', v_detail_unit_price; END IF;
        IF v_detail_subtotal <> 7000 THEN RAISE WARNING 'SC1: Subtotal in details is %, expected 7000', v_detail_subtotal; END IF;
    END;

    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    SC1: Available Amoxicillin (base units) after override sale: %', v_stock_amox; -- Expected: 450 - 10 = 440
    IF v_stock_amox <> 440 THEN RAISE WARNING 'SC1: Amoxicillin stock after override sale is %, expected 440', v_stock_amox; END IF;

    -- SC6: Verify transaction amounts for SC1
    RAISE NOTICE '    SC1: Verifying transaction amounts for transaction ID % ...', v_sc1_sale_transaction_id;
    SELECT total_amount, final_amount INTO v_transaction_total_amount, v_transaction_final_amount
    FROM sales_transactions WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
    SELECT SUM(subtotal) INTO v_calculated_details_subtotal
    FROM sales_transaction_details WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '      SC1: Transaction Total Amount: %, Calculated Details Subtotal: %', v_transaction_total_amount, v_calculated_details_subtotal;
    RAISE NOTICE '      SC1: Transaction Final Amount: %', v_transaction_final_amount;
    IF v_transaction_total_amount <> v_calculated_details_subtotal THEN RAISE WARNING 'SC1: Transaction total_amount % does not match sum of details subtotal %', v_transaction_total_amount, v_calculated_details_subtotal; END IF;
    IF v_transaction_final_amount <> v_transaction_total_amount THEN RAISE WARNING 'SC1: Transaction final_amount % does not match total_amount %', v_transaction_final_amount, v_transaction_total_amount; END IF;

    -- ---- 7. SC2: Sales Process Consuming Multiple Batches (FEFO) ----
    RAISE NOTICE '---- 7. SC2: Sales Process Consuming Multiple Batches (FEFO) ----';
    RAISE NOTICE '  SC2: Setting up new medicine (MED_FEFO) and batches...';
    -- Create Category for FEFO medicine
    CALL sp_create_category(
        p_tenant_id => v_tenant_id,
        p_category_name => 'FEFO Category',
        p_description => 'Category for FEFO testing',
        p_new_category_id => v_category_id_fefo,
        p_message => v_sp_message
    );
    RAISE NOTICE '      SC2: Create Category Message: %', v_sp_message;
    IF v_category_id_fefo IS NULL THEN
        RAISE EXCEPTION 'SC2: Failed to create category for FEFO test. Message: %', v_sp_message;
    END IF;

    -- Create Medicine for FEFO test (MED_FEFO)
    CALL sp_create_medicine(
        p_tenant_id       => v_tenant_id,
        p_medicine_name   => 'FEFOMED'::VARCHAR,
        p_description     => 'FEFO Test Medicine'::TEXT,
        p_registration_number => 'FTM001'::VARCHAR,
        p_category_id     => v_category_id_fefo,
        -- p_supplier_id     => v_supplier_id, -- Already removed
        -- p_brand_id        => NULL::UUID, -- Already removed
        p_base_unit       => 'Base unit is tablet'::VARCHAR,
        p_new_medicine_id => v_medicine_id_fefo,       -- OUT
        p_message         => v_sp_message                -- OUT
    );
    RAISE NOTICE '      SC2: Create Medicine Message: %', v_sp_message;
    IF v_medicine_id_fefo IS NULL THEN
        RAISE EXCEPTION 'SC2: Failed to create medicine for FEFO test. Message: %', v_sp_message;
    END IF;

    -- Add Packaging Unit for MED_FEFO
    CALL sp_add_medicine_packaging_unit(
        p_tenant_id => v_tenant_id,
        p_medicine_id => v_medicine_id_fefo,
        p_unit_name => 'Hộp 50 viên FEFO',
        p_quantity_per_unit => 50,
        p_is_default_sale_unit => TRUE,
        p_is_default_import_unit => TRUE,
        p_new_packaging_unit_id => v_packaging_id_fefo_box,
        p_message => v_sp_message
    );
    RAISE NOTICE '        SC2: Add Packaging Unit Message: %', v_sp_message;
    IF v_packaging_id_fefo_box IS NULL THEN RAISE EXCEPTION 'SC2: Failed to add packaging unit for FEFO medicine. Message: %', v_sp_message; END IF;

    -- Import MED_FEFO - Batch 1 (expires sooner)
    RAISE NOTICE '        SC2: Importing MED_FEFO Batch 1 (expires sooner)...';
    -- Create Import Invoice for FEFO Batch 1
    CALL sp_create_import_invoice(
        p_tenant_id => v_tenant_id,
        p_supplier_id => v_supplier_id,
        p_employee_id => v_employee_id,
        p_invoice_number => 'HDN_FEFO_B1',
        p_import_date => (CURRENT_DATE - INTERVAL '5 days')::DATE,
        p_new_import_invoice_id => v_import_invoice_id_fefo,
        p_message => v_sp_message,
        p_notes => 'FEFO Batch 1 Import'
    );
    IF v_import_invoice_id_fefo IS NULL THEN RAISE EXCEPTION 'SC2: Failed to create import invoice for FEFO Batch 1. Message: %', v_sp_message; END IF;
    RAISE NOTICE '            SC2: Import Invoice (FEFO B1) Message: %', v_sp_message;

    CALL sp_add_import_invoice_detail(
        p_tenant_id => v_tenant_id,
        p_import_invoice_id => v_import_invoice_id_fefo,
        p_medicine_id => v_medicine_id_fefo,
        p_packaging_unit_id => v_packaging_id_fefo_box,
        p_quantity_imported_in_unit => 10,
        p_price_per_import_unit => 50000,
        p_new_import_invoice_detail_id => v_import_detail_id_fefo_b1,
        p_message => v_sp_message
    );
    IF v_import_detail_id_fefo_b1 IS NULL THEN RAISE EXCEPTION 'SC2: Failed to add import detail for FEFO Batch 1. Message: %', v_sp_message; END IF;
    RAISE NOTICE '            SC2: Import Detail (FEFO B1) Message: %', v_sp_message;

    CALL sp_add_medicine_batch_from_import_alt(
        p_tenant_id => v_tenant_id,
        p_import_invoice_detail_id => v_import_detail_id_fefo_b1,
        p_batch_number => 'FEFO_B1'::VARCHAR,
        p_expiry_date => (CURRENT_DATE + INTERVAL '6 months')::TIMESTAMP WITHOUT TIME ZONE,
        p_selling_price_per_base_unit => 1200::INTEGER,
        p_message => v_sp_message
    );
    RAISE NOTICE '            SC2: Add Batch (FEFO B1) Message: %', v_sp_message;
    IF v_sp_message NOT LIKE 'Batch created successfully%' THEN RAISE EXCEPTION 'SC2: Failed to add FEFO Batch 1. Message: %', v_sp_message; END IF;

    -- Import MED_FEFO - Batch 2 (expires later)
    RAISE NOTICE '        SC2: Importing MED_FEFO Batch 2 (expires later)...';
    -- Create new Import Invoice for FEFO Batch 2 or reuse v_import_invoice_id_fefo if applicable. For simplicity, creating a new one.
    CALL sp_create_import_invoice(
        p_tenant_id => v_tenant_id,
        p_supplier_id => v_supplier_id,
        p_employee_id => v_employee_id,
        p_invoice_number => 'HDN_FEFO_B2',
        p_import_date => (CURRENT_DATE - INTERVAL '4 days')::DATE,
        p_new_import_invoice_id => v_import_invoice_id_fefo,
        p_message => v_sp_message,
        p_notes => 'FEFO Batch 2 Import'
    ); -- Reusing v_import_invoice_id_fefo var for simplicity, new invoice record
    IF v_import_invoice_id_fefo IS NULL THEN RAISE EXCEPTION 'SC2: Failed to create import invoice for FEFO Batch 2. Message: %', v_sp_message; END IF;
    RAISE NOTICE '            SC2: Import Invoice (FEFO B2) Message: %', v_sp_message;

    CALL sp_add_import_invoice_detail(
        p_tenant_id => v_tenant_id,
        p_import_invoice_id => v_import_invoice_id_fefo,
        p_medicine_id => v_medicine_id_fefo,
        p_packaging_unit_id => v_packaging_id_fefo_box,
        p_quantity_imported_in_unit => 15,
        p_price_per_import_unit => 52000,
        p_new_import_invoice_detail_id => v_import_detail_id_fefo_b2,
        p_message => v_sp_message
    );
    IF v_import_detail_id_fefo_b2 IS NULL THEN RAISE EXCEPTION 'SC2: Failed to add import detail for FEFO Batch 2. Message: %', v_sp_message; END IF;
    RAISE NOTICE '            SC2: Import Detail (FEFO B2) Message: %', v_sp_message;

    CALL sp_add_medicine_batch_from_import_alt(
        p_tenant_id => v_tenant_id,
        p_import_invoice_detail_id => v_import_detail_id_fefo_b2,
        p_batch_number => 'FEFO_B2'::VARCHAR,
        p_expiry_date => (CURRENT_DATE + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE,
        p_selling_price_per_base_unit => 1250::INTEGER,
        p_message => v_sp_message
    );
    RAISE NOTICE '            SC2: Add Batch (FEFO B2) Message: %', v_sp_message;
    IF v_sp_message NOT LIKE 'Batch created successfully%' THEN RAISE EXCEPTION 'SC2: Failed to add FEFO Batch 2. Message: %', v_sp_message; END IF;

    RAISE NOTICE '    SC2: Finished setting up MED_FEFO and batches.';

    RAISE NOTICE '  SC2: Selling 15 units of FEFO Medicine...';
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => NULL,
        p_prescription_id       => NULL,
        p_payment_method_id     => v_payment_method_id_cash,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_fefo, 'quantity_requested_in_base_unit', 15)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes                 => 'SC2: Test FEFO, selling 15 units.',
        p_new_transaction_id    => v_sc2_sale_transaction_id, -- OUT
        p_message               => v_sc2_sale_message       -- OUT
    );
    RAISE NOTICE '    SC2: Sale Transaction ID: %', v_sc2_sale_transaction_id;
    RAISE NOTICE '    SC2: Sale Message: %', v_sc2_sale_message;
    IF v_sc2_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC2: FEFO Sale failed: %', v_sc2_sale_message; END IF;

    RAISE NOTICE '    SC2: Verifying transaction details and stock...';
    DECLARE
        v_details_count INTEGER;
        v_qty_from_batch1 NUMERIC;
        v_qty_from_batch2 NUMERIC;
    BEGIN
        SELECT COUNT(*) INTO v_details_count FROM sales_transaction_details WHERE sale_transaction_id = v_sc2_sale_transaction_id AND medicine_id = v_medicine_id_fefo;
        RAISE NOTICE '      SC2: Number of detail lines for FEFO med: % (Expected 2)', v_details_count;
        IF v_details_count <> 2 THEN RAISE WARNING 'SC2: Expected 2 detail lines for FEFO sale, got %', v_details_count; END IF;

        SELECT quantity_sold_in_base_unit INTO v_qty_from_batch1 FROM sales_transaction_details WHERE sale_transaction_id = v_sc2_sale_transaction_id AND batch_id = (SELECT batch_id FROM batches WHERE batch_number = 'FEFO_B1' AND medicine_id = v_medicine_id_fefo);
        SELECT quantity_sold_in_base_unit INTO v_qty_from_batch2 FROM sales_transaction_details WHERE sale_transaction_id = v_sc2_sale_transaction_id AND batch_id = (SELECT batch_id FROM batches WHERE batch_number = 'FEFO_B2' AND medicine_id = v_medicine_id_fefo);
        RAISE NOTICE '      SC2: Qty from Batch1 (expires sooner): %, Expected: 10', v_qty_from_batch1;
        RAISE NOTICE '      SC2: Qty from Batch2: %, Expected: 5', v_qty_from_batch2;
        IF v_qty_from_batch1 <> 10 THEN RAISE WARNING 'SC2: Qty from Batch1 is %, expected 10', v_qty_from_batch1; END IF;
        IF v_qty_from_batch2 <> 5 THEN RAISE WARNING 'SC2: Qty from Batch2 is %, expected 5', v_qty_from_batch2; END IF;
    END;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_fefo) INTO v_stock_fefo;
    RAISE NOTICE '    SC2: FEFO Medicine Stock after sale: % (Expected 15)', v_stock_fefo;
    IF v_stock_fefo <> 15 THEN RAISE WARNING 'SC2: FEFO stock after sale is %, expected 15', v_stock_fefo; END IF;

    -- SC6: Verify transaction amounts for SC2
    RAISE NOTICE '    SC2: Verifying transaction amounts for transaction ID % ...', v_sc2_sale_transaction_id;
    SELECT total_amount, final_amount INTO v_transaction_total_amount, v_transaction_final_amount
    FROM sales_transactions WHERE sale_transaction_id = v_sc2_sale_transaction_id AND tenant_id = v_tenant_id;
    SELECT SUM(subtotal) INTO v_calculated_details_subtotal
    FROM sales_transaction_details WHERE sale_transaction_id = v_sc2_sale_transaction_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '      SC2: Transaction Total Amount: %, Calculated Details Subtotal: %', v_transaction_total_amount, v_calculated_details_subtotal;
    IF v_transaction_total_amount <> v_calculated_details_subtotal THEN RAISE WARNING 'SC2: Transaction total_amount % does not match sum of details subtotal %', v_transaction_total_amount, v_calculated_details_subtotal; END IF;
    IF v_transaction_final_amount <> v_transaction_total_amount THEN RAISE WARNING 'SC2: Transaction final_amount % does not match total_amount %', v_transaction_final_amount, v_transaction_total_amount; END IF;

    -- ---- 8. SC3: Attempt Sale of Non-Existent Medicine ----
    RAISE NOTICE '---- 8. SC3: Attempt Sale of Non-Existent Medicine ----';
    RAISE NOTICE '  SC3: Attempting to sell medicine with ID %...', v_non_existent_medicine_id;
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => NULL,
        p_prescription_id       => NULL,
        p_payment_method_id     => v_payment_method_id_cash,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_non_existent_medicine_id, 'quantity_requested_in_base_unit', 1)
        ),
        p_transaction_status_id => v_status_id_pending, -- Should not complete
        p_notes                 => 'SC3: Test sale of non-existent medicine.',
        p_new_transaction_id    => v_sc1_sale_transaction_id, -- OUT
        p_message               => v_sc1_sale_message       -- OUT
    );
    RAISE NOTICE '    SC3: Sale Transaction ID (should be NULL): %', v_sc1_sale_transaction_id;
    RAISE NOTICE '    SC3: Sale Message (should indicate error): %', v_sc1_sale_message;
    IF v_sc1_sale_transaction_id IS NOT NULL THEN
        RAISE WARNING 'SC3: Sale of non-existent medicine created a transaction ID %', v_sc1_sale_transaction_id;
    END IF;
    IF v_sc1_sale_message IS NULL OR (v_sc1_sale_message NOT LIKE '%không tồn tại%' AND v_sc1_sale_message NOT LIKE '%not exist%') THEN
        RAISE WARNING 'SC3: Error message for non-existent medicine is not as expected: %', v_sc1_sale_message;
    ELSE
         RAISE NOTICE '    SC3: Sale attempt with non-existent medicine handled as expected.';
    END IF;

    -- ---- 9. SC4: Sales Process for Walk-in Customer (No Customer/Prescription ID) ----
    RAISE NOTICE '---- 9. SC4: Sales Process for Walk-in Customer ----';
    RAISE NOTICE '  SC4: Selling 5 Paracetamol to a walk-in customer...';
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => NULL, -- Walk-in
        p_prescription_id       => NULL, -- No prescription
        p_payment_method_id     => v_payment_method_id_cash,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_para, 'quantity_requested_in_base_unit', 5)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes                 => 'SC4: Walk-in customer, cash payment.',
        p_new_transaction_id    => v_sc1_sale_transaction_id, -- OUT
        p_message               => v_sc1_sale_message       -- OUT
    );
    RAISE NOTICE '    SC4: Sale Transaction ID: %', v_sc1_sale_transaction_id;
    RAISE NOTICE '    SC4: Sale Message: %', v_sc1_sale_message;
    IF v_sc1_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC4: Walk-in customer sale failed: %', v_sc1_sale_message; END IF;

    DECLARE v_walkin_customer_id UUID; v_walkin_prescription_id UUID;
    BEGIN
        SELECT customer_id, prescription_id INTO v_walkin_customer_id, v_walkin_prescription_id
        FROM sales_transactions WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
        RAISE NOTICE '      SC4: Transaction Customer ID: %, Prescription ID: % (both expected NULL)', v_walkin_customer_id, v_walkin_prescription_id;
        IF v_walkin_customer_id IS NOT NULL THEN RAISE WARNING 'SC4: Customer ID is not NULL for walk-in sale.'; END IF;
        IF v_walkin_prescription_id IS NOT NULL THEN RAISE WARNING 'SC4: Prescription ID is not NULL for walk-in sale.'; END IF;
    END;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '    SC4: Paracetamol stock after walk-in sale: % (Expected 180 - 5 = 175)', v_stock_para;
    IF v_stock_para <> 175 THEN RAISE WARNING 'SC4: Paracetamol stock is %, expected 175', v_stock_para; END IF;

    -- SC6: Verify transaction amounts for SC4
    RAISE NOTICE '    SC4: Verifying transaction amounts for transaction ID % ...', v_sc1_sale_transaction_id;
    SELECT total_amount, final_amount INTO v_transaction_total_amount, v_transaction_final_amount
    FROM sales_transactions WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
    SELECT SUM(subtotal) INTO v_calculated_details_subtotal
    FROM sales_transaction_details WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '      SC4: Transaction Total Amount: %, Calculated Details Subtotal: %', v_transaction_total_amount, v_calculated_details_subtotal;
    IF v_transaction_total_amount <> v_calculated_details_subtotal THEN RAISE WARNING 'SC4: Transaction total_amount % does not match sum of details subtotal %', v_transaction_total_amount, v_calculated_details_subtotal; END IF;
    IF v_transaction_final_amount <> v_transaction_total_amount THEN RAISE WARNING 'SC4: Transaction final_amount % does not match total_amount %', v_transaction_final_amount, v_transaction_total_amount; END IF;

    -- ---- 10. SC5: Sales Process for Registered Customer (Distinct Sale) ----
    RAISE NOTICE '---- 10. SC5: Sales Process for Registered Customer (Distinct Sale) ----';
    RAISE NOTICE '  SC5: Selling 25 Amoxicillin to registered customer %...', v_customer_id;
    CALL sp_process_sale_transaction(
        p_tenant_id             => v_tenant_id,
        p_employee_id           => v_employee_id,
        p_customer_id           => v_customer_id, -- Registered customer
        p_prescription_id       => NULL,
        p_payment_method_id     => v_payment_method_id_card,
        p_sale_items            => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 25)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes                 => 'SC5: Registered customer, card payment.',
        p_new_transaction_id    => v_sc1_sale_transaction_id, -- OUT
        p_message               => v_sc1_sale_message       -- OUT
    );
    RAISE NOTICE '    SC5: Sale Transaction ID: %', v_sc1_sale_transaction_id;
    RAISE NOTICE '    SC5: Sale Message: %', v_sc1_sale_message;
    IF v_sc1_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC5: Registered customer sale failed: %', v_sc1_sale_message; END IF;

    DECLARE v_reg_customer_id UUID;
    BEGIN
        SELECT customer_id INTO v_reg_customer_id
        FROM sales_transactions WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
        RAISE NOTICE '      SC5: Transaction Customer ID: % (Expected %)', v_reg_customer_id, v_customer_id;
        IF v_reg_customer_id <> v_customer_id THEN RAISE WARNING 'SC5: Customer ID in transaction % does not match expected %', v_reg_customer_id, v_customer_id; END IF;
    END;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    SC5: Amoxicillin stock after registered customer sale: % (Expected 440 - 25 = 415)', v_stock_amox;
    IF v_stock_amox <> 415 THEN RAISE WARNING 'SC5: Amoxicillin stock is %, expected 415', v_stock_amox; END IF;

    -- SC6: Verify transaction amounts for SC5
    RAISE NOTICE '    SC5: Verifying transaction amounts for transaction ID % ...', v_sc1_sale_transaction_id;
    SELECT total_amount, final_amount INTO v_transaction_total_amount, v_transaction_final_amount
    FROM sales_transactions WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
    SELECT SUM(subtotal) INTO v_calculated_details_subtotal
    FROM sales_transaction_details WHERE sale_transaction_id = v_sc1_sale_transaction_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '      SC5: Transaction Total Amount: %, Calculated Details Subtotal: %', v_transaction_total_amount, v_calculated_details_subtotal;
    IF v_transaction_total_amount <> v_calculated_details_subtotal THEN RAISE WARNING 'SC5: Transaction total_amount % does not match sum of details subtotal %', v_transaction_total_amount, v_calculated_details_subtotal; END IF;
    IF v_transaction_final_amount <> v_transaction_total_amount THEN RAISE WARNING 'SC5: Transaction final_amount % does not match total_amount %', v_transaction_final_amount, v_transaction_total_amount; END IF;


    -- =============================================
-- ADDITIONAL TEST SCENARIOS
-- =============================================

-- ---- 11. SC7: Sales Process with Prescription ----
RAISE NOTICE '---- 11. SC7: Sales Process with Prescription ----';
DECLARE
    v_prescription_id UUID;
    v_prescription_detail_id_amox UUID;
    v_prescription_detail_id_para UUID;
    v_sc7_sale_transaction_id UUID;
    v_sc7_sale_message TEXT;
BEGIN
    -- Create a prescription
    RAISE NOTICE '  SC7: Creating prescription for customer %...', v_customer_name;
    CALL sp_create_prescription(
        p_tenant_id => v_tenant_id,
        p_customer_id => v_customer_id,
        p_doctor_name => 'Bác sĩ Nguyễn Văn X',
        p_clinic_address => 'Phòng khám Y Test',
        p_diagnosis => 'Nhiễm khuẩn đường hô hấp',
        p_notes => 'Uống thuốc đủ liều',
        p_employee_id => v_employee_id,
        p_new_prescription_id => v_prescription_id,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC7: Prescription ID: %', v_prescription_id;
    IF v_prescription_id IS NULL THEN RAISE EXCEPTION 'SC7: Failed to create prescription. %', v_sp_message; END IF;

    -- Add medicines to prescription
    CALL sp_add_prescription_detail(
        p_tenant_id => v_tenant_id,
        p_prescription_id => v_prescription_id,
        p_medicine_id => v_medicine_id_amox,
        p_dosage => 'Ngày uống 2 viên, sáng 1, tối 1, sau ăn',
        p_quantity_prescribed => 30,
        p_notes => 'Uống đủ 15 ngày',
        p_new_prescription_detail_id => v_prescription_detail_id_amox,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC7: Prescription Detail Amox ID: %', v_prescription_detail_id_amox;
    IF v_prescription_detail_id_amox IS NULL THEN RAISE EXCEPTION 'SC7: Failed to add Amoxicillin to prescription. %', v_sp_message; END IF;

    CALL sp_add_prescription_detail(
        p_tenant_id => v_tenant_id,
        p_prescription_id => v_prescription_id,
        p_medicine_id => v_medicine_id_para,
        p_dosage => 'Uống khi sốt, 4-6 giờ/lần, tối đa 4 lần/ngày',
        p_quantity_prescribed => 20,
        p_notes => 'Uống khi cần',
        p_new_prescription_detail_id => v_prescription_detail_id_para,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC7: Prescription Detail Para ID: %', v_prescription_detail_id_para;
    IF v_prescription_detail_id_para IS NULL THEN RAISE EXCEPTION 'SC7: Failed to add Paracetamol to prescription. %', v_sp_message; END IF;

    -- Process sale based on prescription
    RAISE NOTICE '  SC7: Processing sale based on prescription...';
    CALL sp_process_sale_transaction(
        p_tenant_id => v_tenant_id,
        p_employee_id => v_employee_id,
        p_customer_id => v_customer_id,
        p_prescription_id => v_prescription_id,
        p_payment_method_id => v_payment_method_id_cash,
        p_sale_items => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 30),
            jsonb_build_object('medicine_id', v_medicine_id_para, 'quantity_requested_in_base_unit', 20)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes => 'SC7: Sale based on prescription',
        p_new_transaction_id => v_sc7_sale_transaction_id,
        p_message => v_sc7_sale_message
    );
    RAISE NOTICE '    SC7: Sale Transaction ID: %', v_sc7_sale_transaction_id;
    RAISE NOTICE '    SC7: Sale Message: %', v_sc7_sale_message;
    IF v_sc7_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC7: Prescription-based sale failed: %', v_sc7_sale_message; END IF;

    -- Verify prescription is marked as dispensed
    DECLARE
        v_is_dispensed BOOLEAN;
    BEGIN
        SELECT is_dispensed INTO v_is_dispensed
        FROM prescriptions
        WHERE prescription_id = v_prescription_id AND tenant_id = v_tenant_id;
        RAISE NOTICE '    SC7: Prescription is_dispensed: % (Expected: TRUE)', v_is_dispensed;
        IF v_is_dispensed IS NOT TRUE THEN RAISE WARNING 'SC7: Prescription not marked as dispensed after sale'; END IF;
    END;

    -- Verify stock levels
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '    SC7: Amoxicillin stock after prescription sale: % (Expected 415 - 30 = 385)', v_stock_amox;
    RAISE NOTICE '    SC7: Paracetamol stock after prescription sale: % (Expected 175 - 20 = 155)', v_stock_para;
    IF v_stock_amox <> 385 THEN RAISE WARNING 'SC7: Amoxicillin stock is %, expected 385', v_stock_amox; END IF;
    IF v_stock_para <> 155 THEN RAISE WARNING 'SC7: Paracetamol stock is %, expected 155', v_stock_para; END IF;
END;

-- ---- 12. SC8: Inventory Adjustment Test ----
RAISE NOTICE '---- 12. SC8: Inventory Adjustment Test ----';
DECLARE
    v_adjustment_id_decrease UUID;
    v_adjustment_id_increase UUID;
BEGIN
    -- Record initial stock
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '  SC8: Initial Amoxicillin stock: %', v_stock_amox;

    -- Test inventory decrease (e.g., damaged goods)
    RAISE NOTICE '  SC8: Testing inventory decrease (damaged goods)...';
    CALL sp_create_inventory_adjustment(
        p_tenant_id => v_tenant_id,
        p_batch_id => v_batch_id_amox,
        p_employee_id => v_employee_id,
        p_adjustment_type_id => v_adj_type_xuat,
        p_quantity_change => -10, -- Negative for decrease
        p_reason => 'Hàng bị hỏng do ẩm ướt',
        p_new_adjustment_id => v_adjustment_id_decrease,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC8: Decrease Adjustment ID: %', v_adjustment_id_decrease;
    RAISE NOTICE '    SC8: Adjustment Message: %', v_sp_message;
    IF v_adjustment_id_decrease IS NULL THEN RAISE EXCEPTION 'SC8: Inventory decrease adjustment failed: %', v_sp_message; END IF;

    -- Verify stock after decrease
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    SC8: Amoxicillin stock after decrease: % (Expected 385 - 10 = 375)', v_stock_amox;
    IF v_stock_amox <> 375 THEN RAISE WARNING 'SC8: Amoxicillin stock after decrease is %, expected 375', v_stock_amox; END IF;

    -- Test inventory increase (e.g., found additional stock during count)
    RAISE NOTICE '  SC8: Testing inventory increase (stock count adjustment)...';
    CALL sp_create_inventory_adjustment(
        p_tenant_id => v_tenant_id,
        p_batch_id => v_batch_id_amox,
        p_employee_id => v_employee_id,
        p_adjustment_type_id => v_adj_type_nhap,
        p_quantity_change => 5, -- Positive for increase
        p_reason => 'Điều chỉnh sau kiểm kê',
        p_new_adjustment_id => v_adjustment_id_increase,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC8: Increase Adjustment ID: %', v_adjustment_id_increase;
    RAISE NOTICE '    SC8: Adjustment Message: %', v_sp_message;
    IF v_adjustment_id_increase IS NULL THEN RAISE EXCEPTION 'SC8: Inventory increase adjustment failed: %', v_sp_message; END IF;

    -- Verify stock after increase
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    SC8: Amoxicillin stock after increase: % (Expected 375 + 5 = 380)', v_stock_amox;
    IF v_stock_amox <> 380 THEN RAISE WARNING 'SC8: Amoxicillin stock after increase is %, expected 380', v_stock_amox; END IF;
END;

-- ---- 13. SC9: Expired Medicine Handling ----
RAISE NOTICE '---- 13. SC9: Expired Medicine Handling ----';
DECLARE
    v_medicine_id_expired UUID;
    v_packaging_id_expired UUID;
    v_import_invoice_id_expired UUID;
    v_import_detail_id_expired UUID;
    v_batch_id_expired UUID;
    v_sc9_sale_transaction_id UUID;
    v_sc9_sale_message TEXT;
    v_stock_expired NUMERIC;
BEGIN
    -- Create medicine that will be expired
    RAISE NOTICE '  SC9: Creating medicine that will be expired...';
    CALL sp_create_medicine(
        p_tenant_id => v_tenant_id,
        p_medicine_name => 'ExpiredMed Test',
        p_description => 'Medicine for expired test',
        p_registration_number => 'EXPTEST001',
        p_category_id => v_category_id_khang_sinh,
        p_base_unit => 'Viên',
        p_new_medicine_id => v_medicine_id_expired,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC9: Expired Medicine ID: %', v_medicine_id_expired;
    IF v_medicine_id_expired IS NULL THEN RAISE EXCEPTION 'SC9: Failed to create expired test medicine. %', v_sp_message; END IF;

    -- Add packaging unit
    CALL sp_add_medicine_packaging_unit(
        p_tenant_id => v_tenant_id,
        p_medicine_id => v_medicine_id_expired,
        p_unit_name => 'Hộp 20 viên',
        p_quantity_per_unit => 20,
        p_is_default_sale_unit => TRUE,
        p_is_default_import_unit => TRUE,
        p_new_packaging_unit_id => v_packaging_id_expired,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC9: Expired Medicine Packaging ID: %', v_packaging_id_expired;
    IF v_packaging_id_expired IS NULL THEN RAISE EXCEPTION 'SC9: Failed to add packaging unit for expired medicine. %', v_sp_message; END IF;

    -- Create import invoice
    CALL sp_create_import_invoice(
        p_tenant_id => v_tenant_id,
        p_supplier_id => v_supplier_id,
        p_employee_id => v_employee_id,
        p_invoice_number => 'INV-EXP-TEST',
        p_import_date => (CURRENT_DATE - INTERVAL '1 year'),
        p_new_import_invoice_id => v_import_invoice_id_expired,
        p_message => v_sp_message,
        p_notes => 'Import for expired medicine test'
    );
    RAISE NOTICE '    SC9: Expired Medicine Import Invoice ID: %', v_import_invoice_id_expired;
    IF v_import_invoice_id_expired IS NULL THEN RAISE EXCEPTION 'SC9: Failed to create import invoice for expired medicine. %', v_sp_message; END IF;

    -- Add import detail
    CALL sp_add_import_invoice_detail(
        p_tenant_id => v_tenant_id,
        p_import_invoice_id => v_import_invoice_id_expired,
        p_medicine_id => v_medicine_id_expired,
        p_packaging_unit_id => v_packaging_id_expired,
        p_quantity_imported_in_unit => 5,
        p_price_per_import_unit => 50000,
        p_new_import_invoice_detail_id => v_import_detail_id_expired,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC9: Expired Medicine Import Detail ID: %', v_import_detail_id_expired;
    IF v_import_detail_id_expired IS NULL THEN RAISE EXCEPTION 'SC9: Failed to add import detail for expired medicine. %', v_sp_message; END IF;

    -- Add batch with expiry date in the past
    CALL sp_add_medicine_batch_from_import_alt(
        p_tenant_id => v_tenant_id,
        p_import_invoice_detail_id => v_import_detail_id_expired,
        p_batch_number => 'EXPIRED-BATCH-001',
        p_expiry_date => (CURRENT_DATE - INTERVAL '1 day')::TIMESTAMP WITHOUT TIME ZONE, -- Expired yesterday
        p_selling_price_per_base_unit => 3000,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC9: Add Batch Message: %', v_sp_message;
    
    -- Get the batch ID
    SELECT b.batch_id INTO v_batch_id_expired 
    FROM batches b
    JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id
    WHERE iid.import_invoice_detail_id = v_import_detail_id_expired LIMIT 1;
    RAISE NOTICE '    SC9: Expired Batch ID: %', v_batch_id_expired;
    IF v_batch_id_expired IS NULL THEN RAISE EXCEPTION 'SC9: Failed to retrieve batch for expired medicine'; END IF;

    -- Check stock (should not include expired medicine)
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_expired) INTO v_stock_expired;
    RAISE NOTICE '    SC9: Available stock for expired medicine: % (Expected 0)', v_stock_expired;
    IF v_stock_expired <> 0 THEN RAISE WARNING 'SC9: Expired medicine stock is %, expected 0', v_stock_expired; END IF;

    -- Attempt to sell expired medicine
    RAISE NOTICE '  SC9: Attempting to sell expired medicine...';
    CALL sp_process_sale_transaction(
        p_tenant_id => v_tenant_id,
        p_employee_id => v_employee_id,
        p_customer_id => NULL,
        p_prescription_id => NULL,
        p_payment_method_id => v_payment_method_id_cash,
        p_sale_items => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_expired, 'quantity_requested_in_base_unit', 10)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes => 'SC9: Attempt to sell expired medicine',
        p_new_transaction_id => v_sc9_sale_transaction_id,
        p_message => v_sc9_sale_message
    );
    RAISE NOTICE '    SC9: Sale Transaction ID (should be NULL): %', v_sc9_sale_transaction_id;
    RAISE NOTICE '    SC9: Sale Message (should indicate error): %', v_sc9_sale_message;
    IF v_sc9_sale_transaction_id IS NOT NULL THEN
        RAISE WARNING 'SC9: Sale of expired medicine created a transaction ID %', v_sc9_sale_transaction_id;
    END IF;
    IF v_sc9_sale_message IS NULL OR (v_sc9_sale_message NOT LIKE '%hết hạn%' AND v_sc9_sale_message NOT LIKE '%expired%' AND v_sc9_sale_message NOT LIKE '%không đủ%' AND v_sc9_sale_message NOT LIKE '%insufficient%') THEN
        RAISE WARNING 'SC9: Error message for expired medicine is not as expected: %', v_sc9_sale_message;
    ELSE
        RAISE NOTICE '    SC9: Sale attempt with expired medicine handled as expected.';
    END IF;

    -- Run report to find expired medicines
    RAISE NOTICE '  SC9: Running report to find expired medicines...';
    PERFORM * FROM fn_report_expired_medicines(v_tenant_id);
    RAISE NOTICE '    SC9: Expired medicines report executed successfully.';
END;

-- ---- 14. SC10: Discount Application Test ----
RAISE NOTICE '---- 14. SC10: Discount Application Test ----';
DECLARE
    v_sc10_sale_transaction_id UUID;
    v_sc10_sale_message TEXT;
    v_discount_amount NUMERIC := 5000; -- 5,000 VND discount
    v_transaction_total_amount NUMERIC;
    v_transaction_final_amount NUMERIC;
    v_calculated_details_subtotal NUMERIC;
BEGIN
    -- Process sale with discount
    RAISE NOTICE '  SC10: Processing sale with discount...';
    CALL sp_process_sale_transaction_with_discount(
        p_tenant_id => v_tenant_id,
        p_employee_id => v_employee_id,
        p_customer_id => v_customer_id,
        p_prescription_id => NULL,
        p_payment_method_id => v_payment_method_id_cash,
        p_sale_items => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 20),
            jsonb_build_object('medicine_id', v_medicine_id_para, 'quantity_requested_in_base_unit', 10)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_discount_amount => v_discount_amount,
        p_notes => 'SC10: Sale with discount applied',
        p_new_transaction_id => v_sc10_sale_transaction_id,
        p_message => v_sc10_sale_message
    );
    RAISE NOTICE '    SC10: Sale Transaction ID: %', v_sc10_sale_transaction_id;
    RAISE NOTICE '    SC10: Sale Message: %', v_sc10_sale_message;
    IF v_sc10_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC10: Sale with discount failed: %', v_sc10_sale_message; END IF;

    -- Verify transaction amounts
    SELECT total_amount, final_amount INTO v_transaction_total_amount, v_transaction_final_amount
    FROM sales_transactions 
    WHERE sale_transaction_id = v_sc10_sale_transaction_id AND tenant_id = v_tenant_id;
    
    SELECT SUM(subtotal) INTO v_calculated_details_subtotal
    FROM sales_transaction_details 
    WHERE sale_transaction_id = v_sc10_sale_transaction_id AND tenant_id = v_tenant_id;
    
    RAISE NOTICE '    SC10: Transaction Total Amount: %', v_transaction_total_amount;

    RAISE NOTICE '    SC10: Calculated Details Subtotal: %', v_calculated_details_subtotal;
    RAISE NOTICE '    SC10: Transaction Final Amount: %', v_transaction_final_amount;
    RAISE NOTICE '    SC10: Applied Discount: %', v_discount_amount;
    
    IF v_transaction_total_amount <> v_calculated_details_subtotal THEN 
        RAISE WARNING 'SC10: Transaction total_amount % does not match sum of details subtotal %', 
            v_transaction_total_amount, v_calculated_details_subtotal; 
    END IF;
    
    IF v_transaction_final_amount <> (v_transaction_total_amount - v_discount_amount) THEN 
        RAISE WARNING 'SC10: Transaction final_amount % does not match (total_amount - discount) %', 
            v_transaction_final_amount, (v_transaction_total_amount - v_discount_amount); 
    END IF;

    -- Verify stock levels
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '    SC10: Amoxicillin stock after discounted sale: % (Expected 380 - 20 = 360)', v_stock_amox;
    RAISE NOTICE '    SC10: Paracetamol stock after discounted sale: % (Expected 155 - 10 = 145)', v_stock_para;
    IF v_stock_amox <> 360 THEN RAISE WARNING 'SC10: Amoxicillin stock is %, expected 360', v_stock_amox; END IF;
    IF v_stock_para <> 145 THEN RAISE WARNING 'SC10: Paracetamol stock is %, expected 145', v_stock_para; END IF;
END;

-- ---- 15. SC11: Loyalty Points Accumulation Test ----
RAISE NOTICE '---- 15. SC11: Loyalty Points Accumulation Test ----';
DECLARE
    v_loyalty_tier_id UUID;
    v_initial_points NUMERIC;
    v_points_rate NUMERIC := 0.01; -- 1% of purchase amount as points
    v_sc11_sale_transaction_id UUID;
    v_sc11_sale_message TEXT;
    v_transaction_amount NUMERIC;
    v_expected_points_earned NUMERIC;
    v_actual_points_earned NUMERIC;
    v_updated_points NUMERIC;
BEGIN
    -- Create loyalty tier
    RAISE NOTICE '  SC11: Creating loyalty tier...';
    CALL sp_create_loyalty_tier(
        p_tenant_id => v_tenant_id,
        p_tier_name => 'Standard Test',
        p_min_points_required => 0,
        p_discount_percentage => 0,
        p_other_benefits => 'Basic tier benefits',
        p_new_loyalty_tier_id => v_loyalty_tier_id,
        p_message => v_sp_message
    );
    RAISE NOTICE '    SC11: Loyalty Tier ID: %', v_loyalty_tier_id;
    IF v_loyalty_tier_id IS NULL THEN RAISE EXCEPTION 'SC11: Failed to create loyalty tier. %', v_sp_message; END IF;

    -- Assign loyalty tier to customer and check initial points
    UPDATE customers 
    SET loyalty_tier_id = v_loyalty_tier_id, 
        loyalty_points = 100 -- Set initial points
    WHERE customer_id = v_customer_id AND tenant_id = v_tenant_id;
    
    SELECT loyalty_points INTO v_initial_points 
    FROM customers 
    WHERE customer_id = v_customer_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '    SC11: Initial loyalty points: %', v_initial_points;

    -- Process sale with loyalty points accumulation
    RAISE NOTICE '  SC11: Processing sale with loyalty points accumulation...';
    CALL sp_process_sale_transaction_with_loyalty(
        p_tenant_id => v_tenant_id,
        p_employee_id => v_employee_id,
        p_customer_id => v_customer_id,
        p_prescription_id => NULL,
        p_payment_method_id => v_payment_method_id_cash,
        p_sale_items => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 50)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_points_rate => v_points_rate,
        p_notes => 'SC11: Sale with loyalty points accumulation',
        p_new_transaction_id => v_sc11_sale_transaction_id,
        p_message => v_sc11_sale_message
    );
    RAISE NOTICE '    SC11: Sale Transaction ID: %', v_sc11_sale_transaction_id;
    RAISE NOTICE '    SC11: Sale Message: %', v_sc11_sale_message;
    IF v_sc11_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC11: Sale with loyalty points failed: %', v_sc11_sale_message; END IF;

    -- Verify points earned
    SELECT total_amount, points_earned INTO v_transaction_amount, v_actual_points_earned
    FROM sales_transactions 
    WHERE sale_transaction_id = v_sc11_sale_transaction_id AND tenant_id = v_tenant_id;
    
    v_expected_points_earned := ROUND(v_transaction_amount * v_points_rate, 2);
    RAISE NOTICE '    SC11: Transaction Amount: %', v_transaction_amount;
    RAISE NOTICE '    SC11: Expected Points Earned: %', v_expected_points_earned;
    RAISE NOTICE '    SC11: Actual Points Earned: %', v_actual_points_earned;
    
    IF v_actual_points_earned <> v_expected_points_earned THEN 
        RAISE WARNING 'SC11: Points earned % does not match expected %', 
            v_actual_points_earned, v_expected_points_earned; 
    END IF;

    -- Verify updated customer points
    SELECT loyalty_points INTO v_updated_points 
    FROM customers 
    WHERE customer_id = v_customer_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '    SC11: Updated loyalty points: % (Expected: % + % = %)', 
        v_updated_points, v_initial_points, v_actual_points_earned, (v_initial_points + v_actual_points_earned);
    
    IF v_updated_points <> (v_initial_points + v_actual_points_earned) THEN 
        RAISE WARNING 'SC11: Updated points % does not match expected %', 
            v_updated_points, (v_initial_points + v_actual_points_earned); 
    END IF;

    -- Verify stock levels
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    RAISE NOTICE '    SC11: Amoxicillin stock after loyalty sale: % (Expected 360 - 50 = 310)', v_stock_amox;
    IF v_stock_amox <> 310 THEN RAISE WARNING 'SC11: Amoxicillin stock is %, expected 310', v_stock_amox; END IF;
END;

-- ---- 16. SC12: Transaction Cancellation Test ----
RAISE NOTICE '---- 16. SC12: Transaction Cancellation Test ----';
DECLARE
    v_sc12_sale_transaction_id UUID;
    v_sc12_sale_message TEXT;
    v_pre_cancel_stock_amox NUMERIC;
    v_post_cancel_stock_amox NUMERIC;
    v_cancel_result BOOLEAN;
    v_cancel_message TEXT;
BEGIN
    -- Process a sale that will be cancelled
    RAISE NOTICE '  SC12: Processing sale to be cancelled...';
    CALL sp_process_sale_transaction(
        p_tenant_id => v_tenant_id,
        p_employee_id => v_employee_id,
        p_customer_id => NULL,
        p_prescription_id => NULL,
        p_payment_method_id => v_payment_method_id_cash,
        p_sale_items => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 10)
        ),
        p_transaction_status_id => v_status_id_completed,
        p_notes => 'SC12: Sale to be cancelled',
        p_new_transaction_id => v_sc12_sale_transaction_id,
        p_message => v_sc12_sale_message
    );
    RAISE NOTICE '    SC12: Sale Transaction ID: %', v_sc12_sale_transaction_id;
    RAISE NOTICE '    SC12: Sale Message: %', v_sc12_sale_message;
    IF v_sc12_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC12: Sale to be cancelled failed: %', v_sc12_sale_message; END IF;

    -- Check stock after sale
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_pre_cancel_stock_amox;
    RAISE NOTICE '    SC12: Amoxicillin stock after sale: % (Expected 310 - 10 = 300)', v_pre_cancel_stock_amox;
    IF v_pre_cancel_stock_amox <> 300 THEN RAISE WARNING 'SC12: Amoxicillin stock before cancellation is %, expected 300', v_pre_cancel_stock_amox; END IF;

    -- Cancel the transaction
    RAISE NOTICE '  SC12: Cancelling transaction...';
    CALL sp_cancel_sale_transaction(
        p_tenant_id => v_tenant_id,
        p_sale_transaction_id => v_sc12_sale_transaction_id,
        p_employee_id => v_employee_id,
        p_cancellation_reason => 'Customer changed mind',
        p_success => v_cancel_result,
        p_message => v_cancel_message
    );
    RAISE NOTICE '    SC12: Cancellation Result: %', v_cancel_result;
    RAISE NOTICE '    SC12: Cancellation Message: %', v_cancel_message;
    IF NOT v_cancel_result THEN RAISE EXCEPTION 'SC12: Transaction cancellation failed: %', v_cancel_message; END IF;

    -- Verify transaction status
    DECLARE
        v_transaction_status_id UUID;
        v_status_name VARCHAR(100);
    BEGIN
        SELECT st.transaction_status_id, ts.status_name 
        INTO v_transaction_status_id, v_status_name
        FROM sales_transactions st
        JOIN transaction_statuses ts ON st.transaction_status_id = ts.transaction_status_id AND st.tenant_id = ts.tenant_id
        WHERE st.sale_transaction_id = v_sc12_sale_transaction_id AND st.tenant_id = v_tenant_id;
        
        RAISE NOTICE '    SC12: Transaction Status ID: %, Status Name: %', v_transaction_status_id, v_status_name;
        IF v_transaction_status_id <> v_status_id_cancelled THEN 
            RAISE WARNING 'SC12: Transaction status ID % does not match cancelled status ID %', 
                v_transaction_status_id, v_status_id_cancelled; 
        END IF;
        IF v_status_name <> 'Đã hủy' THEN 
            RAISE WARNING 'SC12: Transaction status name % is not "Đã hủy"', v_status_name; 
        END IF;
    END;

    -- Verify stock is restored
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_post_cancel_stock_amox;
    RAISE NOTICE '    SC12: Amoxicillin stock after cancellation: % (Expected 300 + 10 = 310)', v_post_cancel_stock_amox;
    IF v_post_cancel_stock_amox <> 310 THEN RAISE WARNING 'SC12: Amoxicillin stock after cancellation is %, expected 310', v_post_cancel_stock_amox; END IF;
END;

-- ---- 17. SC13: Multiple Payment Methods Test ----
RAISE NOTICE '---- 17. SC13: Multiple Payment Methods Test ----';
DECLARE
    v_sc13_sale_transaction_id UUID;
    v_sc13_sale_message TEXT;
    v_payment_id_1 UUID;
    v_payment_id_2 UUID;
    v_payment_message TEXT;
    v_transaction_amount NUMERIC;
    v_cash_amount NUMERIC := 20000; -- 20,000 VND in cash
    v_card_amount NUMERIC; -- Remainder will be paid by card
    v_total_payments NUMERIC;
BEGIN
    -- Process a sale with partial payment
    RAISE NOTICE '  SC13: Processing sale for multiple payment methods...';
    CALL sp_process_sale_transaction(
        p_tenant_id => v_tenant_id,
        p_employee_id => v_employee_id,
        p_customer_id => v_customer_id,
        p_prescription_id => NULL,
        p_payment_method_id => NULL, -- No payment method initially
        p_sale_items => jsonb_build_array(
            jsonb_build_object('medicine_id', v_medicine_id_amox, 'quantity_requested_in_base_unit', 30),
            jsonb_build_object('medicine_id', v_medicine_id_para, 'quantity_requested_in_base_unit', 15)
        ),
        p_transaction_status_id => v_status_id_pending, -- Pending until fully paid
        p_notes => 'SC13: Sale with multiple payment methods',
        p_new_transaction_id => v_sc13_sale_transaction_id,
        p_message => v_sc13_sale_message
    );
    RAISE NOTICE '    SC13: Sale Transaction ID: %', v_sc13_sale_transaction_id;
    RAISE NOTICE '    SC13: Sale Message: %', v_sc13_sale_message;
    IF v_sc13_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC13: Sale for multiple payments failed: %', v_sc13_sale_message; END IF;

    -- Get transaction amount
    SELECT total_amount INTO v_transaction_amount
    FROM sales_transactions 
    WHERE sale_transaction_id = v_sc13_sale_transaction_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '    SC13: Transaction Total Amount: %', v_transaction_amount;
    
    -- Calculate card amount (remainder)
    v_card_amount := v_transaction_amount - v_cash_amount;
    RAISE NOTICE '    SC13: Cash Payment: %, Card Payment: %', v_cash_amount, v_card_amount;

    -- Add first payment (cash)
    RAISE NOTICE '  SC13: Adding first payment (cash)...';
    CALL sp_add_invoice_payment(
        p_tenant_id => v_tenant_id,
        p_sale_transaction_id => v_sc13_sale_transaction_id,
        p_payment_method_id => v_payment_method_id_cash,
        p_amount_paid => v_cash_amount,
        p_reference_number => NULL,
        p_notes => 'Partial payment in cash',
        p_new_payment_id => v_payment_id_1,
        p_message => v_payment_message
    );
    RAISE NOTICE '    SC13: Cash Payment ID: %', v_payment_id_1;
    RAISE NOTICE '    SC13: Payment Message: %', v_payment_message;
    IF v_payment_id_1 IS NULL THEN RAISE EXCEPTION 'SC13: Cash payment failed: %', v_payment_message; END IF;

    -- Add second payment (card)
    RAISE NOTICE '  SC13: Adding second payment (card)...';
    CALL sp_add_invoice_payment(
        p_tenant_id => v_tenant_id,
        p_sale_transaction_id => v_sc13_sale_transaction_id,
        p_payment_method_id => v_payment_method_id_card,
        p_amount_paid => v_card_amount,
        p_reference_number => 'CARD-REF-123456',
        p_notes => 'Remaining payment by card',
        p_new_payment_id => v_payment_id_2,
        p_message => v_payment_message
    );
    RAISE NOTICE '    SC13: Card Payment ID: %', v_payment_id_2;
    RAISE NOTICE '    SC13: Payment Message: %', v_payment_message;
    IF v_payment_id_2 IS NULL THEN RAISE EXCEPTION 'SC13: Card payment failed: %', v_payment_message; END IF;

    -- Complete the transaction
    RAISE NOTICE '  SC13: Completing transaction after full payment...';
    CALL sp_update_transaction_status(
        p_tenant_id => v_tenant_id,
        p_sale_transaction_id => v_sc13_sale_transaction_id,
        p_new_status_id => v_status_id_completed,
        p_employee_id => v_employee_id,
        p_notes => 'Transaction completed after full payment',
        p_success => v_cancel_result, -- Reusing variable
        p_message => v_cancel_message -- Reusing variable
    );
    RAISE NOTICE '    SC13: Status Update Result: %', v_cancel_result;
    RAISE NOTICE '    SC13: Status Update Message: %', v_cancel_message;
    IF NOT v_cancel_result THEN RAISE EXCEPTION 'SC13: Transaction status update failed: %', v_cancel_message; END IF;

    -- Verify payments total
    SELECT SUM(amount_paid) INTO v_total_payments
    FROM invoice_payments
    WHERE sale_transaction_id = v_sc13_sale_transaction_id AND tenant_id = v_tenant_id;
    RAISE NOTICE '    SC13: Total Payments: % (Expected: %)', v_total_payments, v_transaction_amount;
    IF v_total_payments <> v_transaction_amount THEN 
        RAISE WARNING 'SC13: Total payments % does not match transaction amount %', 
            v_total_payments, v_transaction_amount; 
    END IF;

    -- Verify stock levels
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_amox) INTO v_stock_amox;
    SELECT fn_get_available_stock_for_medicine(v_tenant_id, v_medicine_id_para) INTO v_stock_para;
    RAISE NOTICE '    SC13: Amoxicillin stock after multiple payment sale: % (Expected 310 - 30 = 280)', v_stock_amox;
    RAISE NOTICE '    SC13: Paracetamol stock after multiple payment sale: % (Expected 145 - 15 = 130)', v_stock_para;
    IF v_stock_amox <> 280 THEN RAISE WARNING 'SC13: Amoxicillin stock is %, expected 280', v_stock_amox; END IF;
    IF v_stock_para <> 130 THEN RAISE WARNING 'SC13: Paracetamol stock is %, expected 130', v_stock_para; END IF;
END;

    -- ---- 18. SC14: Near-Expiry Medicine Report Test ----
    RAISE NOTICE '---- 18. SC14: Near-Expiry Medicine Report Test ----';
    DECLARE
        v_medicine_id_near_expiry UUID;
        v_packaging_id_near_expiry UUID;
        v_import_invoice_id_near_expiry UUID;
        v_import_detail_id_near_expiry UUID;
        v_batch_id_near_expiry UUID;
        v_near_expiry_days INTEGER := 90; -- Consider medicines expiring within 90 days as near-expiry
        v_report_count INTEGER;
    BEGIN
        -- Create medicine that will be near expiry
        RAISE NOTICE '  SC14: Creating medicine that will be near expiry...';
        CALL sp_create_medicine(
            p_tenant_id => v_tenant_id,
            p_medicine_name => 'NearExpiryMed Test',
            p_description => 'Medicine for near-expiry test',
            p_registration_number => 'NEAREXP001',
            p_category_id => v_category_id_giam_dau,
            p_base_unit => 'Viên',
            p_new_medicine_id => v_medicine_id_near_expiry,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC14: Near-Expiry Medicine ID: %', v_medicine_id_near_expiry;
        IF v_medicine_id_near_expiry IS NULL THEN RAISE EXCEPTION 'SC14: Failed to create near-expiry test medicine. %', v_sp_message; END IF;

        -- Add packaging unit
        CALL sp_add_medicine_packaging_unit(
            p_tenant_id => v_tenant_id,
            p_medicine_id => v_medicine_id_near_expiry,
            p_unit_name => 'Hộp 30 viên',
            p_quantity_per_unit => 30,
            p_is_default_sale_unit => TRUE,
            p_is_default_import_unit => TRUE,
            p_new_packaging_unit_id => v_packaging_id_near_expiry,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC14: Near-Expiry Medicine Packaging ID: %', v_packaging_id_near_expiry;
        IF v_packaging_id_near_expiry IS NULL THEN RAISE EXCEPTION 'SC14: Failed to add packaging unit for near-expiry medicine. %', v_sp_message; END IF;

        -- Create import invoice
        CALL sp_create_import_invoice(
            p_tenant_id => v_tenant_id,
            p_supplier_id => v_supplier_id,
            p_employee_id => v_employee_id,
            p_invoice_number => 'INV-NEAR-EXP-TEST',
            p_import_date => CURRENT_DATE,
            p_new_import_invoice_id => v_import_invoice_id_near_expiry,
            p_message => v_sp_message,
            p_notes => 'Import for near-expiry medicine test'
        );
        RAISE NOTICE '    SC14: Near-Expiry Medicine Import Invoice ID: %', v_import_invoice_id_near_expiry;
        IF v_import_invoice_id_near_expiry IS NULL THEN RAISE EXCEPTION 'SC14: Failed to create import invoice for near-expiry medicine. %', v_sp_message; END IF;

        -- Add import detail
        CALL sp_add_import_invoice_detail(
            p_tenant_id => v_tenant_id,
            p_import_invoice_id => v_import_invoice_id_near_expiry,
            p_medicine_id => v_medicine_id_near_expiry,
            p_packaging_unit_id => v_packaging_id_near_expiry,
            p_quantity_imported_in_unit => 10,
            p_price_per_import_unit => 45000,
            p_new_import_invoice_detail_id => v_import_detail_id_near_expiry,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC14: Near-Expiry Medicine Import Detail ID: %', v_import_detail_id_near_expiry;
        IF v_import_detail_id_near_expiry IS NULL THEN RAISE EXCEPTION 'SC14: Failed to add import detail for near-expiry medicine. %', v_sp_message; END IF;

        -- Add batch with expiry date in the near future (within 90 days)
        CALL sp_add_medicine_batch_from_import_alt(
            p_tenant_id => v_tenant_id,
            p_import_invoice_detail_id => v_import_detail_id_near_expiry,
            p_batch_number => 'NEAR-EXP-BATCH-001',
            p_expiry_date => (CURRENT_DATE + INTERVAL '60 days')::TIMESTAMP WITHOUT TIME ZONE, -- Expires in 60 days
            p_selling_price_per_base_unit => 2000,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC14: Add Batch Message: %', v_sp_message;
        
        -- Get the batch ID
        SELECT b.batch_id INTO v_batch_id_near_expiry 
        FROM batches b
        JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id
        WHERE iid.import_invoice_detail_id = v_import_detail_id_near_expiry LIMIT 1;
        RAISE NOTICE '    SC14: Near-Expiry Batch ID: %', v_batch_id_near_expiry;
        IF v_batch_id_near_expiry IS NULL THEN RAISE EXCEPTION 'SC14: Failed to retrieve batch for near-expiry medicine'; END IF;

        -- Run report to find near-expiry medicines
        RAISE NOTICE '  SC14: Running report to find near-expiry medicines...';
        SELECT COUNT(*) INTO v_report_count 
        FROM fn_report_near_expiry_medicines(v_tenant_id, v_near_expiry_days);
        RAISE NOTICE '    SC14: Near-expiry medicines found: %', v_report_count;
        IF v_report_count = 0 THEN RAISE WARNING 'SC14: No near-expiry medicines found in report, expected at least 1'; END IF;

        -- Verify our specific near-expiry medicine is in the report
        DECLARE
            v_found BOOLEAN := FALSE;
        BEGIN
            SELECT TRUE INTO v_found
            FROM fn_report_near_expiry_medicines(v_tenant_id, v_near_expiry_days)
            WHERE medicine_id = v_medicine_id_near_expiry;
            
            IF v_found THEN
                RAISE NOTICE '    SC14: Near-expiry medicine correctly identified in report.';
            ELSE
                RAISE WARNING 'SC14: Near-expiry medicine not found in report.';
            END IF;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                RAISE WARNING 'SC14: Near-expiry medicine not found in report.';
        END;
    END;

    -- ---- 19. SC15: Medicine Search Test ----
    RAISE NOTICE '---- 19. SC15: Medicine Search Test ----';
    DECLARE
        v_search_term VARCHAR(50);
        v_search_results_count INTEGER;
    BEGIN
        -- Test search by medicine name
        v_search_term := 'Amox';
        RAISE NOTICE '  SC15: Searching for medicines with term "%"...', v_search_term;
        SELECT COUNT(*) INTO v_search_results_count 
        FROM fn_search_medicines(v_tenant_id, v_search_term);
        RAISE NOTICE '    SC15: Search results for "%": %', v_search_term, v_search_results_count;
        IF v_search_results_count = 0 THEN RAISE WARNING 'SC15: No results found for search term "%"', v_search_term; END IF;

        -- Test search by category
        v_search_term := 'Kháng Sinh';
        RAISE NOTICE '  SC15: Searching for medicines with category term "%"...', v_search_term;
        SELECT COUNT(*) INTO v_search_results_count 
        FROM fn_search_medicines(v_tenant_id, v_search_term);
        RAISE NOTICE '    SC15: Search results for category "%": %', v_search_term, v_search_results_count;
        IF v_search_results_count = 0 THEN RAISE WARNING 'SC15: No results found for category search term "%"', v_search_term; END IF;

        -- Test search by registration number
        v_search_term := 'TEST';
        RAISE NOTICE '  SC15: Searching for medicines with registration number containing "%"...', v_search_term;
        SELECT COUNT(*) INTO v_search_results_count 
        FROM fn_search_medicines(v_tenant_id, v_search_term);
        RAISE NOTICE '    SC15: Search results for registration number "%": %', v_search_term, v_search_results_count;
        IF v_search_results_count = 0 THEN RAISE WARNING 'SC15: No results found for registration number search term "%"', v_search_term; END IF;

        -- Test search with no results
        v_search_term := 'NonExistentMedicine123';
        RAISE NOTICE '  SC15: Searching for non-existent medicine "%"...', v_search_term;
        SELECT COUNT(*) INTO v_search_results_count 
        FROM fn_search_medicines(v_tenant_id, v_search_term);
        RAISE NOTICE '    SC15: Search results for non-existent term "%": %', v_search_term, v_search_results_count;
        IF v_search_results_count > 0 THEN RAISE WARNING 'SC15: Unexpected results found for non-existent term "%"', v_search_term; END IF;
    END;

    -- ---- 20. SC16: Batch Tracking Test ----
    RAISE NOTICE '---- 20. SC16: Batch Tracking Test ----';
    DECLARE
        v_medicine_id_batch_track UUID;
        v_packaging_id_batch_track UUID;
        v_import_invoice_id_batch_track UUID;
        v_import_detail_id_batch_track UUID;
        v_batch_id_batch_track_1 UUID;
        v_batch_id_batch_track_2 UUID;
        v_sc16_sale_transaction_id UUID;
        v_sc16_sale_message TEXT;
        v_batch_1_initial_qty NUMERIC;
        v_batch_2_initial_qty NUMERIC;
        v_batch_1_after_sale_qty NUMERIC;
        v_batch_2_after_sale_qty NUMERIC;
    BEGIN
        -- Create medicine for batch tracking
        RAISE NOTICE '  SC16: Creating medicine for batch tracking...';
        CALL sp_create_medicine(
            p_tenant_id => v_tenant_id,
            p_medicine_name => 'BatchTrackMed Test',
            p_description => 'Medicine for batch tracking test',
            p_registration_number => 'BATCHTRACK001',
            p_category_id => v_category_id_khang_sinh,
            p_base_unit => 'Viên',
            p_new_medicine_id => v_medicine_id_batch_track,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC16: Batch Track Medicine ID: %', v_medicine_id_batch_track;
        IF v_medicine_id_batch_track IS NULL THEN RAISE EXCEPTION 'SC16: Failed to create batch tracking test medicine. %', v_sp_message; END IF;

        -- Add packaging unit
        CALL sp_add_medicine_packaging_unit(
            p_tenant_id => v_tenant_id,
            p_medicine_id => v_medicine_id_batch_track,
            p_unit_name => 'Hộp 50 viên',
            p_quantity_per_unit => 50,
            p_is_default_sale_unit => TRUE,
            p_is_default_import_unit => TRUE,
            p_new_packaging_unit_id => v_packaging_id_batch_track,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC16: Batch Track Medicine Packaging ID: %', v_packaging_id_batch_track;
        IF v_packaging_id_batch_track IS NULL THEN RAISE EXCEPTION 'SC16: Failed to add packaging unit for batch tracking medicine. %', v_sp_message; END IF;

        -- Create import invoice
        CALL sp_create_import_invoice(
            p_tenant_id => v_tenant_id,
            p_supplier_id => v_supplier_id,
            p_employee_id => v_employee_id,
            p_invoice_number => 'INV-BATCH-TRACK-TEST',
            p_import_date => CURRENT_DATE,
            p_new_import_invoice_id => v_import_invoice_id_batch_track,
            p_message => v_sp_message,
            p_notes => 'Import for batch tracking test'
        );
        RAISE NOTICE '    SC16: Batch Track Import Invoice ID: %', v_import_invoice_id_batch_track;
        IF v_import_invoice_id_batch_track IS NULL THEN RAISE EXCEPTION 'SC16: Failed to create import invoice for batch tracking. %', v_sp_message; END IF;

        -- Add import detail
        CALL sp_add_import_invoice_detail(
            p_tenant_id => v_tenant_id,
            p_import_invoice_id => v_import_invoice_id_batch_track,
            p_medicine_id => v_medicine_id_batch_track,
            p_packaging_unit_id => v_packaging_id_batch_track,
            p_quantity_imported_in_unit => 4,
            p_price_per_import_unit => 60000,
            p_new_import_invoice_detail_id => v_import_detail_id_batch_track,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC16: Batch Track Import Detail ID: %', v_import_detail_id_batch_track;
        IF v_import_detail_id_batch_track IS NULL THEN RAISE EXCEPTION 'SC16: Failed to add import detail for batch tracking. %', v_sp_message; END IF;

        -- Add first batch
        CALL sp_add_medicine_batch_from_import_alt(
            p_tenant_id => v_tenant_id,
            p_import_invoice_detail_id => v_import_detail_id_batch_track,
            p_batch_number => 'BATCH-TRACK-001',
            p_expiry_date => (CURRENT_DATE + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE,
            p_selling_price_per_base_unit => 1500,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC16: Add First Batch Message: %', v_sp_message;
        
        -- Get the first batch ID
        SELECT b.batch_id INTO v_batch_id_batch_track_1 
        FROM batches b
        JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id
        WHERE iid.import_invoice_detail_id = v_import_detail_id_batch_track 
        AND b.batch_number = 'BATCH-TRACK-001'
        LIMIT 1;
        RAISE NOTICE '    SC16: First Batch ID: %', v_batch_id_batch_track_1;
        IF v_batch_id_batch_track_1 IS NULL THEN RAISE EXCEPTION 'SC16: Failed to retrieve first batch for batch tracking'; END IF;

        -- Add second batch with different import detail
        CALL sp_add_import_invoice_detail(
            p_tenant_id => v_tenant_id,
            p_import_invoice_id => v_import_invoice_id_batch_track,
            p_medicine_id => v_medicine_id_batch_track,
            p_packaging_unit_id => v_packaging_id_batch_track,
            p_quantity_imported_in_unit => 2,
            p_price_per_import_unit => 58000,
            p_new_import_invoice_detail_id => v_import_detail_id_batch_track,
            p_message => v_sp_message
        );
        
        CALL sp_add_medicine_batch_from_import_alt(
            p_tenant_id => v_tenant_id,
            p_import_invoice_detail_id => v_import_detail_id_batch_track,
            p_batch_number => 'BATCH-TRACK-002',
            p_expiry_date => (CURRENT_DATE + INTERVAL '2 years')::TIMESTAMP WITHOUT TIME ZONE,
            p_selling_price_per_base_unit => 1550,
            p_message => v_sp_message
        );
        RAISE NOTICE '    SC16: Add Second Batch Message: %', v_sp_message;
        
        -- Get the second batch ID
        SELECT b.batch_id INTO v_batch_id_batch_track_2 
        FROM batches b
        JOIN import_invoice_details iid ON b.import_invoice_detail_id = iid.import_invoice_detail_id
        WHERE iid.import_invoice_detail_id = v_import_detail_id_batch_track 
        AND b.batch_number = 'BATCH-TRACK-002'
        LIMIT 1;
        RAISE NOTICE '    SC16: Second Batch ID: %', v_batch_id_batch_track_2;
        IF v_batch_id_batch_track_2 IS NULL THEN RAISE EXCEPTION 'SC16: Failed to retrieve second batch for batch tracking'; END IF;

        -- Get initial quantities
        SELECT quantity_remaining INTO v_batch_1_initial_qty FROM batches WHERE batch_id = v_batch_id_batch_track_1;
        SELECT quantity_remaining INTO v_batch_2_initial_qty FROM batches WHERE batch_id = v_batch_id_batch_track_2;
        RAISE NOTICE '    SC16: Initial Batch 1 Quantity: %', v_batch_1_initial_qty;
        RAISE NOTICE '    SC16: Initial Batch 2 Quantity: %', v_batch_2_initial_qty;

        -- Process sale with specific batch selection
        RAISE NOTICE '  SC16: Processing sale with specific batch selection...';
        CALL sp_process_sale_transaction_with_batch_selection(
            p_tenant_id => v_tenant_id,
            p_employee_id => v_employee_id,
            p_customer_id => NULL,
            p_prescription_id => NULL,
            p_payment_method_id => v_payment_method_id_cash,
            p_sale_items => jsonb_build_array(
                jsonb_build_object(
                    'medicine_id', v_medicine_id_batch_track, 
                    'batch_id', v_batch_id_batch_track_1,
                    'quantity_requested_in_base_unit', 25
                ),
                jsonb_build_object(
                    'medicine_id', v_medicine_id_batch_track, 
                    'batch_id', v_batch_id_batch_track_2,
                    'quantity_requested_in_base_unit', 15
                )
            ),
            p_transaction_status_id => v_status_id_completed,
            p_notes => 'SC16: Sale with specific batch selection',
            p_new_transaction_id => v_sc16_sale_transaction_id,
            p_message => v_sc16_sale_message
        );
        RAISE NOTICE '    SC16: Sale Transaction ID: %', v_sc16_sale_transaction_id;
        RAISE NOTICE '    SC16: Sale Message: %', v_sc16_sale_message;
        IF v_sc16_sale_transaction_id IS NULL THEN RAISE EXCEPTION 'SC16: Sale with batch selection failed: %', v_sc16_sale_message; END IF;

        -- Verify batch quantities after sale
        SELECT quantity_remaining INTO v_batch_1_after_sale_qty FROM batches WHERE batch_id = v_batch_id_batch_track_1;
        SELECT quantity_remaining INTO v_batch_2_after_sale_qty FROM batches WHERE batch_id = v_batch_id_batch_track_2;
        RAISE NOTICE '    SC16: Batch 1 Quantity After Sale: % (Expected: % - 25 = %)', 
            v_batch_1_after_sale_qty, v_batch_1_initial_qty, (v_batch_1_initial_qty - 25);
        RAISE NOTICE '    SC16: Batch 2 Quantity After Sale: % (Expected: % - 15 = %)', 
            v_batch_2_after_sale_qty, v_batch_2_initial_qty, (v_batch_2_initial_qty - 15);
        
        IF v_batch_1_after_sale_qty <> (v_batch_1_initial_qty - 25) THEN 
            RAISE WARNING 'SC16: Batch 1 quantity % does not match expected %', 
                v_batch_1_after_sale_qty, (v_batch_1_initial_qty - 25); 
        END IF;
        
        IF v_batch_2_after_sale_qty <> (v_batch_2_initial_qty - 15) THEN 
            RAISE WARNING 'SC16: Batch 2 quantity % does not match expected %', 
                v_batch_2_after_sale_qty, (v_batch_2_initial_qty - 15); 
        END IF;

        -- Verify transaction details
        DECLARE
            v_detail_count INTEGER;
            v_batch_1_qty NUMERIC;
            v_batch_2_qty NUMERIC;
        BEGIN
            SELECT COUNT(*) INTO v_detail_count 
            FROM sales_transaction_details 
            WHERE sale_transaction_id = v_sc16_sale_transaction_id AND tenant_id = v_tenant_id;
            
            RAISE NOTICE '    SC16: Number of transaction details: % (Expected: 2)', v_detail_count;
            IF v_detail_count <> 2 THEN RAISE WARNING 'SC16: Expected 2 transaction details, got %', v_detail_count; END IF;
            
            SELECT quantity_sold_in_base_unit INTO v_batch_1_qty 
            FROM sales_transaction_details 
            WHERE sale_transaction_id = v_sc16_sale_transaction_id AND batch_id = v_batch_id_batch_track_1;
            
            SELECT quantity_sold_in_base_unit INTO v_batch_2_qty 
            FROM sales_transaction_details 
            WHERE sale_transaction_id = v_sc16_sale_transaction_id AND batch_id = v_batch_id_batch_track_2;
            
            RAISE NOTICE '    SC16: Batch 1 Quantity in Transaction: % (Expected: 25)', v_batch_1_qty;
            RAISE NOTICE '    SC16: Batch 2 Quantity in Transaction: % (Expected: 15)', v_batch_2_qty;
            
            IF v_batch_1_qty <> 25 THEN RAISE WARNING 'SC16: Batch 1 quantity in transaction is %, expected 25', v_batch_1_qty; END IF;
            IF v_batch_2_qty <> 15 THEN RAISE WARNING 'SC16: Batch 2 quantity in transaction is %, expected 15', v_batch_2_qty; END IF;
        END;
    END;

    RAISE NOTICE '========== ALL PHARMACY SALES FLOW TESTS COMPLETED SUCCESSFULLY =========';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An unexpected error occurred during the test script: %', SQLERRM;
        RAISE NOTICE 'SQLSTATE: %', SQLSTATE;
        RAISE NOTICE '========== TEST FAILED DUE TO UNEXPECTED ERROR =========';
        RAISE; -- Re-raise the exception to make the test fail clearly
END $$;
