-- Enable UUID generation if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table: tenants (<PERSON><PERSON><PERSON> đơn vị/nhà thuốc thuê bao)
-- Each tenant is an independent pharmacy entity.
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_name VARCHAR(255) NOT NULL UNIQUE, -- Tên nhà thuốc/đơn vị
    contact_person VARCHAR(255), -- <PERSON><PERSON><PERSON><PERSON> liên hệ chính
    email VARCHAR(255) UNIQUE, -- Email quản lý
    phone_number VARCHAR(50),
    address TEXT,
    subscription_start_date DATE NOT NULL,
    subscription_end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP -- For auto-update on change, a trigger is needed in PostgreSQL
);

-- Table: categories (<PERSON>h mục thuốc)
-- Categories for medicines, specific to each tenant.
CREATE TABLE categories (
    category_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    category_name VARCHAR(255) NOT NULL, -- Tên danh mục (e.g., 'Kháng sinh', 'Giảm đau', 'Vitamin')
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    PRIMARY KEY (category_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, category_name) -- Category names must be unique within a tenant
);

-- Table: suppliers (Nhà cung cấp)
-- Information about medicine suppliers, specific to each tenant.
CREATE TABLE suppliers (
    supplier_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    supplier_name VARCHAR(255) NOT NULL, -- Tên nhà cung cấp
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone_number VARCHAR(50),
    address TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    PRIMARY KEY (supplier_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, supplier_name)
);

-- Table: medicines (Thuốc)
-- The main medicines table, linked to tenants and categories.
CREATE TABLE medicines (
    medicine_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    medicine_name VARCHAR(255) NOT NULL,
    registration_number VARCHAR(50), -- Số đăng ký (SĐK) của thuốc
    vat_rate NUMERIC(4,2) DEFAULT 0.00, -- Tỷ lệ Thuế VAT (e.g., 0.05 for 5%)
    category_id UUID, -- Nullable if a medicine doesn't fit a category initially
    base_unit VARCHAR(50) NOT NULL, -- e.g., 'Viên', 'Chai', 'Tuýp', 'Hộp' (smallest unit of sale/dispensing)
    description TEXT,
    storage_instructions VARCHAR(255),
    is_prescription_required BOOLEAN DEFAULT FALSE,
    manufacturer VARCHAR(255), -- Manufacturer of the medicine
    country_of_origin VARCHAR(100), -- Country where the medicine is manufactured
    active_ingredients TEXT, -- Key active ingredients
    is_active BOOLEAN DEFAULT TRUE, -- Added
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (medicine_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id, tenant_id) REFERENCES categories(category_id, tenant_id) ON DELETE SET NULL, -- If category is deleted, medicine remains
    UNIQUE (tenant_id, medicine_name), -- Medicine names must be unique within a tenant
    UNIQUE (tenant_id, registration_number) -- SĐK phải là duy nhất trong một Tenant nếu được cung cấp
);

-- Table: medicine_packaging_units (Đơn vị đóng gói của thuốc)
-- Defines different packaging units for a medicine (e.g., Box of 10 blisters, Blister of 10 tablets).
CREATE TABLE medicine_packaging_units (
    packaging_unit_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    medicine_id UUID NOT NULL,
    unit_name VARCHAR(100) NOT NULL, -- Tên đơn vị đóng gói (e.g., 'Hộp', 'Vỉ', 'Chai 100ml')
    quantity_per_unit INTEGER NOT NULL, -- Số lượng đơn vị cơ sở trong một đơn vị đóng gói này (e.g., 1 hộp = 10 vỉ, 1 vỉ = 10 viên -> so với BaseUnit 'Viên')
    is_default_sale_unit BOOLEAN DEFAULT FALSE, -- Đơn vị bán mặc định?
    is_default_import_unit BOOLEAN DEFAULT FALSE, -- Đơn vị nhập mặc định?
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (packaging_unit_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE, -- Added for consistency
    FOREIGN KEY (medicine_id, tenant_id) REFERENCES medicines(medicine_id, tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, medicine_id, unit_name)
);

-- Table: batches (Lô thuốc)
-- Tracks individual batches of medicines, including expiry and quantity.
CREATE TABLE batches (
    batch_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    medicine_id UUID NOT NULL,
    supplier_id UUID, -- Can be null if supplier is not tracked or direct import
    import_invoice_detail_id UUID, -- Mã chi tiết hóa đơn nhập liên quan (nếu có)
    batch_number VARCHAR(100), -- Số lô sản xuất. Có thể không duy nhất toàn hệ thống, nhưng duy nhất cho một MedicineID+TenantID+ExpiryDate
    import_date DATE DEFAULT CURRENT_DATE, -- Ngày nhập lô hàng này (có thể khác ngày trên hóa đơn)
    expiry_date DATE NOT NULL, -- Hạn sử dụng của lô
    quantity_imported NUMERIC(10,2) NOT NULL, -- Số lượng nhập ban đầu (tính theo BaseUnit của Medicine)
    quantity_remaining NUMERIC(10,2) NOT NULL, -- Số lượng còn lại (tính theo BaseUnit)
    import_price_per_base_unit NUMERIC(12,2) NOT NULL, -- Giá nhập mỗi đơn vị cơ sở
    selling_price_per_base_unit NUMERIC(12,2) NOT NULL, -- Giá bán mỗi đơn vị cơ sở (có thể thay đổi theo chính sách)
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    PRIMARY KEY (batch_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE, -- Added for consistency
    FOREIGN KEY (medicine_id, tenant_id) REFERENCES medicines(medicine_id, tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id, tenant_id) REFERENCES suppliers(supplier_id, tenant_id) ON DELETE SET NULL -- If supplier is deleted, batch remains
    -- FK to ImportInvoiceDetails will be added via ALTER TABLE after ImportInvoiceDetails is defined
);

-- Table: import_invoices (Hóa đơn nhập hàng)
-- Header information for import invoices.
CREATE TABLE import_invoices (
    import_invoice_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    supplier_id UUID, -- Liên kết với nhà cung cấp
    employee_id UUID, -- Nhân viên chịu trách nhiệm nhập hàng, nullable if not tracked (FK defined later)
    invoice_number VARCHAR(100) NOT NULL,
    import_date DATE NOT NULL DEFAULT CURRENT_DATE,
    total_amount NUMERIC(15,2) DEFAULT 0.00, -- Tổng tiền trên hóa đơn (có thể tính từ details sau)
    notes TEXT,
    status VARCHAR(50) DEFAULT 'PENDING', -- e.g., PENDING, COMPLETED, CANCELLED
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (import_invoice_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id, tenant_id) REFERENCES suppliers(supplier_id, tenant_id) ON DELETE SET NULL
    -- FK to Employees will be added via ALTER TABLE or ensured Employees is defined before this table
);

-- Table: import_invoice_details (Chi tiết hóa đơn nhập hàng)
-- Line items for each import invoice.
CREATE TABLE import_invoice_details (
    import_invoice_detail_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    import_invoice_id UUID NOT NULL,
    medicine_id UUID NOT NULL,
    packaging_unit_id UUID NOT NULL,
    quantity_imported_in_unit NUMERIC(10,2) NOT NULL, -- Số lượng nhập theo đơn vị đóng gói khi nhập
    quantity_imported_in_base_unit NUMERIC(10,2) NOT NULL, -- Tổng số lượng quy đổi về đơn vị cơ sở
    price_per_import_unit NUMERIC(12, 2) NOT NULL, -- Giá mỗi đơn vị khi nhập
    total_amount_for_item NUMERIC(12, 2) NOT NULL, -- Thành tiền cho mục này
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (import_invoice_detail_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (import_invoice_id, tenant_id) REFERENCES import_invoices(import_invoice_id, tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (medicine_id, tenant_id) REFERENCES medicines(medicine_id, tenant_id) ON DELETE RESTRICT, -- Prevent deleting medicine if it's in an invoice detail
    FOREIGN KEY (packaging_unit_id, tenant_id) REFERENCES medicine_packaging_units(packaging_unit_id, tenant_id) ON DELETE RESTRICT
);

-- Add the deferred foreign key to batches now that import_invoice_details is defined
ALTER TABLE batches
ADD CONSTRAINT fk_batch_import_invoice_detail
FOREIGN KEY (import_invoice_detail_id, tenant_id)
REFERENCES import_invoice_details(import_invoice_detail_id, tenant_id) ON DELETE SET NULL;

-- Table: roles (Vai trò người dùng)
CREATE TABLE roles (
    role_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    role_name VARCHAR(100) NOT NULL, -- Tên vai trò (e.g., 'Quản lý', 'Dược sĩ chính', 'Nhân viên bán hàng')
    description TEXT, -- Mô tả vai trò
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    PRIMARY KEY (role_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, role_name)
);

-- Table: permissions (Quyền hạn)
-- Các quyền này thường là cố định trong hệ thống và không theo Tenant.
CREATE TABLE permissions (
    permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    permission_name VARCHAR(255) NOT NULL UNIQUE, -- Tên quyền (e.g., 'ViewSalesReports', 'EditMedicines', 'ProcessSales')
    description TEXT -- Mô tả quyền
);

-- Table: role_permissions (Quyền của Vai trò)
-- Liên kết giữa vai trò (theo Tenant) và các quyền hạn hệ thống.
CREATE TABLE role_permissions (
    role_permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    FOREIGN KEY (role_id, tenant_id) REFERENCES roles(role_id, tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, role_id, permission_id) -- Đảm bảo một vai trò không có cùng một quyền nhiều lần trong một tenant
);

-- Table: employees (Nhân viên)
-- Manages pharmacy staff information and system access.
CREATE TABLE employees (
    employee_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    role_id UUID, -- Liên kết với bảng Roles. Có thể NULL nếu nhân viên chưa được gán vai trò cụ thể.
    full_name VARCHAR(255) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10), -- e.g., 'Male', 'Female', 'Other'
    address TEXT,
    phone_number VARCHAR(50),
    email VARCHAR(255), -- Email cũng có thể dùng để đăng nhập
    username VARCHAR(100) UNIQUE, -- Tên đăng nhập hệ thống, nếu có
    password_hash TEXT, -- Lưu trữ mật khẩu đã hash
    is_active BOOLEAN DEFAULT TRUE,
    hire_date DATE,
    termination_date DATE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (employee_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id, tenant_id) REFERENCES roles(role_id, tenant_id) ON DELETE SET NULL -- If role is deleted, employee's role is set to NULL
);

-- Now that employees table is defined, add FK constraint to import_invoices
ALTER TABLE import_invoices
ADD CONSTRAINT fk_import_invoice_employee
FOREIGN KEY (employee_id, tenant_id)
REFERENCES employees(employee_id, tenant_id) ON DELETE SET NULL;

-- Table: loyalty_tiers (Hạng Thành Viên Khách Hàng)
CREATE TABLE loyalty_tiers (
    loyalty_tier_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    tier_name VARCHAR(100) NOT NULL, -- Tên hạng (e.g., 'Standard', 'Silver', 'Gold', 'Platinum')
    min_points_required NUMERIC(10,2) DEFAULT 0.00, -- Điểm tối thiểu để đạt hạng này
    discount_percentage NUMERIC(5,2) DEFAULT 0.00, -- % giảm giá cho hạng này
    other_benefits TEXT, -- Các quyền lợi khác
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    PRIMARY KEY (loyalty_tier_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, tier_name)
);

-- Table: customers (Khách hàng)
-- Stores information about pharmacy customers.
CREATE TABLE customers (
    customer_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    full_name VARCHAR(255) NOT NULL, -- Họ và tên khách hàng
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    phone_number VARCHAR(50), -- Số điện thoại, có thể dùng làm key định danh nếu UNIQUE
    email VARCHAR(255),
    loyalty_points NUMERIC(10,2) DEFAULT 0.00, -- Điểm tích lũy
    loyalty_tier_id UUID, -- Hạng thành viên (nếu có)
    referred_by_customer_id UUID NULL, -- ID của khách hàng đã giới thiệu (nếu có)
    is_active BOOLEAN DEFAULT TRUE, -- <<< ADDED THIS LINE
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (customer_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (loyalty_tier_id, tenant_id) REFERENCES loyalty_tiers(loyalty_tier_id, tenant_id) ON DELETE SET NULL,
    FOREIGN KEY (referred_by_customer_id, tenant_id) REFERENCES customers(customer_id, tenant_id) ON DELETE SET NULL, -- Self-referencing for referrals
    UNIQUE (tenant_id, phone_number) -- Số điện thoại nên là duy nhất trong một Tenant nếu dùng làm key
);

-- Table: prescriptions (Đơn thuốc)
-- Stores prescription information issued by doctors.
CREATE TABLE prescriptions (
    prescription_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    customer_id UUID, -- Liên kết với khách hàng (nếu có, đơn thuốc có thể của khách vãng lai)
    doctor_name VARCHAR(255), -- Tên bác sĩ kê đơn
    clinic_address TEXT, -- Địa chỉ phòng khám
    prescription_date DATE NOT NULL DEFAULT CURRENT_DATE,
    diagnosis TEXT, -- Chẩn đoán
    notes TEXT, -- Ghi chú thêm của bác sĩ
    is_dispensed BOOLEAN DEFAULT FALSE, -- Đơn đã được xuất thuốc hoàn toàn hay chưa?
    employee_id UUID, -- Nhân viên xử lý đơn này (nếu có)
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (prescription_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id, tenant_id) REFERENCES employees(employee_id, tenant_id) ON DELETE SET NULL,
    FOREIGN KEY (customer_id, tenant_id) REFERENCES customers(customer_id, tenant_id) ON DELETE SET NULL
);

-- Table: prescription_details (Chi tiết đơn thuốc)
-- Lists each medicine prescribed in a prescription.
CREATE TABLE prescription_details (
    prescription_detail_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    prescription_id UUID NOT NULL,
    medicine_id UUID NOT NULL,
    dosage VARCHAR(255), -- Liều lượng (e.g., 'Sáng 1 viên, tối 1 viên')
    quantity_prescribed NUMERIC(10,2), -- Số lượng kê đơn (tính theo BaseUnit của Medicine)
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (prescription_detail_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (prescription_id, tenant_id) REFERENCES prescriptions(prescription_id, tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (medicine_id, tenant_id) REFERENCES medicines(medicine_id, tenant_id) ON DELETE RESTRICT -- Không cho xóa thuốc nếu đã được kê đơn
);

-- Table: payment_methods (Phương thức thanh toán)
-- Stores different payment methods available.
CREATE TABLE payment_methods (
    payment_method_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    method_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (payment_method_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, method_name) -- Ensure method names are unique per tenant
);

-- Table: transaction_statuses (Trạng thái giao dịch)
-- Stores different statuses a transaction can have (e.g., Pending, Completed, Cancelled).
CREATE TABLE transaction_statuses (
    transaction_status_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    status_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (transaction_status_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, status_name) -- Ensure status names are unique per tenant
);

-- Table: sales_transactions (Giao dịch bán hàng)
-- Records each sale transaction, whether OTC or prescription-based.
CREATE TABLE sales_transactions (
    sale_transaction_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    employee_id UUID NOT NULL, -- Nhân viên thực hiện giao dịch
    customer_id UUID, -- Khách hàng (nếu có, có thể là khách lẻ không lưu thông tin)
    prescription_id UUID, -- Đơn thuốc liên quan (nếu bán theo đơn)
    transaction_date TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    total_amount NUMERIC(15,2) NOT NULL, -- Tổng tiền giao dịch
    payment_method_id UUID, -- Changed from VARCHAR(50) payment_method to FK
    transaction_status_id UUID, -- Added to store transaction status ID
    notes TEXT, -- Ghi chú cho giao dịch
    points_earned NUMERIC(10,2) DEFAULT 0.00, -- Điểm tích lũy từ giao dịch này
    discount_applied NUMERIC(12,2) DEFAULT 0.00, -- Số tiền giảm giá
    final_amount NUMERIC(15,2) GENERATED ALWAYS AS (total_amount - discount_applied) STORED,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (sale_transaction_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id, tenant_id) REFERENCES employees(employee_id, tenant_id) ON DELETE RESTRICT,
    FOREIGN KEY (customer_id, tenant_id) REFERENCES customers(customer_id, tenant_id) ON DELETE SET NULL,
    FOREIGN KEY (prescription_id, tenant_id) REFERENCES prescriptions(prescription_id, tenant_id) ON DELETE SET NULL,
    FOREIGN KEY (payment_method_id, tenant_id) REFERENCES payment_methods(payment_method_id, tenant_id) ON DELETE SET NULL, -- Added FK for payment_method_id
    FOREIGN KEY (transaction_status_id, tenant_id) REFERENCES transaction_statuses(transaction_status_id, tenant_id) ON DELETE SET NULL -- Added FK for transaction_status_id
);

-- Table: sales_transaction_details (Chi tiết giao dịch bán hàng)
-- Lists each medicine item sold in a transaction, linking to the specific batch.
CREATE TABLE sales_transaction_details (
    sale_transaction_detail_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    sale_transaction_id UUID NOT NULL,
    batch_id UUID NOT NULL, -- Quan trọng: Liên kết tới lô thuốc cụ thể đã bán
    medicine_id UUID NOT NULL, -- Để dễ truy vấn, dù thông tin này có trong Batch
    quantity_sold_in_base_unit NUMERIC(10,2) NOT NULL, -- Số lượng bán (tính theo BaseUnit)
    unit_price NUMERIC(12,2) NOT NULL, -- Giá bán mỗi BaseUnit tại thời điểm giao dịch
    subtotal NUMERIC(14,2) GENERATED ALWAYS AS (quantity_sold_in_base_unit * unit_price) STORED,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (sale_transaction_detail_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (sale_transaction_id, tenant_id) REFERENCES sales_transactions(sale_transaction_id, tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id, tenant_id) REFERENCES batches(batch_id, tenant_id) ON DELETE RESTRICT, -- Không cho xóa lô nếu đã có giao dịch
    FOREIGN KEY (medicine_id, tenant_id) REFERENCES medicines(medicine_id, tenant_id) ON DELETE RESTRICT -- Không cho xóa thuốc nếu đã có giao dịch
);

-- Table: invoice_payments (Thanh toán hóa đơn)
-- Records payments made against sales transactions/invoices.
CREATE TABLE invoice_payments (
    invoice_payment_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    sale_transaction_id UUID NOT NULL,
    payment_method_id UUID NOT NULL,
    payment_date TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    amount_paid NUMERIC(15,2) NOT NULL,
    reference_number VARCHAR(255), -- e.g., check number, online transaction ID
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (invoice_payment_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (sale_transaction_id, tenant_id) REFERENCES sales_transactions(sale_transaction_id, tenant_id) ON DELETE CASCADE, -- Cascade delete if transaction is removed
    FOREIGN KEY (payment_method_id, tenant_id) REFERENCES payment_methods(payment_method_id, tenant_id) ON DELETE RESTRICT -- Restrict delete if payment method is in use
);

-- Table: inventory_adjustment_types (Loại điều chỉnh tồn kho)
-- Defines different types of inventory adjustments (e.g., Damaged, Expired, Recount).
CREATE TABLE inventory_adjustment_types (
    adjustment_type_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    action VARCHAR(20) NOT NULL, -- e.g., 'INCREASE', 'DECREASE', 'SET_TO' (determines effect on quantity)
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (adjustment_type_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    UNIQUE (tenant_id, type_name),
    CONSTRAINT chk_adjustment_action CHECK (action IN ('INCREASE', 'DECREASE', 'SET_TO'))
);

-- Table: inventory_adjustments (Điều chỉnh tồn kho)
-- Records manual adjustments to inventory levels (e.g., due to damage, expiry, stock count corrections).
CREATE TABLE inventory_adjustments (
    adjustment_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    batch_id UUID NOT NULL,
    employee_id UUID NOT NULL,
    adjustment_type_id UUID NOT NULL,
    adjustment_date TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    quantity_change NUMERIC(10,2) NOT NULL, -- Số lượng thay đổi (+/- , tính theo BaseUnit)
    reason TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Added
    PRIMARY KEY (adjustment_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id, tenant_id) REFERENCES batches(batch_id, tenant_id) ON DELETE RESTRICT,
    FOREIGN KEY (employee_id, tenant_id) REFERENCES employees(employee_id, tenant_id) ON DELETE RESTRICT,
    FOREIGN KEY (adjustment_type_id, tenant_id) REFERENCES inventory_adjustment_types(adjustment_type_id, tenant_id) ON DELETE RESTRICT
);

-- Table: sales_invoices (Hóa đơn bán hàng)
-- Stores formal invoices issued to customers, potentially linked to sales transactions or for credit sales.
CREATE TABLE sales_invoices (
    sales_invoice_id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    sale_transaction_id UUID, -- Optional: link to a specific point-of-sale transaction
    customer_id UUID NOT NULL,
    invoice_number VARCHAR(100) NOT NULL, -- Official invoice number
    invoice_date TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMP WITHOUT TIME ZONE,
    subtotal_amount NUMERIC(15,2) DEFAULT 0.00,
    tax_amount NUMERIC(15,2) DEFAULT 0.00,
    total_amount NUMERIC(15,2) NOT NULL,
    amount_paid NUMERIC(15,2) DEFAULT 0.00,
    balance_due NUMERIC(15,2) GENERATED ALWAYS AS (total_amount - amount_paid) STORED,
    status VARCHAR(50) DEFAULT 'Draft', -- e.g., Draft, Sent, Paid, Partially Paid, Overdue, Cancelled
    notes TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (sales_invoice_id, tenant_id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (sale_transaction_id, tenant_id) REFERENCES sales_transactions(sale_transaction_id, tenant_id) ON DELETE SET NULL,
    FOREIGN KEY (customer_id, tenant_id) REFERENCES customers(customer_id, tenant_id) ON DELETE RESTRICT,
    UNIQUE (tenant_id, invoice_number) -- Invoice number should be unique per tenant
);

-- AUTO-UPDATE UpdatedAt Trigger Function and Triggers --

-- 1. Create a generic trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Apply the trigger to tables with an updated_at column

-- Trigger for tenants table
CREATE TRIGGER trigger_tenants_updated_at
BEFORE UPDATE ON tenants
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for employees table
CREATE TRIGGER trigger_employees_updated_at
BEFORE UPDATE ON employees
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for medicines table
CREATE TRIGGER trigger_medicines_updated_at
BEFORE UPDATE ON medicines
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for medicine_packaging_units table
CREATE TRIGGER trigger_medicine_packaging_units_updated_at
BEFORE UPDATE ON medicine_packaging_units
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for import_invoices table
CREATE TRIGGER trigger_import_invoices_updated_at
BEFORE UPDATE ON import_invoices
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for import_invoice_details table
CREATE TRIGGER trigger_import_invoice_details_updated_at
BEFORE UPDATE ON import_invoice_details
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for batches table
CREATE TRIGGER trigger_batches_updated_at
BEFORE UPDATE ON batches
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Trigger for users table
-- CREATE TRIGGER trigger_users_updated_at
-- BEFORE UPDATE ON users
-- FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- Trigger for roles table
CREATE TRIGGER trigger_roles_updated_at
BEFORE UPDATE ON roles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for customers table
CREATE TRIGGER trigger_customers_updated_at
BEFORE UPDATE ON customers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for loyalty_tiers table
CREATE TRIGGER trigger_loyalty_tiers_updated_at
BEFORE UPDATE ON loyalty_tiers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for suppliers table
CREATE TRIGGER trigger_suppliers_updated_at
BEFORE UPDATE ON suppliers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for categories table
CREATE TRIGGER trigger_categories_updated_at
BEFORE UPDATE ON categories
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for inventory_adjustments table
CREATE TRIGGER trigger_inventory_adjustments_updated_at
BEFORE UPDATE ON inventory_adjustments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for inventory_adjustment_types table
CREATE TRIGGER trigger_inventory_adjustment_types_updated_at
BEFORE UPDATE ON inventory_adjustment_types
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for sales_invoices table
CREATE TRIGGER trigger_sales_invoices_updated_at
BEFORE UPDATE ON sales_invoices
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for payment_methods table
CREATE TRIGGER trigger_payment_methods_updated_at
BEFORE UPDATE ON payment_methods
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for transaction_statuses table
CREATE TRIGGER trigger_transaction_statuses_updated_at
BEFORE UPDATE ON transaction_statuses
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for invoice_payments table
CREATE TRIGGER trigger_invoice_payments_updated_at
BEFORE UPDATE ON invoice_payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for prescription_details table
CREATE TRIGGER trigger_prescription_details_updated_at
BEFORE UPDATE ON prescription_details
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Reminder: If other tables are added or modified to include an updated_at column,
-- a similar trigger should be created for them.

-- Example of how to add a trigger for a new table 'example_table':
-- CREATE TABLE example_table (
--     example_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
--     data TEXT,
--     created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
-- );
-- CREATE TRIGGER trigger_exampletable_updated_at
-- BEFORE UPDATE ON example_table
-- FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- Additional Triggers and Functions --

-- Trigger Function and Trigger to prevent negative stock in batches
CREATE OR REPLACE FUNCTION fn_check_negative_stock_on_batch_update()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.quantity_remaining < 0 THEN
        RAISE EXCEPTION 'Batch quantity remaining cannot be negative. Batch ID: %, Medicine ID: %, Attempted Quantity Remaining: %',
                        NEW.batch_id, NEW.medicine_id, NEW.quantity_remaining;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_before_batch_check_negative_stock
BEFORE UPDATE ON batches
FOR EACH ROW
WHEN (OLD.quantity_remaining IS DISTINCT FROM NEW.quantity_remaining) -- Only run if quantity_remaining changes
EXECUTE FUNCTION fn_check_negative_stock_on_batch_update();

-- Function to calculate total stock for a medicine (considering non-expired batches with positive stock)
CREATE OR REPLACE FUNCTION fn_calculate_medicine_total_stock(
    p_tenant_id UUID,
    p_medicine_id UUID
)
RETURNS NUMERIC AS $$
DECLARE
    v_total_stock NUMERIC;
BEGIN
    SELECT COALESCE(SUM(b.quantity_remaining), 0)
    INTO v_total_stock
    FROM batches b
    WHERE b.tenant_id = p_tenant_id
      AND b.medicine_id = p_medicine_id
      AND b.quantity_remaining > 0
      AND b.expiry_date >= CURRENT_DATE; -- Only consider non-expired batches

    RETURN v_total_stock;
END;
$$ LANGUAGE plpgsql;

-- Function to check stock availability for a specific batch
CREATE OR REPLACE FUNCTION fn_check_batch_stock_availability(
    p_tenant_id UUID,
    p_batch_id UUID,
    p_quantity_needed NUMERIC
)
RETURNS BOOLEAN AS $$
DECLARE
    v_quantity_remaining NUMERIC;
BEGIN
    SELECT b.quantity_remaining
    INTO v_quantity_remaining
    FROM batches b
    WHERE b.batch_id = p_batch_id
      AND b.tenant_id = p_tenant_id
      AND b.expiry_date >= CURRENT_DATE; -- Ensure batch is not expired

    IF NOT FOUND THEN
        -- Batch not found or expired
        RETURN FALSE;
    END IF;

    RETURN v_quantity_remaining >= p_quantity_needed;
END;
$$ LANGUAGE plpgsql;

-- Trigger Function and Trigger to update batch quantity after a sales transaction detail is inserted
CREATE OR REPLACE FUNCTION fn_update_batch_quantity_after_sale()
RETURNS TRIGGER AS $$
BEGIN
    -- NEW refers to the row being inserted into sales_transaction_details
    UPDATE batches
    SET quantity_remaining = quantity_remaining - NEW.quantity_sold_in_base_unit,
        updated_at = CURRENT_TIMESTAMP
    WHERE batch_id = NEW.batch_id
      AND tenant_id = NEW.tenant_id;

    -- Check if quantity_remaining went negative, which ideally should be prevented by sp_process_sale_transaction
    -- or a BEFORE trigger on batches if direct updates were allowed that could bypass sp_process_sale_transaction logic.
    -- For now, sp_process_sale_transaction should prevent this scenario.
    IF (SELECT b.quantity_remaining FROM batches b WHERE b.batch_id = NEW.batch_id AND b.tenant_id = NEW.tenant_id) < 0 THEN
        RAISE EXCEPTION 'Batch quantity for batch ID % became negative after sale. Transaction for detail ID % is problematic.', NEW.batch_id, NEW.sale_transaction_detail_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_after_sales_detail_insert_update_batch_qty
AFTER INSERT ON sales_transaction_details
FOR EACH ROW
EXECUTE FUNCTION fn_update_batch_quantity_after_sale();

-- Trigger Function and Trigger to update batch quantity after an inventory adjustment is inserted
CREATE OR REPLACE FUNCTION fn_update_batch_quantity_after_adjustment()
RETURNS TRIGGER AS $$
BEGIN
    -- Update stock in the batches table based on the quantity_change
    -- quantity_change can be positive (stock increase) or negative (stock decrease)
    UPDATE batches
    SET quantity_remaining = quantity_remaining + NEW.quantity_change
    WHERE batch_id = NEW.batch_id AND tenant_id = NEW.tenant_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Batch ID % for tenant % not found during inventory adjustment insert. Cannot update stock.', NEW.batch_id, NEW.tenant_id;
    END IF;
    -- The trigger 'trg_before_batch_check_negative_stock' on 'batches' table
    -- will prevent quantity_remaining from becoming negative if NEW.quantity_change is too large a negative number.
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_after_inventory_adjustment_insert_update_batch_qty
AFTER INSERT ON inventory_adjustments
FOR EACH ROW
EXECUTE FUNCTION fn_update_batch_quantity_after_adjustment();

-- Stored Procedures for Inventory and Import Management --

-- Procedure to add a new medicine
CREATE OR REPLACE PROCEDURE sp_add_medicine(
    p_tenant_id UUID,
    p_medicine_name VARCHAR(255),
    p_description TEXT,
    p_category_id UUID, -- Still assuming 'categories' table exists for this
    p_manufacturer_name VARCHAR(255), -- Changed from p_manufacturer_id UUID
    p_base_unit_of_measure VARCHAR(50),
    OUT p_new_medicine_id UUID,
    p_registration_number VARCHAR(50) DEFAULT NULL, -- Added registration_number
    p_vat_rate NUMERIC(4,2) DEFAULT 0.00, -- Added vat_rate
    p_storage_instructions VARCHAR(255) DEFAULT NULL, -- Added storage_instructions
    p_is_prescription_required BOOLEAN DEFAULT FALSE,
    p_country_of_origin VARCHAR(100) DEFAULT NULL, -- Added country_of_origin
    p_active_ingredients TEXT DEFAULT NULL -- Added active_ingredients
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Basic validations
    IF p_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Tenant ID cannot be null';
    END IF;

    IF p_medicine_name IS NULL OR TRIM(p_medicine_name) = '' THEN
        RAISE EXCEPTION 'Medicine name cannot be null or empty';
    END IF;

    IF p_base_unit_of_measure IS NULL OR TRIM(p_base_unit_of_measure) = '' THEN
        RAISE EXCEPTION 'Base unit of measure cannot be null or empty';
    END IF;

    IF p_vat_rate < 0 OR p_vat_rate > 100 THEN
        RAISE EXCEPTION 'VAT rate must be between 0 and 100, got %', p_vat_rate;
    END IF;

    -- Validate category_id if provided
    IF p_category_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM categories c
        WHERE c.category_id = p_category_id AND c.tenant_id = p_tenant_id
    ) THEN
        RAISE EXCEPTION 'Category ID % not found for tenant ID %.', p_category_id, p_tenant_id;
    END IF;

    -- Manufacturer name is now a direct input, no ID validation needed against a separate table.
    -- Dosage form is also not a separate entity anymore, base_unit_of_measure covers the unit.

    INSERT INTO medicines (
        tenant_id, medicine_name, description, category_id, manufacturer,
        base_unit, registration_number, vat_rate, storage_instructions,
        is_prescription_required, country_of_origin, active_ingredients,
        created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_medicine_name, p_description, p_category_id, p_manufacturer_name,
        p_base_unit_of_measure, p_registration_number, p_vat_rate, p_storage_instructions,
        p_is_prescription_required, p_country_of_origin, p_active_ingredients,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING medicine_id INTO p_new_medicine_id;
END;
$$;

-- Procedure to add a packaging unit for a medicine
CREATE OR REPLACE PROCEDURE sp_add_medicine_packaging_unit(
    p_tenant_id UUID,
    p_medicine_id UUID,
    p_unit_name VARCHAR(100), -- e.g., 'Hộp', 'Vỉ', 'Thùng'
    p_quantity_per_unit INTEGER, -- How many base_units are in this packaging_unit (e.g., 10 viên/vỉ)
    OUT p_new_packaging_unit_id UUID,
    OUT p_message TEXT, -- <<< ADDED
    p_is_default_import_unit BOOLEAN DEFAULT FALSE,
    p_is_default_sale_unit BOOLEAN DEFAULT FALSE
)
LANGUAGE plpgsql
AS $$
BEGIN
    p_message := ''; -- <<< ADDED
    p_new_packaging_unit_id := NULL; -- Initialize OUT param

    -- Ensure that the medicine exists for the tenant
    IF NOT EXISTS (SELECT 1 FROM medicines m WHERE m.medicine_id = p_medicine_id AND m.tenant_id = p_tenant_id) THEN
        p_message := 'Medicine ID ' || p_medicine_id || ' not found for tenant ID ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Ensure quantity_per_unit is positive
    IF p_quantity_per_unit <= 0 THEN
        p_message := 'Quantity per unit must be a positive number. Value: ' || p_quantity_per_unit;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if packaging unit with the same name already exists for this medicine and tenant
    IF EXISTS (SELECT 1 FROM medicine_packaging_units mpu WHERE mpu.tenant_id = p_tenant_id AND mpu.medicine_id = p_medicine_id AND mpu.unit_name = p_unit_name) THEN
        p_message := 'Packaging unit "' || p_unit_name || '" already exists for this medicine.';
        SELECT packaging_unit_id INTO p_new_packaging_unit_id FROM medicine_packaging_units mpu WHERE mpu.tenant_id = p_tenant_id AND mpu.medicine_id = p_medicine_id AND mpu.unit_name = p_unit_name;
        RETURN; -- Return existing ID and message
    END IF;

    INSERT INTO medicine_packaging_units (tenant_id, medicine_id, unit_name, quantity_per_unit,
                                        is_default_import_unit, is_default_sale_unit, created_at, updated_at)
    VALUES (p_tenant_id, p_medicine_id, p_unit_name, p_quantity_per_unit,
            p_is_default_import_unit, p_is_default_sale_unit, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING packaging_unit_id INTO p_new_packaging_unit_id;

    p_message := 'Packaging unit "' || p_unit_name || '" added successfully with ID ' || p_new_packaging_unit_id || '.'; -- <<< ADDED

EXCEPTION -- <<< ADDED
    WHEN OTHERS THEN -- <<< ADDED
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT; -- <<< ADDED
        -- p_new_packaging_unit_id remains NULL or its initial value if error before RETURNING
        -- The transaction will be rolled back automatically on unhandled exception.
        -- No explicit ROLLBACK needed here if the procedure is called in a way that starts a transaction.
        -- However, if called from an environment that doesn't auto-manage, consider explicit ROLLBACK.
        RETURN;

END;
$$;

-- Procedure to create a new import invoice
CREATE OR REPLACE PROCEDURE sp_create_import_invoice(
    p_tenant_id UUID,
    p_supplier_id UUID,
    p_employee_id UUID,
    p_invoice_number VARCHAR(100),
    p_import_date DATE,
    OUT p_new_import_invoice_id UUID,
    OUT p_message TEXT,
    p_notes TEXT DEFAULT NULL
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate input
    IF p_tenant_id IS NULL OR p_supplier_id IS NULL OR p_employee_id IS NULL OR p_invoice_number IS NULL OR p_invoice_number = '' OR p_import_date IS NULL THEN
        p_message := 'Tenant ID, Supplier ID, Employee ID, Invoice Number, and Import Date cannot be null or empty.';
        p_new_import_invoice_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check for existing invoice number for the tenant
    IF EXISTS (SELECT 1 FROM import_invoices ii WHERE ii.tenant_id = p_tenant_id AND ii.invoice_number = p_invoice_number) THEN
        p_message := 'Import invoice with number "' || p_invoice_number || '" already exists for this tenant.';
        SELECT import_invoice_id INTO p_new_import_invoice_id FROM import_invoices ii WHERE ii.tenant_id = p_tenant_id AND ii.invoice_number = p_invoice_number;
        RETURN; -- Return existing id and message
    END IF;

    INSERT INTO import_invoices (
        tenant_id, supplier_id, employee_id, invoice_number, import_date, notes, total_amount, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_supplier_id, p_employee_id, p_invoice_number, p_import_date, p_notes, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING import_invoice_id INTO p_new_import_invoice_id;

    p_message := 'Import invoice "' || p_invoice_number || '" created successfully with ID ' || p_new_import_invoice_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error creating import invoice: ' || SQLERRM;
        p_new_import_invoice_id := NULL;
        RAISE;
END;
$$;

-- Procedure to add a detail to an import invoice
CREATE OR REPLACE PROCEDURE sp_add_import_invoice_detail(
    p_tenant_id UUID,
    p_import_invoice_id UUID,
    p_medicine_id UUID,
    p_packaging_unit_id UUID, -- The ID of the medicine_packaging_units used for import
    p_quantity_imported_in_unit INTEGER,
    p_price_per_import_unit NUMERIC,
    OUT p_new_import_invoice_detail_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_import_detail RECORD;
    v_quantity_per_packaging_unit INTEGER;
    v_quantity_in_base_units INTEGER;
    v_item_total_amount NUMERIC;
BEGIN
    p_message := ''; -- Initialize message
    -- Validate foreign keys and retrieve packaging unit info
    IF NOT EXISTS (SELECT 1 FROM import_invoices ii WHERE ii.import_invoice_id = p_import_invoice_id AND ii.tenant_id = p_tenant_id) THEN
        p_message := 'Import Invoice ID ' || p_import_invoice_id || ' not found for tenant ' || p_tenant_id || '.';
        p_new_import_invoice_detail_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM medicines m WHERE m.medicine_id = p_medicine_id AND m.tenant_id = p_tenant_id) THEN
        p_message := 'Medicine ID ' || p_medicine_id || ' not found for tenant ' || p_tenant_id || '.';
        p_new_import_invoice_detail_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    SELECT mpu.quantity_per_unit INTO v_quantity_per_packaging_unit
    FROM medicine_packaging_units mpu
    WHERE mpu.packaging_unit_id = p_packaging_unit_id AND mpu.medicine_id = p_medicine_id AND mpu.tenant_id = p_tenant_id;

    IF NOT FOUND THEN
        p_message := 'Packaging Unit ID ' || p_packaging_unit_id || ' for Medicine ID ' || p_medicine_id || ' not found for tenant ' || p_tenant_id || '.';
        p_new_import_invoice_detail_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Validate inputs
    IF p_quantity_imported_in_unit IS NULL OR p_quantity_imported_in_unit <= 0 THEN
        p_message := 'Quantity imported in unit must be a positive integer. Value: ' || p_quantity_imported_in_unit;
        p_new_import_invoice_detail_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_price_per_import_unit IS NULL OR p_price_per_import_unit < 0 THEN
        p_message := 'Price per import unit cannot be negative. Value: ' || p_price_per_import_unit;
        p_new_import_invoice_detail_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Calculate quantity in base units
    v_quantity_in_base_units := p_quantity_imported_in_unit * v_quantity_per_packaging_unit;

    -- Calculate item total amount
    v_item_total_amount := p_quantity_imported_in_unit * p_price_per_import_unit;

    INSERT INTO import_invoice_details (
        tenant_id, import_invoice_id, medicine_id, packaging_unit_id,
        quantity_imported_in_unit, price_per_import_unit,
        quantity_imported_in_base_unit, total_amount_for_item, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_import_invoice_id, p_medicine_id, p_packaging_unit_id,
        p_quantity_imported_in_unit, p_price_per_import_unit,
        v_quantity_in_base_units, v_item_total_amount, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING import_invoice_detail_id INTO p_new_import_invoice_detail_id;

    -- Update total_amount in import_invoices table
    UPDATE import_invoices
    SET total_amount = total_amount + v_item_total_amount,
        updated_at = CURRENT_TIMESTAMP
    WHERE import_invoice_id = p_import_invoice_id AND tenant_id = p_tenant_id;

    p_message := 'Import invoice detail added successfully with ID ' || p_new_import_invoice_detail_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        IF p_message = '' THEN
            p_message := 'Error adding import invoice detail: ' || SQLERRM;
        END IF;
        p_new_import_invoice_detail_id := NULL;
        RAISE;
END;
$$;

-- Procedure to add a medicine batch from an import invoice detail
CREATE OR REPLACE PROCEDURE sp_add_medicine_batch_from_import(
    p_tenant_id UUID,
    p_import_invoice_detail_id UUID,
    p_batch_number VARCHAR(100),
    p_expiry_date TIMESTAMP WITHOUT TIME ZONE, -- Changed from DATE
    p_selling_price_per_base_unit INTEGER, -- Changed from NUMERIC, Selling price for one base unit of the medicine
    OUT p_new_batch_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_import_detail RECORD;
    v_supplier_id UUID;
    v_medicine_id UUID;
    v_quantity_imported_in_base_unit INTEGER;
    v_import_price_per_base_unit NUMERIC;
    v_import_date DATE;
BEGIN
    p_message := ''; -- Initialize message
    -- Get details from import_invoice_details and related tables
    SELECT
        iid.medicine_id,
        iid.quantity_imported_in_base_unit,
        (iid.price_per_import_unit / mpu.quantity_per_unit) AS calculated_import_price_per_base,
        ii.supplier_id,
        ii.import_date
    INTO
        v_medicine_id,
        v_quantity_imported_in_base_unit,
        v_import_price_per_base_unit,
        v_supplier_id,
        v_import_date
    FROM import_invoice_details iid
    JOIN import_invoices ii ON iid.import_invoice_id = ii.import_invoice_id AND iid.tenant_id = ii.tenant_id
    JOIN medicine_packaging_units mpu ON iid.packaging_unit_id = mpu.packaging_unit_id AND iid.tenant_id = mpu.tenant_id AND iid.medicine_id = mpu.medicine_id
    WHERE iid.import_invoice_detail_id = p_import_invoice_detail_id AND iid.tenant_id = p_tenant_id;

    IF NOT FOUND THEN
        p_message := 'Import Invoice Detail ID ' || p_import_invoice_detail_id || ' not found for tenant ' || p_tenant_id || '.';
        p_new_batch_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Validate batch number (ensure it's unique for the medicine within the tenant if desired, or just not null)
    IF p_batch_number IS NULL OR p_batch_number = '' THEN
        p_message := 'Batch number cannot be null or empty.';
        p_new_batch_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check for uniqueness of batch_number for a given medicine_id and tenant_id
    IF EXISTS (SELECT 1 FROM batches b WHERE b.tenant_id = p_tenant_id AND b.medicine_id = v_medicine_id AND b.batch_number = p_batch_number) THEN
        p_message := 'Batch number "' || p_batch_number || '" already exists for medicine ID "' || v_medicine_id || '" for this tenant.';
        SELECT batch_id INTO p_new_batch_id FROM batches b WHERE b.tenant_id = p_tenant_id AND b.medicine_id = v_medicine_id AND b.batch_number = p_batch_number;
        RETURN; -- Return existing batch_id and message
    END IF;

    IF p_expiry_date IS NULL THEN
        p_message := 'Expiry date cannot be null.';
        p_new_batch_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_expiry_date <= CURRENT_DATE THEN
        p_message := 'Expiry date must be in the future. Got: ' || p_expiry_date::DATE;
        p_new_batch_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_selling_price_per_base_unit IS NULL OR p_selling_price_per_base_unit < 0 THEN
        p_message := 'Selling price per base unit must be a non-negative integer. Value: ' || p_selling_price_per_base_unit;
        p_new_batch_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    INSERT INTO batches (
        tenant_id, medicine_id, supplier_id, import_invoice_detail_id,
        batch_number, import_date, expiry_date, quantity_imported, quantity_remaining,
        import_price_per_base_unit, selling_price_per_base_unit, created_at, updated_at
    )
    VALUES (
        p_tenant_id, v_medicine_id, v_supplier_id, p_import_invoice_detail_id,
        p_batch_number, v_import_date, p_expiry_date, v_quantity_imported_in_base_unit, v_quantity_imported_in_base_unit,
        v_import_price_per_base_unit, p_selling_price_per_base_unit, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING batch_id INTO p_new_batch_id;

    p_message := 'Batch "' || p_batch_number || '" for medicine ID ' || v_medicine_id || ' added successfully with ID ' || p_new_batch_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        IF p_message = '' THEN
            p_message := 'Error adding medicine batch from import: ' || SQLERRM;
        END IF;
        p_new_batch_id := NULL;
        RAISE;
END;
$$;

-- Procedure to add a medicine batch from an import invoice detail (alternative version)
CREATE OR REPLACE PROCEDURE sp_add_medicine_batch_from_import_alt(
    p_tenant_id UUID,
    p_import_invoice_detail_id UUID,
    p_batch_number VARCHAR(100),
    p_expiry_date TIMESTAMP WITHOUT TIME ZONE,
    p_selling_price_per_base_unit INTEGER,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_import_detail RECORD;
    v_supplier_id UUID;
    v_medicine_id UUID;
    v_quantity_imported_in_base_unit INTEGER;
    v_import_price_per_base_unit NUMERIC;
    v_import_date DATE;
    p_new_batch_id UUID;
BEGIN
    p_message := ''; -- Initialize message
    -- Get details from import_invoice_details and related tables
    SELECT
        iid.medicine_id,
        iid.quantity_imported_in_base_unit,
        (iid.price_per_import_unit / mpu.quantity_per_unit) AS calculated_import_price_per_base,
        ii.supplier_id,
        ii.import_date
    INTO
        v_medicine_id,
        v_quantity_imported_in_base_unit,
        v_import_price_per_base_unit,
        v_supplier_id,
        v_import_date
    FROM import_invoice_details iid
    JOIN import_invoices ii ON iid.import_invoice_id = ii.import_invoice_id AND iid.tenant_id = ii.tenant_id
    JOIN medicine_packaging_units mpu ON iid.packaging_unit_id = mpu.packaging_unit_id AND iid.tenant_id = mpu.tenant_id AND iid.medicine_id = mpu.medicine_id
    WHERE iid.import_invoice_detail_id = p_import_invoice_detail_id AND iid.tenant_id = p_tenant_id;

    IF NOT FOUND THEN
        p_message := 'Import Invoice Detail ID ' || p_import_invoice_detail_id || ' not found for tenant ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Validate batch number (ensure it's unique for the medicine within the tenant if desired, or just not null)
    IF p_batch_number IS NULL OR p_batch_number = '' THEN
        p_message := 'Batch number cannot be null or empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check for uniqueness of batch_number for a given medicine_id and tenant_id
    IF EXISTS (SELECT 1 FROM batches b WHERE b.tenant_id = p_tenant_id AND b.medicine_id = v_medicine_id AND b.batch_number = p_batch_number) THEN
        p_message := 'Batch number "' || p_batch_number || '" already exists for medicine ID "' || v_medicine_id || '" for this tenant.';
        SELECT batch_id INTO p_new_batch_id FROM batches b WHERE b.tenant_id = p_tenant_id AND b.medicine_id = v_medicine_id AND b.batch_number = p_batch_number;
        RETURN; -- Return existing batch_id and message
    END IF;

    IF p_expiry_date IS NULL THEN
        p_message := 'Expiry date cannot be null.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_expiry_date <= CURRENT_DATE THEN
        p_message := 'Expiry date must be in the future. Got: ' || p_expiry_date::DATE;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_selling_price_per_base_unit IS NULL OR p_selling_price_per_base_unit < 0 THEN
        p_message := 'Selling price per base unit must be a non-negative integer. Value: ' || p_selling_price_per_base_unit;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    INSERT INTO batches (
        tenant_id, medicine_id, supplier_id, import_invoice_detail_id,
        batch_number, import_date, expiry_date, quantity_imported, quantity_remaining,
        import_price_per_base_unit, selling_price_per_base_unit, created_at, updated_at
    )
    VALUES (
        p_tenant_id, v_medicine_id, v_supplier_id, p_import_invoice_detail_id,
        p_batch_number, v_import_date, p_expiry_date, v_quantity_imported_in_base_unit, v_quantity_imported_in_base_unit,
        v_import_price_per_base_unit, p_selling_price_per_base_unit, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING batch_id INTO p_new_batch_id;

    p_message := 'Batch created successfully: "' || p_batch_number || '" for medicine ID ' || v_medicine_id || ' added with ID ' || p_new_batch_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        IF p_message = '' THEN
            p_message := 'Error adding medicine batch from import: ' || SQLERRM;
        END IF;
        RAISE;
END;
$$;

-- Procedure to perform an inventory adjustment
CREATE OR REPLACE PROCEDURE sp_perform_inventory_adjustment(
    p_tenant_id UUID,
    p_batch_id UUID,
    p_employee_id UUID,
    p_adjustment_type_id UUID,
    p_quantity_change NUMERIC, -- Positive for increase, negative for decrease
    p_reason TEXT,
    OUT p_new_adjustment_id UUID
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate foreign keys and existence
    IF NOT EXISTS (SELECT 1 FROM batches b WHERE b.batch_id = p_batch_id AND b.tenant_id = p_tenant_id) THEN
        RAISE EXCEPTION 'Batch ID % not found for tenant ID %.', p_batch_id, p_tenant_id;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM employees e WHERE e.employee_id = p_employee_id AND e.tenant_id = p_tenant_id) THEN
        RAISE EXCEPTION 'Employee ID % not found for tenant ID %.', p_employee_id, p_tenant_id;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM inventory_adjustment_types iat WHERE iat.adjustment_type_id = p_adjustment_type_id AND iat.tenant_id = p_tenant_id) THEN
        RAISE EXCEPTION 'Inventory Adjustment Type ID % not found for tenant ID %.', p_adjustment_type_id, p_tenant_id;
    END IF;

    -- Ensure quantity_change is not zero, as it wouldn't make sense for an adjustment
    IF p_quantity_change = 0 THEN
        RAISE EXCEPTION 'Quantity change cannot be zero for an inventory adjustment.';
    END IF;

    INSERT INTO inventory_adjustments (
        tenant_id, batch_id, employee_id, adjustment_type_id,
        adjustment_date, quantity_change, reason, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_batch_id, p_employee_id, p_adjustment_type_id,
        CURRENT_TIMESTAMP, p_quantity_change, p_reason, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING adjustment_id INTO p_new_adjustment_id;

    -- The trigger 'trg_after_inventory_adjustment_insert_update_batch_qty'
    -- will automatically update the batches.quantity_remaining.
    -- The trigger 'trg_before_batch_check_negative_stock' on 'batches' table
    -- will prevent quantity_remaining from becoming negative if p_quantity_change is too large a negative number.
END;
$$;

-- Stored Procedure to create a new category
CREATE OR REPLACE PROCEDURE sp_create_category(
    p_tenant_id UUID,
    p_category_name VARCHAR(255),
    p_description TEXT, -- Optional, can be NULL
    OUT p_new_category_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    p_message := '';
    p_new_category_id := NULL;

    -- Validate inputs
    IF p_tenant_id IS NULL THEN
        p_message := 'Tenant ID cannot be NULL.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_category_name IS NULL OR TRIM(p_category_name) = '' THEN
        p_message := 'Category name cannot be empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if category name already exists for the tenant
    IF EXISTS (SELECT 1 FROM categories c WHERE c.tenant_id = p_tenant_id AND c.category_name = p_category_name) THEN
        SELECT category_id INTO p_new_category_id
        FROM categories c
        WHERE c.tenant_id = p_tenant_id AND c.category_name = p_category_name;

        p_message := 'Category with name "' || p_category_name || '" already exists with ID ' || p_new_category_id || '.';
        RETURN; -- Return existing ID and message
    END IF;

    INSERT INTO categories (tenant_id, category_name, description, created_at, updated_at)
    VALUES (p_tenant_id, p_category_name, p_description, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING category_id INTO p_new_category_id;

    p_message := 'Category "' || p_category_name || '" created successfully with ID ' || p_new_category_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT;
        -- p_new_category_id remains NULL or its initial value
        -- The transaction will be rolled back automatically on unhandled exception.
END;
$$;

-- Procedure to add a medicine packaging unit is defined above at line ~774

-- Stored Procedure to create a new medicine
CREATE OR REPLACE PROCEDURE sp_create_medicine(
    p_tenant_id UUID,
    p_medicine_name VARCHAR(255),
    p_description TEXT,
    p_registration_number VARCHAR(50),
    p_category_id UUID,
    p_base_unit VARCHAR(50),    -- e.g., 'Viên', 'Chai', 'Hộp'
    OUT p_new_medicine_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    p_message := '';
    p_new_medicine_id := NULL;

    -- Basic Validations
    IF p_tenant_id IS NULL THEN
        p_message := 'Tenant ID cannot be NULL.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;
    IF p_medicine_name IS NULL OR TRIM(p_medicine_name) = '' THEN
        p_message := 'Medicine name cannot be empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;
    IF p_category_id IS NULL THEN
        p_message := 'Category ID cannot be NULL.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;
    -- Add more validations as needed (e.g., for supplier_id, base_unit)

    -- Check if medicine name already exists for the tenant
    IF EXISTS (SELECT 1 FROM medicines m WHERE m.tenant_id = p_tenant_id AND m.medicine_name = p_medicine_name) THEN
        SELECT medicine_id INTO p_new_medicine_id
        FROM medicines m
        WHERE m.tenant_id = p_tenant_id AND m.medicine_name = p_medicine_name;

        p_message := 'Medicine with name "' || p_medicine_name || '" already exists with ID ' || p_new_medicine_id || '.';
        RETURN; -- Return existing ID and message
    END IF;

    -- Check if registration number already exists for the tenant
    IF p_registration_number IS NOT NULL AND EXISTS (SELECT 1 FROM medicines m WHERE m.tenant_id = p_tenant_id AND m.registration_number = p_registration_number) THEN
        SELECT medicine_id INTO p_new_medicine_id
        FROM medicines m
        WHERE m.tenant_id = p_tenant_id AND m.registration_number = p_registration_number;

        p_message := 'Medicine with registration number "' || p_registration_number || '" already exists with ID ' || p_new_medicine_id || '.';
        RETURN; -- Return existing ID and message
    END IF;

    INSERT INTO medicines (
        tenant_id, medicine_name, description, registration_number,
        category_id, base_unit,
        is_active, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_medicine_name, p_description, p_registration_number,
        p_category_id, p_base_unit,
        TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING medicine_id INTO p_new_medicine_id;

    p_message := 'Medicine "' || p_medicine_name || '" created successfully with ID ' || p_new_medicine_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT;
        -- p_new_medicine_id remains NULL or its initial value
END;
$$;

-- Functions and Stored Procedures for Sales Operations --

-- Function to get total available stock for a medicine
CREATE OR REPLACE FUNCTION fn_get_available_stock_for_medicine(
    p_tenant_id UUID,
    p_medicine_id UUID
)
RETURNS NUMERIC(10,2) AS $$
DECLARE
    v_total_stock NUMERIC(10,2);
BEGIN
    SELECT COALESCE(SUM(b.quantity_remaining), 0)
    INTO v_total_stock
    FROM batches b
    WHERE b.tenant_id = p_tenant_id
      AND b.medicine_id = p_medicine_id
      AND b.quantity_remaining > 0
      AND b.expiry_date >= CURRENT_DATE; -- Only consider non-expired batches

    RETURN v_total_stock;
END;
$$ LANGUAGE plpgsql;

-- Function to find the best batch for sale (FEFO - First Expired First Out)
-- Returns a record with batch_id, quantity_available_in_batch, and selling_price_per_base_unit
CREATE OR REPLACE FUNCTION fn_find_best_batch_for_sale(
    p_tenant_id UUID,
    p_medicine_id UUID
)
RETURNS TABLE(batch_id UUID, quantity_available_in_batch NUMERIC, selling_price_per_base_unit NUMERIC, expiry_date DATE) AS $$
DECLARE
    v_medicine_exists BOOLEAN;
BEGIN
    -- Validate input parameters
    IF p_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Tenant ID cannot be null';
    END IF;

    IF p_medicine_id IS NULL THEN
        RAISE EXCEPTION 'Medicine ID cannot be null';
    END IF;

    -- Check if medicine exists
    SELECT EXISTS(
        SELECT 1 FROM medicines m
        WHERE m.tenant_id = p_tenant_id AND m.medicine_id = p_medicine_id
    ) INTO v_medicine_exists;

    IF NOT v_medicine_exists THEN
        RAISE EXCEPTION 'Medicine ID % not found for tenant ID %', p_medicine_id, p_tenant_id;
    END IF;

    RETURN QUERY
    SELECT
        b.batch_id,
        b.quantity_remaining AS quantity_available_in_batch,
        b.selling_price_per_base_unit,
        b.expiry_date
    FROM batches b
    WHERE b.tenant_id = p_tenant_id
      AND b.medicine_id = p_medicine_id
      AND b.quantity_remaining > 0
      AND b.expiry_date >= CURRENT_DATE -- Only consider non-expired batches
    ORDER BY b.expiry_date ASC, b.import_date ASC, b.created_at ASC -- FEFO, then FIFO on import_date if expiry is same
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Stored Procedure to process a sales transaction
CREATE OR REPLACE PROCEDURE sp_process_sale_transaction(
    p_tenant_id UUID,
    p_employee_id UUID,
    p_customer_id UUID,         -- Can be NULL for walk-in customers
    p_prescription_id UUID,     -- Can be NULL if not a prescription sale
    p_payment_method_id UUID,
    p_sale_items JSONB,         -- Array of objects: {medicine_id: UUID, quantity_requested_in_base_unit: NUMERIC, unit_price_override: NUMERIC (optional)}
    p_transaction_status_id UUID,
    OUT p_new_transaction_id UUID, -- This name can remain, it's an OUT parameter holding the generated ID
    OUT p_message TEXT,
    p_notes TEXT DEFAULT ''
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_sale_item JSONB;
    v_medicine_id UUID;
    v_quantity_requested_in_base_unit NUMERIC;
    v_unit_price_override NUMERIC;
    v_best_batch RECORD;
    v_quantity_to_sell_from_this_batch NUMERIC;
    v_current_item_quantity_fulfilled NUMERIC;
    v_calculated_total_amount NUMERIC := 0.00;
    v_calculated_discount_amount NUMERIC := 0.00;
    v_calculated_points_earned INTEGER := 0;
    v_unit_price NUMERIC;
    v_subtotal NUMERIC; -- Declare v_subtotal
BEGIN
    p_message := '';
    p_new_transaction_id := NULL;

    -- Basic Validations
    IF NOT EXISTS (SELECT 1 FROM employees e WHERE e.employee_id = p_employee_id AND e.tenant_id = p_tenant_id) THEN
        p_message := 'Employee ID ' || p_employee_id || ' not found for tenant ID ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_customer_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM customers c WHERE c.customer_id = p_customer_id AND c.tenant_id = p_tenant_id) THEN
        p_message := 'Customer ID ' || p_customer_id || ' not found for tenant ID ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_prescription_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM prescriptions pr WHERE pr.prescription_id = p_prescription_id AND pr.tenant_id = p_tenant_id) THEN
        p_message := 'Prescription ID ' || p_prescription_id || ' not found for tenant ID ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_payment_method_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM payment_methods pm WHERE pm.payment_method_id = p_payment_method_id AND pm.tenant_id = p_tenant_id) THEN
        p_message := 'Payment Method ID ' || p_payment_method_id || ' not found for tenant ID ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_transaction_status_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM transaction_statuses ts WHERE ts.transaction_status_id = p_transaction_status_id AND ts.tenant_id = p_tenant_id) THEN
        p_message := 'Transaction Status ID ' || p_transaction_status_id || ' not found for tenant ID ' || p_tenant_id || '.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Validate sale items
    IF p_sale_items IS NULL OR jsonb_array_length(p_sale_items) = 0 THEN
        p_message := 'Sale items cannot be null or empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Payment method is now UUID, no ID validation needed against a separate table.
    -- Transaction status will use default 'Pending' or set explicitly if needed later.

    -- 1. Create the initial sales_transactions record
    INSERT INTO sales_transactions (
        tenant_id, employee_id, customer_id, prescription_id,
        payment_method_id, transaction_status_id, notes,
        total_amount, discount_applied, points_earned, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_employee_id, p_customer_id, p_prescription_id,
        p_payment_method_id, p_transaction_status_id, p_notes,
        0, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP -- Initial total_amount, will be updated later
    )
    RETURNING sale_transaction_id INTO p_new_transaction_id;

    -- Loop through each item in p_sale_items
    FOR v_sale_item IN SELECT * FROM jsonb_array_elements(p_sale_items)
    LOOP
        v_medicine_id := (v_sale_item->>'medicine_id')::UUID;
        v_quantity_requested_in_base_unit := (v_sale_item->>'quantity_requested_in_base_unit')::NUMERIC;
        v_unit_price_override := (v_sale_item->>'unit_price_override')::NUMERIC;

        -- Validate medicine exists and is active (example, actual validation might be more complex)
        IF NOT EXISTS (SELECT 1 FROM medicines WHERE medicine_id = v_medicine_id AND tenant_id = p_tenant_id) THEN
            p_message := 'Medicine ID ' || v_medicine_id || ' not found or not active.';
            RAISE EXCEPTION USING MESSAGE = p_message;
        END IF;

        v_current_item_quantity_fulfilled := 0;

        -- Loop to fulfill the requested quantity for the current medicine from available batches
        WHILE v_current_item_quantity_fulfilled < v_quantity_requested_in_base_unit
        LOOP
            -- Find the best batch (FEFO)
            SELECT * INTO v_best_batch FROM fn_find_best_batch_for_sale(p_tenant_id, v_medicine_id);

            IF v_best_batch IS NULL OR v_best_batch.batch_id IS NULL THEN
                -- No more suitable batches found for this medicine
                p_message := 'Insufficient stock or no suitable batches available for medicine ID ' || v_medicine_id ||
                             '. Requested: ' || v_quantity_requested_in_base_unit ||
                             ', Fulfilled so far: ' || v_current_item_quantity_fulfilled || '.';
                RAISE EXCEPTION USING MESSAGE = p_message;
            END IF;

            v_quantity_to_sell_from_this_batch := LEAST(v_best_batch.quantity_available_in_batch, (v_quantity_requested_in_base_unit - v_current_item_quantity_fulfilled));

            v_unit_price := COALESCE(v_unit_price_override, v_best_batch.selling_price_per_base_unit);
            IF v_unit_price IS NULL OR v_unit_price < 0 THEN
                 p_message := 'Invalid selling price for batch ID ' || v_best_batch.batch_id || ' of medicine ID ' || v_medicine_id || '.';
                 RAISE EXCEPTION USING MESSAGE = p_message;
            END IF;

            -- Insert into sales_transaction_details
            INSERT INTO sales_transaction_details (
                tenant_id, sale_transaction_id, medicine_id, batch_id,
                quantity_sold_in_base_unit, unit_price, created_at, updated_at
            )
            VALUES (
                p_tenant_id, p_new_transaction_id, v_medicine_id, v_best_batch.batch_id,
                v_quantity_to_sell_from_this_batch, v_unit_price, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            );
            -- The trigger 'trg_after_sales_detail_insert_update_batch_qty' will update batch quantity.

            v_current_item_quantity_fulfilled := v_current_item_quantity_fulfilled + v_quantity_to_sell_from_this_batch;
            v_calculated_total_amount := v_calculated_total_amount + (v_quantity_to_sell_from_this_batch * v_unit_price);

        END LOOP; -- End of WHILE loop for fulfilling current medicine item
    END LOOP; -- End of FOR loop for p_sale_items

    -- 3. Calculate final amounts (discount and loyalty points)
    -- For now, we're using placeholder values. In a real implementation, these would call actual functions
    -- that calculate discounts and loyalty points based on business rules
    v_calculated_discount_amount := 0; -- Placeholder, replace with actual discount calculation
    v_calculated_points_earned := 0; -- Placeholder, replace with actual points calculation

    -- 4. Update the sales_transactions record with final amounts
    UPDATE sales_transactions
    SET total_amount = v_calculated_total_amount,
        discount_applied = v_calculated_discount_amount,
        points_earned = v_calculated_points_earned,
        updated_at = CURRENT_TIMESTAMP
    WHERE sale_transaction_id = p_new_transaction_id AND tenant_id = p_tenant_id;

    p_message := 'Sales transaction ' || p_new_transaction_id || ' processed successfully.';

EXCEPTION
    WHEN OTHERS THEN
        -- p_message is already set by RAISE EXCEPTION or will be set here
        IF p_message IS NULL OR p_message = '' THEN
            p_message := SQLERRM;
        END IF;
        -- The transaction will be rolled back automatically on unhandled exception in PL/pgSQL block.
        -- No explicit ROLLBACK needed here if the procedure is called in a way that starts a transaction.
        -- However, if called from an environment that doesn't auto-manage, consider explicit ROLLBACK.
        RETURN;

END;
$$;

-- Procedure to create a new supplier
CREATE OR REPLACE PROCEDURE sp_create_supplier(
    p_tenant_id UUID,
    p_supplier_name VARCHAR(255),
    OUT p_new_supplier_id UUID,
    OUT p_message TEXT,
    p_contact_person VARCHAR(255) DEFAULT NULL,
    p_email VARCHAR(255) DEFAULT NULL,
    p_phone_number VARCHAR(50) DEFAULT NULL,
    p_address TEXT DEFAULT NULL
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate input
    IF p_tenant_id IS NULL OR p_supplier_name IS NULL OR p_supplier_name = '' THEN
        p_message := 'Tenant ID and Supplier Name cannot be null or empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if supplier with the same name already exists for this tenant
    IF EXISTS (SELECT 1 FROM suppliers s WHERE s.tenant_id = p_tenant_id AND s.supplier_name = p_supplier_name) THEN
        p_message := 'Supplier with name "' || p_supplier_name || '" already exists for this tenant.';
        SELECT supplier_id INTO p_new_supplier_id FROM suppliers s WHERE s.tenant_id = p_tenant_id AND s.supplier_name = p_supplier_name;
        RETURN; -- Return existing supplier_id and message
    END IF;

    INSERT INTO suppliers (
        tenant_id, supplier_name, contact_person, email, phone_number, address, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_supplier_name, p_contact_person, p_email, p_phone_number, p_address, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING supplier_id INTO p_new_supplier_id;

    p_message := 'Supplier "' || p_supplier_name || '" created successfully with ID ' || p_new_supplier_id || '.';

EXCEPTION
    WHEN OTHERS THEN
        p_message := 'Error creating supplier: ' || SQLERRM;
        -- Ensure p_new_supplier_id is NULL if an error occurs before RETURNING
        p_new_supplier_id := NULL;
        -- Re-raise the exception to ensure transaction rollback if not handled by caller
        RAISE;
END;
$$;

-- Procedure to create a new Tenant
CREATE OR REPLACE PROCEDURE sp_create_tenant(
    p_tenant_name VARCHAR(255),
    OUT p_new_tenant_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Validate input
    IF p_tenant_name IS NULL OR p_tenant_name = '' THEN
        p_message := 'Tenant Name cannot be null or empty.';
        p_new_tenant_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if tenant with the same name already exists
    SELECT tenant_id INTO p_new_tenant_id FROM tenants t WHERE t.tenant_name = p_tenant_name;

    IF p_new_tenant_id IS NOT NULL THEN
        p_message := 'Tenant with name "' || p_tenant_name || '" already exists.';
        RETURN; -- Return existing tenant_id and message
    END IF;

    -- If no existing tenant, insert a new one
    INSERT INTO tenants (
        tenant_name,
        subscription_start_date, -- This is NOT NULL in the table definition
        is_active,
        created_at,
        updated_at
    )
    VALUES (
        p_tenant_name,
        CURRENT_DATE, -- Defaulting subscription_start_date
        TRUE,         -- Defaulting is_active to TRUE
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    )
    RETURNING tenants.tenant_id INTO p_new_tenant_id;

    p_message := 'Tenant "' || p_tenant_name || '" created successfully with ID ' || p_new_tenant_id || '.';

EXCEPTION
    WHEN unique_violation THEN -- Should be caught by the pre-check for name, but as a safeguard
        -- This block might be redundant if pre-checks are thorough and unique constraints are well-defined
        -- However, it acts as a safeguard.
        IF p_tenant_name IS NOT NULL AND EXISTS(SELECT 1 FROM tenants t WHERE t.tenant_name = p_tenant_name) THEN
             SELECT tenant_id INTO p_new_tenant_id FROM tenants t WHERE t.tenant_name = p_tenant_name;
             p_message := 'Error: Tenant with name "' || p_tenant_name || '" already exists (recovered via unique_violation) with ID ' || p_new_tenant_id || '.';
        ELSE
            p_message := 'Error: A unique constraint was violated while creating tenant "' || p_tenant_name || '", but could not retrieve existing ID.';
            -- p_new_tenant_id remains NULL
        END IF;
        RAISE WARNING USING MESSAGE = p_message;
    WHEN OTHERS THEN
        p_new_tenant_id := NULL;
        p_message := 'Error creating tenant: ' || SQLERRM;
        RAISE; -- Re-raise other errors to ensure transaction rollback
END;
$$;

-- Procedure to create a new Employee
CREATE OR REPLACE PROCEDURE sp_create_employee(
    p_tenant_id UUID,
    p_full_name VARCHAR(255),
    p_email VARCHAR(255),
    OUT p_new_employee_id UUID,
    OUT p_message TEXT,
    p_phone_number VARCHAR(50) DEFAULT NULL,
    p_role_id UUID DEFAULT NULL, -- Role can be assigned later
    p_username VARCHAR(100) DEFAULT NULL, -- Username for login, can be NULL if email is used
    p_is_active BOOLEAN DEFAULT TRUE
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_employee_id UUID;
BEGIN
    -- Validate input
    IF p_tenant_id IS NULL OR p_full_name IS NULL OR p_full_name = '' OR p_email IS NULL OR p_email = '' THEN
        p_message := 'Tenant ID, Full Name, and Email cannot be empty.';
        p_new_employee_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Normalize email to lowercase for consistent checking
    p_email := lower(p_email);

    -- Validate email format
    IF p_email !~ '^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$' THEN
        p_message := 'Invalid email format: ' || p_email;
        p_new_employee_id := NULL;
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    IF p_username IS NOT NULL THEN
        p_username := lower(p_username);
    END IF;

    -- Check if employee with the same email already exists for this tenant
    SELECT employee_id INTO v_existing_employee_id
    FROM employees e
    WHERE e.tenant_id = p_tenant_id AND e.email = p_email;

    IF v_existing_employee_id IS NOT NULL THEN
        p_new_employee_id := v_existing_employee_id;
        p_message := 'Employee with email "' || p_email || '" already exists with ID ' || p_new_employee_id || '.';
        RETURN;
    END IF;

    -- If username is provided, check if employee with the same username already exists for this tenant
    IF p_username IS NOT NULL THEN
        SELECT employee_id INTO v_existing_employee_id
        FROM employees e
        WHERE e.tenant_id = p_tenant_id AND e.username = p_username;

        IF v_existing_employee_id IS NOT NULL THEN
            p_new_employee_id := v_existing_employee_id;
            p_message := 'Employee with username "' || p_username || '" already exists with ID ' || p_new_employee_id || '.';
            RETURN;
        END IF;
    END IF;

    INSERT INTO employees (
        tenant_id, full_name, email, phone_number, role_id, username, is_active, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_full_name, p_email, p_phone_number, p_role_id, p_username, p_is_active, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING employee_id INTO p_new_employee_id;

    p_message := 'Employee "' || p_full_name || '" created successfully with ID ' || p_new_employee_id || '.';

EXCEPTION
    WHEN unique_violation THEN -- e.g. if unique constraint on email/username is violated despite checks (race condition)
        -- This block might be redundant if pre-checks are thorough and unique constraints are well-defined
        -- However, it acts as a safeguard.
        IF p_email IS NOT NULL AND EXISTS(SELECT 1 FROM employees e WHERE e.tenant_id = p_tenant_id AND e.email = p_email) THEN
             SELECT employee_id INTO v_existing_employee_id FROM employees e WHERE e.tenant_id = p_tenant_id AND e.email = p_email;
             p_message := 'Error: Employee with email "' || p_email || '" already exists (ID: ' || v_existing_employee_id || '). Unique constraint violated.';
        ELSIF p_username IS NOT NULL AND EXISTS(SELECT 1 FROM employees e WHERE e.tenant_id = p_tenant_id AND e.username = p_username) THEN
             SELECT employee_id INTO v_existing_employee_id FROM employees e WHERE e.tenant_id = p_tenant_id AND e.username = p_username;
             p_message := 'Error: Employee with username "' || p_username || '" already exists (ID: ' || v_existing_employee_id || '). Unique constraint violated.';
        ELSE
            p_message := 'Error creating employee: Unique constraint violated (unspecified field). ' || SQLERRM;
        END IF;
        p_new_employee_id := v_existing_employee_id; -- return existing id if found
        RAISE WARNING USING MESSAGE = p_message;
    WHEN OTHERS THEN
        p_message := 'Error creating employee: ' || SQLERRM;
        p_new_employee_id := NULL;
        RAISE; -- Re-raise to ensure transaction rollback
END;
$$;

-- Stored Procedure to create a new customer or return existing
CREATE OR REPLACE PROCEDURE sp_create_customer(
    p_tenant_id UUID,
    p_full_name VARCHAR(255),
    p_phone_number VARCHAR(50),
    p_email VARCHAR(255),
    p_address TEXT,
    OUT p_new_customer_id UUID,
    OUT p_message TEXT,
    p_date_of_birth DATE DEFAULT NULL, -- Optional
    p_gender VARCHAR(10) DEFAULT NULL, -- Optional
    p_loyalty_tier_id UUID DEFAULT NULL -- Optional
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_customer_id UUID;
BEGIN
    p_message := '';
    p_new_customer_id := NULL;

    -- Validate required inputs
    IF p_tenant_id IS NULL OR p_full_name IS NULL OR TRIM(p_full_name) = '' OR p_phone_number IS NULL OR TRIM(p_phone_number) = '' THEN
        p_message := 'Tenant ID, Full Name, and Phone Number cannot be empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if customer with the same phone number already exists for this tenant
    SELECT customer_id INTO v_existing_customer_id
    FROM customers c
    WHERE c.tenant_id = p_tenant_id AND c.phone_number = p_phone_number;

    IF v_existing_customer_id IS NOT NULL THEN
        p_new_customer_id := v_existing_customer_id;
        p_message := 'Customer with phone number "' || p_phone_number || '" already exists with ID ' || p_new_customer_id || '.';
        RETURN;
    END IF;

    -- If email is provided and different, optionally check for email duplication (can be stricter if needed)
    IF p_email IS NOT NULL AND TRIM(p_email) <> '' THEN
        IF EXISTS (SELECT 1 FROM customers c WHERE c.tenant_id = p_tenant_id AND c.email = p_email AND c.phone_number <> p_phone_number) THEN
            p_message := 'Warning: Another customer with email "' || p_email || '" exists but with a different phone number. Proceeding with new customer creation for phone "' || p_phone_number || '".';
            RAISE NOTICE USING MESSAGE = p_message; -- Or make this a stricter check if email must also be unique or combined unique
        END IF;
    END IF;

    INSERT INTO customers (
        tenant_id, full_name, phone_number, email, address,
        date_of_birth, gender, loyalty_tier_id, is_active, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_full_name, p_phone_number, p_email, p_address,
        p_date_of_birth, p_gender, p_loyalty_tier_id, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING customer_id INTO p_new_customer_id;

    p_message := 'Customer "' || p_full_name || '" created successfully with ID ' || p_new_customer_id || '.';

EXCEPTION
    WHEN unique_violation THEN -- Handles unique constraint violation on (tenant_id, phone_number) if race condition occurs
        -- Attempt to retrieve the ID again if the pre-check missed due to race condition
        SELECT customer_id INTO v_existing_customer_id
        FROM customers c WHERE c.tenant_id = p_tenant_id AND c.phone_number = p_phone_number;

        IF v_existing_customer_id IS NOT NULL THEN
            p_new_customer_id := v_existing_customer_id;
            p_message := 'Error: Customer with phone number "' || p_phone_number || '" already exists (recovered via unique_violation) with ID ' || p_new_customer_id || '.';
        ELSE
            p_message := 'Error: A unique constraint was violated while creating customer "' || p_full_name || '", but could not retrieve existing ID.';
            -- p_new_customer_id remains NULL
        END IF;
        RAISE WARNING USING MESSAGE = p_message;
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT;
        p_new_customer_id := NULL; -- Ensure ID is null on other errors
        -- The transaction will be rolled back automatically on unhandled exception if not caught by caller.
END;
$$;

-- Stored Procedure to create a new payment method or return existing
CREATE OR REPLACE PROCEDURE sp_create_payment_method(
    p_tenant_id UUID,
    p_method_name VARCHAR(100),
    p_description TEXT, -- Accepted to match call, but not stored in payment_methods table currently
    p_is_active BOOLEAN,
    OUT p_new_payment_method_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_payment_method_id UUID;
    v_existing_is_active BOOLEAN;
BEGIN
    p_message := '';
    p_new_payment_method_id := NULL;

    -- Validate required inputs
    IF p_tenant_id IS NULL OR p_method_name IS NULL OR TRIM(p_method_name) = '' THEN
        p_message := 'Tenant ID and Method Name cannot be empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if payment method with the same name already exists for this tenant
    SELECT payment_method_id, is_active INTO v_existing_payment_method_id, v_existing_is_active
    FROM payment_methods pm
    WHERE pm.tenant_id = p_tenant_id AND pm.method_name = p_method_name;

    IF v_existing_payment_method_id IS NOT NULL THEN
        p_new_payment_method_id := v_existing_payment_method_id;
        IF v_existing_is_active = p_is_active THEN
            p_message := 'Payment method "' || p_method_name || '" already exists with ID ' || p_new_payment_method_id || ' and has the desired active status.';
        ELSE
            -- If it exists but active status is different, update it
            UPDATE payment_methods
            SET is_active = p_is_active, updated_at = CURRENT_TIMESTAMP
            WHERE payment_method_id = v_existing_payment_method_id AND tenant_id = p_tenant_id;
            p_message := 'Payment method "' || p_method_name || '" already exists with ID ' || p_new_payment_method_id || '. Its active status has been updated to ' || p_is_active || '.';
        END IF;
        RETURN;
    END IF;

    INSERT INTO payment_methods (tenant_id, method_name, is_active, created_at, updated_at)
    VALUES (p_tenant_id, p_method_name, p_is_active, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING payment_method_id INTO p_new_payment_method_id;

    p_message := 'Payment method "' || p_method_name || '" created successfully with ID ' || p_new_payment_method_id || '.';

EXCEPTION
    WHEN unique_violation THEN -- Handles unique constraint violation on (tenant_id, method_name)
        SELECT payment_method_id INTO v_existing_payment_method_id
        FROM payment_methods pm WHERE pm.tenant_id = p_tenant_id AND pm.method_name = p_method_name;

        IF v_existing_payment_method_id IS NOT NULL THEN
            p_new_payment_method_id := v_existing_payment_method_id;
            p_message := 'Error: Payment method "' || p_method_name || '" already exists (recovered via unique_violation) with ID ' || p_new_payment_method_id || '.';
        ELSE
            p_message := 'Error: A unique constraint was violated while creating payment method "' || p_method_name || '", but could not retrieve existing ID.';
        END IF;
        RAISE WARNING USING MESSAGE = p_message;
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT;
        p_new_payment_method_id := NULL;
END;
$$;

-- Stored Procedure to create a new transaction status
CREATE OR REPLACE PROCEDURE sp_create_transaction_status(
    p_tenant_id UUID,
    p_status_name VARCHAR(100),
    p_description TEXT,
    OUT p_new_transaction_status_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_status_id UUID;
BEGIN
    p_message := '';
    p_new_transaction_status_id := NULL;

    -- Validate required inputs
    IF p_tenant_id IS NULL OR p_status_name IS NULL OR TRIM(p_status_name) = '' THEN
        p_message := 'Tenant ID and Status Name cannot be empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if status with the same name already exists for this tenant
    SELECT transaction_status_id INTO v_existing_status_id
    FROM transaction_statuses ts
    WHERE ts.tenant_id = p_tenant_id AND ts.status_name = p_status_name;

    IF v_existing_status_id IS NOT NULL THEN
        p_new_transaction_status_id := v_existing_status_id;
        p_message := 'Transaction status "' || p_status_name || '" already exists with ID ' || p_new_transaction_status_id || '.';
        RETURN;
    END IF;

    INSERT INTO transaction_statuses (
        tenant_id, status_name, description, is_active, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_status_name, p_description, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING transaction_status_id INTO p_new_transaction_status_id;

    p_message := 'Transaction status "' || p_status_name || '" created successfully with ID ' || p_new_transaction_status_id || '.';

EXCEPTION
    WHEN unique_violation THEN
        SELECT transaction_status_id INTO v_existing_status_id
        FROM transaction_statuses ts WHERE ts.tenant_id = p_tenant_id AND ts.status_name = p_status_name;

        IF v_existing_status_id IS NOT NULL THEN
            p_new_transaction_status_id := v_existing_status_id;
            p_message := 'Error: Transaction status "' || p_status_name || '" already exists with ID ' || p_new_transaction_status_id || '.';
        ELSE
            p_message := 'Error: A unique constraint was violated while creating transaction status "' || p_status_name || '".';
        END IF;
        RAISE WARNING USING MESSAGE = p_message;
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT;
        p_new_transaction_status_id := NULL;
END;
$$;

-- Stored Procedure to create a new inventory adjustment type
CREATE OR REPLACE PROCEDURE sp_create_inventory_adjustment_type(
    p_tenant_id UUID,
    p_type_name VARCHAR(100),
    p_description TEXT,
    p_action VARCHAR(20),
    OUT p_new_adjustment_type_id UUID,
    OUT p_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_type_id UUID;
BEGIN
    p_message := '';
    p_new_adjustment_type_id := NULL;

    -- Validate required inputs
    IF p_tenant_id IS NULL OR p_type_name IS NULL OR TRIM(p_type_name) = '' THEN
        p_message := 'Tenant ID and Type Name cannot be empty.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Validate action
    IF p_action IS NULL OR p_action NOT IN ('INCREASE', 'DECREASE', 'SET_TO') THEN
        p_message := 'Action must be one of: INCREASE, DECREASE, SET_TO.';
        RAISE EXCEPTION USING MESSAGE = p_message;
    END IF;

    -- Check if type with the same name already exists for this tenant
    SELECT adjustment_type_id INTO v_existing_type_id
    FROM inventory_adjustment_types iat
    WHERE iat.tenant_id = p_tenant_id AND iat.type_name = p_type_name;

    IF v_existing_type_id IS NOT NULL THEN
        p_new_adjustment_type_id := v_existing_type_id;
        p_message := 'Inventory adjustment type "' || p_type_name || '" already exists with ID ' || p_new_adjustment_type_id || '.';
        RETURN;
    END IF;

    INSERT INTO inventory_adjustment_types (
        tenant_id, type_name, description, action, is_active, created_at, updated_at
    )
    VALUES (
        p_tenant_id, p_type_name, p_description, p_action, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING adjustment_type_id INTO p_new_adjustment_type_id;

    p_message := 'Inventory adjustment type "' || p_type_name || '" created successfully with ID ' || p_new_adjustment_type_id || '.';

EXCEPTION
    WHEN unique_violation THEN
        SELECT adjustment_type_id INTO v_existing_type_id
        FROM inventory_adjustment_types iat WHERE iat.tenant_id = p_tenant_id AND iat.type_name = p_type_name;

        IF v_existing_type_id IS NOT NULL THEN
            p_new_adjustment_type_id := v_existing_type_id;
            p_message := 'Error: Inventory adjustment type "' || p_type_name || '" already exists with ID ' || p_new_adjustment_type_id || '.';
        ELSE
            p_message := 'Error: A unique constraint was violated while creating inventory adjustment type "' || p_type_name || '".';
        END IF;
        RAISE WARNING USING MESSAGE = p_message;
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS p_message = MESSAGE_TEXT;
        p_new_adjustment_type_id := NULL;
END;
$$;

-- Function to search medicines
CREATE OR REPLACE FUNCTION fn_search_medicines(
    p_tenant_id UUID,
    p_search_term TEXT
)
RETURNS TABLE(
    medicine_id UUID,
    medicine_name VARCHAR(255),
    description TEXT,
    category_name VARCHAR(100),
    manufacturer VARCHAR(255),
    available_stock NUMERIC
) AS $$
BEGIN
    -- Validate input parameters
    IF p_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Tenant ID cannot be null';
    END IF;

    -- Handle NULL or empty search term
    IF p_search_term IS NULL OR TRIM(p_search_term) = '' THEN
        RAISE EXCEPTION 'Search term cannot be null or empty';
    END IF;
    RETURN QUERY
    SELECT
        m.medicine_id,
        m.medicine_name,
        m.description,
        c.category_name,
        m.manufacturer,
        fn_get_available_stock_for_medicine(p_tenant_id, m.medicine_id) as available_stock
    FROM
        medicines m
    LEFT JOIN
        categories c ON m.category_id = c.category_id AND m.tenant_id = c.tenant_id
    WHERE
        m.tenant_id = p_tenant_id
        AND m.is_active = TRUE
        AND (
            m.medicine_name ILIKE '%' || p_search_term || '%'
            OR m.description ILIKE '%' || p_search_term || '%'
            OR m.registration_number ILIKE '%' || p_search_term || '%'
            OR m.manufacturer ILIKE '%' || p_search_term || '%'
            OR m.active_ingredients ILIKE '%' || p_search_term || '%'
        )
    ORDER BY
        CASE WHEN m.medicine_name ILIKE p_search_term || '%' THEN 1
             WHEN m.medicine_name ILIKE '%' || p_search_term || '%' THEN 2
             ELSE 3
        END;
END;
$$ LANGUAGE plpgsql;

-- Function to get medicines expiring soon
CREATE OR REPLACE FUNCTION fn_get_expiring_medicines(
    p_tenant_id UUID,
    p_days_threshold INTEGER DEFAULT 90
)
RETURNS TABLE(
    medicine_id UUID,
    medicine_name VARCHAR(255),
    batch_number VARCHAR(100),
    expiry_date DATE,
    days_until_expiry INTEGER,
    quantity_remaining NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.medicine_id,
        m.medicine_name,
        b.batch_number,
        b.expiry_date::date,
        (b.expiry_date::date - CURRENT_DATE) AS days_until_expiry,
        b.quantity_remaining
    FROM
        batches b
    JOIN
        medicines m ON b.medicine_id = m.medicine_id AND b.tenant_id = m.tenant_id
    WHERE
        b.tenant_id = p_tenant_id
        AND b.quantity_remaining > 0
        AND b.expiry_date >= CURRENT_DATE
        AND (b.expiry_date::date - CURRENT_DATE) <= p_days_threshold
    ORDER BY
        days_until_expiry ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to generate sales report
CREATE OR REPLACE FUNCTION fn_generate_sales_report(
    p_tenant_id UUID,
    p_start_date DATE,
    p_end_date DATE
)
RETURNS TABLE(
    transaction_date DATE,
    total_sales NUMERIC,
    total_transactions INTEGER,
    average_transaction_value NUMERIC
) AS $$
BEGIN
    -- Validate input parameters
    IF p_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Tenant ID cannot be null';
    END IF;

    IF p_start_date IS NULL THEN
        RAISE EXCEPTION 'Start date cannot be null';
    END IF;

    IF p_end_date IS NULL THEN
        RAISE EXCEPTION 'End date cannot be null';
    END IF;

    IF p_end_date < p_start_date THEN
        RAISE EXCEPTION 'End date cannot be earlier than start date';
    END IF;

    RETURN QUERY
    SELECT
        DATE(st.created_at) AS transaction_date,
        SUM(st.total_amount) AS total_sales,
        COUNT(st.sale_transaction_id) AS total_transactions,
        CASE
            WHEN COUNT(st.sale_transaction_id) > 0 THEN SUM(st.total_amount) / COUNT(st.sale_transaction_id)
            ELSE 0
        END AS average_transaction_value
    FROM
        sales_transactions st
    WHERE
        st.tenant_id = p_tenant_id
        AND DATE(st.created_at) BETWEEN p_start_date AND p_end_date
    GROUP BY
        DATE(st.created_at)
    ORDER BY
        transaction_date;
END;
$$ LANGUAGE plpgsql;
