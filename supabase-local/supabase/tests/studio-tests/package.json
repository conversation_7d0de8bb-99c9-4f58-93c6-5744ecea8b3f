{"name": "studio-tests", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"preinstall": "npx only-allow pnpm", "env:setup": "supabase start -x studio && supabase status --output json > keys.json && node generate-local-env.js", "pretest:local": "pnpm env:setup", "test:local": "pnpm env:setup && export ENV=local && export PROJECT_REF=default && playwright test", "posttest:local": "supabase stop --no-backup", "test:staging": "export ENV=staging && playwright test", "codegen:setup": "pnpm env:setup && NODE_ENV=test pnpm --prefix ../../apps/studio dev", "codegen": "playwright codegen http://localhost:8082/project/default", "clean": "rimraf node_modules"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.49.0", "lodash": "^4.17.21", "dotenv": "^16.4.7"}}