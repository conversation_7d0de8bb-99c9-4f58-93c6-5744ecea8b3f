import { Page, expect } from '@playwright/test'
import { kebabCase } from 'lodash'
import { test } from '../../base'

const dismissToast = async (page: Page) => {
  await page
    .locator('li.toast')
    .getByRole('button', { name: 'Opt out' })
    .waitFor({ state: 'visible' })
  await page.locator('li.toast').getByRole('button', { name: 'Opt out' }).click()
}

test.describe('Table Editor', () => {
  test.beforeEach(async ({ page, env, ref, apiUrl }) => {
    const tableResponsePromise = page.waitForResponse(
      `${apiUrl}/platform/pg-meta/${ref}/query?key=entity-types-public-0`,
      { timeout: 0 }
    )
    await page.goto(`./project/${ref}/editor`)
    if (env !== 'local') await dismissToast(page)
    await tableResponsePromise
  })

  test('should create a new table, view its definition, add new rows, sort and filter', async ({
    page,
    ref,
    apiUrl,
  }, testInfo) => {
    const tableName = `${kebabCase(testInfo.title).slice(0, 24)}-${testInfo.retry}-${Math.floor(Math.random() * 10000)}`

    // The page has been loaded with the table data, we can now interact with the page
    await page.getByRole('button', { name: 'New table', exact: true }).click()

    await page.getByTestId('table-name-input').waitFor({ state: 'visible' })
    await page.getByTestId('table-name-input').click()
    await page.getByTestId('table-name-input').fill(tableName)

    // make the built-in created_at column nullable
    await page.getByTestId('created_at-extra-options').click()
    await page.getByText('Is Nullable').click()
    // the force option is needed because the button is obscured by the popover but we just want to close the popover.
    await page.getByTestId('created_at-extra-options').click({ force: true })

    // add a new column and add default value
    await page.getByRole('button', { name: 'Add column' }).click()
    await page.getByRole('textbox', { name: 'column_name' }).click()
    await page.getByRole('textbox', { name: 'column_name' }).fill('defaultValueColumn')
    await page.locator('button').filter({ hasText: 'Choose a column type...' }).click()
    await page.getByText('Signed two-byte integer').click()
    await page.getByTestId('defaultValueColumn-default-value').click()
    await page.getByTestId('defaultValueColumn-default-value').fill('2')

    await page.getByRole('button', { name: 'Save' }).waitFor({ state: 'visible' })
    await page.getByRole('button', { name: 'Save' }).click()

    // hide React Query DevTools if present
    await page.evaluate(() => {
      const devtools = document.querySelector('.ReactQueryDevtools')
      if (devtools) {
        devtools.remove()
      }
    })

    // view its definition
    await page.getByText('definition').click()
    await expect(page.locator('div.view-lines')).toContainText(
      `CREATE  TABLE public.${tableName} (  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,  created_at timestamp with time zone NULL DEFAULT now(),  \"defaultValueColumn\" smallint NULL DEFAULT '2'::smallint,  CONSTRAINT ${tableName}_pkey PRIMARY KEY (id)) TABLESPACE pg_default;`
    )

    // add a new row
    await page.getByRole('button', { name: tableName }).click()
    await page.getByTestId('table-editor-insert-new-row').click()
    await page.getByText('Insert a new row into').click()
    await page.getByTestId('defaultValueColumn-input').click()
    await page.getByTestId('defaultValueColumn-input').fill('100')
    await page.getByTestId('action-bar-save-row').click()

    // add a second row
    await page.getByRole('button', { name: tableName }).click()
    await page.getByTestId('table-editor-insert-new-row').click()
    await page.getByText('Insert a new row into').click()
    // the default value should be '100' for defaultValueColumn
    await page.getByTestId('action-bar-save-row').click()

    // Wait for both rows to be visible in the grid
    await page.waitForResponse((response) =>
      response.url().includes(`${apiUrl}/platform/pg-meta/${ref}/query`)
    )
    await expect(page.getByRole('grid')).toContainText('2')
    await expect(page.getByRole('grid')).toContainText('100')

    // Make sure we can see both rows in the grid before sorting
    const rows = page.getByRole('row')
    await expect(rows).toHaveCount(3) // header row + 2 data rows

    // sort by the a column
    await page.getByRole('button', { name: 'Sort' }).click()
    await page.getByTestId('table-editor-pick-column-to-sort-button').click()
    await page.getByLabel('Pick a column to sort by').getByText('defaultValueColumn').click()
    await page.getByRole('button', { name: 'Apply sorting' }).click()

    // Close the sorting dialog
    await page.keyboard.down('Escape')

    // expect the row to be sorted by defaultValueColumn. They're inserted in the order 100, 2
    await expect(rows.nth(1)).toContainText('2')
    await expect(rows.nth(2)).toContainText('100')
    // remove the sorting
    await page.getByRole('button', { name: 'Sorted by 1 rule' }).click()
    await page.getByRole('dialog').getByRole('button').nth(1).click()

    // filter by a column
    await page.getByRole('button', { name: 'Filter' }).click()
    await page.getByRole('button', { name: 'Add filter' }).click()
    await page.getByRole('button', { name: 'id' }).click()
    await page.getByLabel('id').getByText('defaultValueColumn').click()
    await page.getByPlaceholder('Enter a value').click()
    await page.getByPlaceholder('Enter a value').fill('2')
    await page.getByRole('button', { name: 'Apply filter' }).click()

    // Close the filter dialog
    await page.keyboard.down('Escape')

    await expect(page.getByRole('grid')).toContainText('2')
    await expect(page.getByRole('grid')).not.toContainText('100')

    // Delete the table as clean up
    await page.getByLabel(`View ${tableName}`).click()
    await page.getByLabel(`View ${tableName}`).getByRole('button').nth(1).click()
    await page.getByText('Delete table').click()
    await page.getByRole('button', { name: 'Delete' }).click()
    await page.waitForResponse((response) =>
      response.url().includes(`${apiUrl}/platform/pg-meta/${ref}/tables`)
    )
  })

  test('should check the auth schema', async ({ page }) => {
    await page.getByTestId('schema-selector').click()
    await page.getByRole('option', { name: 'auth' }).click()

    // extract the tables names from the sidebar
    const tables = await page
      .getByTestId('tables-list')
      .innerText()
      .then((text) => text.split('\n'))

    // expect the tables list to contain the following tables (additional tables may be present)
    expect(tables).toEqual(
      expect.arrayContaining([
        'audit_log_entries',
        'flow_state',
        'identities',
        'instances',
        'mfa_amr_claims',
        'mfa_challenges',
        'mfa_factors',
        'refresh_tokens',
        'saml_providers',
        'saml_relay_states',
        'schema_migrations',
        'sessions',
        'sso_domains',
        'sso_providers',
        'users',
      ])
    )
  })
})
