{
  "$schema": "https://turbo.build/schema.json",
  "ui": "stream",
  "tasks": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**"]
    },
    "design-system#build": {
      "dependsOn": ["^build"],
      "env": [
        "NEXT_PUBLIC_BASE_PATH",
        "NEXT_PUBLIC_APP_URL",
        // These envs are used in the packages
        "NEXT_PUBLIC_STORAGE_KEY",
        "NEXT_PUBLIC_AUTH_DEBUG_KEY",
        "NEXT_PUBLIC_AUTH_PERSISTED_KEY",
        "NEXT_PUBLIC_AUTH_NAVIGATOR_LOCK_KEY",
        "NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL",
        "NEXT_PUBLIC_GOTRUE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
      ],
      "outputs": [".next/**", "!.next/cache/**", ".contentlayer/**"]
    },
    "studio#build": {
      "dependsOn": ["^build"],
      "env": [
        "ANALYZE",
        "NEXT_PUBLIC_SUPPORT_API_URL",
        "NEXT_PUBLIC_BASE_PATH",
        "NEXT_PUBLIC_STRIPE_PUBLIC_KEY",
        "NEXT_PUBLIC_SUPPORT_ANON_KEY",
        "NEXT_PUBLIC_ENVIRONMENT",
        "NEXT_PUBLIC_IS_PLATFORM",
        "NEXT_PUBLIC_SITE_URL",
        "NEXT_PUBLIC_API_URL",
        "NEXT_PUBLIC_CONFIGCAT_SDK_KEY",
        "NEXT_PUBLIC_HCAPTCHA_SITE_KEY",
        "NEXT_PUBLIC_SUPABASE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY",
        "NEXT_PUBLIC_NODE_ENV",
        "NEXT_PUBLIC_GOTRUE_URL",
        "NEXT_PUBLIC_VERCEL_BRANCH_URL",
        "NODE_ENV",
        "SUPABASE_URL",
        // These envs are used in the packages
        "NEXT_PUBLIC_STORAGE_KEY",
        "NEXT_PUBLIC_AUTH_DEBUG_KEY",
        "NEXT_PUBLIC_AUTH_PERSISTED_KEY",
        "NEXT_PUBLIC_AUTH_NAVIGATOR_LOCK_KEY",
        "NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL",
        "NEXT_PUBLIC_VERCEL_ENV",
        "NEXT_PUBLIC_USERCENTRICS_RULESET_ID",
        // These envs are technically passthrough env vars because they're only used on the server side of Nextjs
        "PLATFORM_PG_META_URL",
        "STUDIO_PG_META_URL",
        "READ_ONLY_URL",
        "READ_ONLY_API_KEY",
        "SUPABASE_SERVICE_KEY",
        "SUPABASE_ANON_KEY",
        "SUPABASE_PUBLIC_URL",
        "DEFAULT_PROJECT_NAME",
        "DEFAULT_ORGANIZATION_NAME",
        "OPENAI_API_KEY",
        "AUTH_JWT_SECRET",
        "LOGFLARE_URL",
        "LOGFLARE_API_KEY",
        "SENTRY_ORG",
        "SENTRY_PROJECT",
        "SENTRY_AUTH_TOKEN",
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY",
        "FORCE_ASSET_CDN",
        "ASSET_CDN_S3_ENDPOINT",
        "SITE_NAME",
        "VERCEL_URL"
      ],
      "passThroughEnv": ["CURRENT_CLI_VERSION"],
      "outputs": [".next/**", "!.next/cache/**"]
    },
    "www#build": {
      "dependsOn": ["^build"],
      "env": [
        "ANALYZE",
        "NEXT_PUBLIC_MISC_USE_URL",
        "NEXT_PUBLIC_MISC_USE_ANON_KEY",
        "NEXT_PUBLIC_STUDIO_URL",
        "NEXT_PUBLIC_DOCS_URL",
        "NEXT_PUBLIC_REFERENCE_DOCS_URL",
        "NEXT_PUBLIC_UI_LIBRARY_URL",
        "NEXT_PUBLIC_SUPABASE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY",
        "NEXT_PUBLIC_VERCEL_ENV",
        "NEXT_PUBLIC_VERCEL_BRANCH_URL",
        "NEXT_PUBLIC_API_URL",
        "NEXT_PUBLIC_HCAPTCHA_SITE_KEY",
        "HCAPTCHA_SECRET_KEY",
        "NODE_ENV",
        // These envs are used in the packages
        "NEXT_PUBLIC_STORAGE_KEY",
        "NEXT_PUBLIC_AUTH_DEBUG_KEY",
        "NEXT_PUBLIC_AUTH_PERSISTED_KEY",
        "NEXT_PUBLIC_AUTH_NAVIGATOR_LOCK_KEY",
        "NEXT_PUBLIC_IS_PLATFORM",
        "NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL",
        "NEXT_PUBLIC_GOTRUE_URL",
        "NEXT_PUBLIC_BASE_PATH",
        "NEXT_PUBLIC_EXAMPLES_SUPABASE_ANON_KEY",
        "NEXT_PUBLIC_EXAMPLES_SUPABASE_URL",
        "NEXT_PUBLIC_USERCENTRICS_RULESET_ID",
        // These envs are technically passthrough env vars because they're only used on the server side of Nextjs
        "LIVE_SUPABASE_COM_SERVICE_ROLE_KEY",
        "GITHUB_CHANGELOG_APP_ID",
        "GITHUB_CHANGELOG_APP_INSTALLATION_ID",
        "GITHUB_CHANGELOG_APP_REST_KEY",
        "GITHUB_CHANGELOG_APP_PRIVATE_KEY",
        "NEXT_PUBLIC_EMAIL_ABUSE_URL",
        "EMAIL_ABUSE_SERVICE_KEY",
        "HUBSPOT_PORTAL_ID",
        "HUBSPOT_ENTERPRISE_FORM_GUID",
        "HUBSPOT_PARTNERSHIP_FORM_GUID",
        "OPENAI_API_KEY",
        "EMAIL_REPORT_SLACK_WEBHOOK",
        "npm_lifecycle_event",
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY",
        "FORCE_ASSET_CDN",
        "ASSET_CDN_S3_ENDPOINT",
        "SITE_NAME"
      ],
      "outputs": [".next/**", "!.next/cache/**", ".contentlayer/**"]
    },
    "ui-library#build": {
      "dependsOn": ["^build"],
      "env": [
        "NEXT_PUBLIC_BASE_PATH",
        "NEXT_PUBLIC_APP_URL",
        "NEXT_PUBLIC_VERCEL_TARGET_ENV",
        "NEXT_PUBLIC_VERCEL_BRANCH_URL",
        "NEXT_PUBLIC_API_URL",
        "NODE_ENV",
        "HIDE_TAILWIND_INDICATOR",
        // These envs are used in the packages
        "NEXT_PUBLIC_STORAGE_KEY",
        "NEXT_PUBLIC_AUTH_DEBUG_KEY",
        "NEXT_PUBLIC_AUTH_PERSISTED_KEY",
        "NEXT_PUBLIC_AUTH_NAVIGATOR_LOCK_KEY",
        "NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL",
        "NEXT_PUBLIC_GOTRUE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY",
        "NEXT_PUBLIC_USERCENTRICS_RULESET_ID"
      ],
      "outputs": [".next/**", "!.next/cache/**", ".contentlayer/**"]
    },
    "lint": {
      "outputs": []
    },
    "clean": {
      "outputs": [],
      "cache": false
    },
    "dev": {
      "cache": false
    },
    "test": {
      "cache": false
    },
    "typecheck": {
      "dependsOn": ["^typecheck"],
      "outputs": []
    }
  }
}
