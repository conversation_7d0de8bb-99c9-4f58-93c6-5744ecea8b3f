create extension if not exists "uuid-ossp";

create table public.launch_weeks (
  id text not null primary key, -- 'lw12', 'lw13', etc
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  start_date timestamp with time zone null,
  end_date timestamp with time zone null
);

alter table public.launch_weeks enable row level security;

create policy "Allow public read access"
on "public"."launch_weeks"
as PERMISSIVE
for select
using ( true );

insert into public.launch_weeks (id) values ('lw12');

create table
  public.tickets (
    id uuid not null default uuid_generate_v4(),
    created_at timestamp with time zone not null default timezone('utc'::text, now()),
    launch_week text not null references public.launch_weeks (id),
    user_id uuid not null references auth.users (id),
    email text null,
    name text null,
    username text null,
    referred_by text null,
    shared_on_twitter timestamp with time zone null,
    shared_on_linkedin timestamp with time zone null,
    game_won_at timestamp with time zone null,
    ticket_number bigint generated by default as identity,
    metadata jsonb null,
    role text null,
    company text null,
    location text null,
    constraint tickets_pkey primary key (id),
    constraint tickets_email_key unique (email, launch_week),
    constraint tickets_ticket_number_key unique (ticket_number, launch_week),
    constraint tickets_username_key unique (username, launch_week),
    constraint public_tickets_id_fkey foreign key (user_id) references auth.users (id)
  );

alter table public.tickets enable row level security;
alter publication supabase_realtime add table public.tickets;

GRANT UPDATE (role) ON TABLE public.tickets TO authenticated;
GRANT UPDATE (company) ON TABLE public.tickets TO authenticated;
GRANT UPDATE (location) ON TABLE public.tickets TO authenticated;

create policy "Allow user to select own ticket"
on public.tickets
as PERMISSIVE
for SELECT
to authenticated
using (user_id = auth.uid());

create policy "Allow authenticated user to update its own ticket"
on public.tickets
as permissive
for update
to authenticated
using (user_id = auth.uid())
with check (user_id = auth.uid());

create policy "Allow insert for authenticated users only"
on public.tickets
as permissive
for insert
to authenticated
with check (user_id = auth.uid());

-- public view without sensible data
create or replace view
  public.tickets_view with (security_invoker=on) as
with
  lw12_referrals as (
    select
      tickets_1.referred_by,
      count(*) as referrals
    from
      tickets tickets_1
    where
      tickets_1.referred_by is not null
    group by
      tickets_1.referred_by
  )
select
  tickets.id,
  tickets.name,
  tickets.username,
  tickets.ticket_number,
  tickets.created_at,
  tickets.launch_week,
  tickets.shared_on_twitter,
  tickets.shared_on_linkedin,
  tickets.metadata,
  tickets.role,
  tickets.company,
  tickets.location,
  case
    when lw12_referrals.referrals is null then 0::bigint
    else lw12_referrals.referrals
  end as referrals,
  case
    when tickets.shared_on_twitter is not null
    and tickets.shared_on_linkedin is not null then true
    else false
  end as platinum,
  case
    when tickets.game_won_at is not null then true
    else false
  end as secret
from
  tickets
  left join lw12_referrals on tickets.username = lw12_referrals.referred_by;

-- Create meetups table
create table
  public.meetups (
    id uuid not null default uuid_generate_v4(),
    created_at timestamp with time zone not null default now(),
    launch_week text not null references public.launch_weeks (id),
    title text null,
    country text null,
    start_at timestamp with time zone null,
    link text null,
    display_info text null,
    is_live boolean not null default false,
    is_published boolean not null default false,
    constraint meetups_pkey primary key (id)
  );

alter table public.meetups enable row level security;
alter publication supabase_realtime add table public.meetups;

create policy "Allow anybody to select all meetups"
on public.meetups
as permissive
for select
using (true);