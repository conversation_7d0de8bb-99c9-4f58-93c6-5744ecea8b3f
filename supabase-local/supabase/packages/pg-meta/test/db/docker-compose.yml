services:
  db:
    build: .
    ports:
      - 5432:5432
    volumes:
      - .:/docker-entrypoint-initdb.d
    environment:
      POSTGRES_PASSWORD: postgres
    command: postgres -c config_file=/etc/postgresql/postgresql.conf -c ssl=on -c ssl_cert_file=/var/lib/postgresql/server.crt -c ssl_key_file=/var/lib/postgresql/server.key
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 1s
      timeout: 2s
      retries: 10
      start_period: 2s
