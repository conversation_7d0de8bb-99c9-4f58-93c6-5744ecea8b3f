{"name": "@supabase/pg-meta", "version": "0.0.0", "license": "MIT", "author": "Supabase", "main": "./src/index.ts", "repository": "supabase/supabase", "scripts": {"preinstall": "npx only-allow pnpm", "clean": "rimraf node_modules", "test": "run-s db:clean db:run test:run db:clean", "db:clean": "cd test/db && docker compose down", "db:run": "cd test/db && docker compose up --detach --wait", "test:run": "vitest run --coverage", "test:update": "vitest run --update", "lint": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/pg": "^8.11.11", "@vitest/coverage-v8": "^3.0.9", "npm-run-all": "^4.1.5", "pg": "^8.13.1", "postgres-array": "^3.0.2", "typescript": "~5.5.0", "vite": "^6.2.6", "vitest": "^3.0.5"}}