//
// PostgreSQL reserved words
// see: https://www.postgresql.org/docs/current/sql-keywords-appendix.html
//

export const POSTGRESQL_RESERVED_WORDS = new Set([
  'AES128',
  'AES256',
  'AL<PERSON>',
  'AL<PERSON>OWOVERWRITE',
  'ANALY<PERSON>',
  'ANALYZ<PERSON>',
  'AND',
  'ANY',
  'ARRAY',
  'AS',
  'ASC',
  'ASYMMETRIC',
  'AUTHORIZATION',
  'BACKUP',
  'BETWEEN',
  'BINARY',
  'BLANKSASNULL',
  'BOTH',
  'BYTEDICT',
  'CASE',
  'CAST',
  'CHECK',
  'COLLATE',
  'COLUMN',
  'CONSTRAINT',
  'CREATE',
  'CREDENTIALS',
  'CROSS',
  'CURRENT_CATALOG',
  'CURRENT_DATE',
  'CURRENT_ROLE',
  'CURRENT_TIME',
  'CURRENT_TIMESTAMP',
  'CURRENT_USER',
  'CURRENT_USER_ID',
  'DEFAULT',
  'DEFERRABLE',
  'DEFLATE',
  'DEFRAG',
  'DELTA',
  'DELTA32K',
  'DESC',
  'DISABLE',
  'DISTINCT',
  'DO',
  'ELSE',
  'EMPTYASNULL',
  'ENABLE',
  'ENCODE',
  'ENCRYPT',
  'ENCRYPTION',
  'END',
  'EXCEPT',
  'EXPLICIT',
  'FALSE',
  'FETCH',
  'FOR',
  'FOREIGN',
  'FREEZE',
  'FROM',
  'FULL',
  'GLOBALDICT256',
  'GLOBALDICT64K',
  'GRANT',
  'GROUP',
  'GZIP',
  'HAVING',
  'IDENTITY',
  'IGNORE',
  'ILIKE',
  'IN',
  'INITIALLY',
  'INNER',
  'INTERSECT',
  'INTO',
  'IS',
  'ISNULL',
  'JOIN',
  'LATERAL',
  'LEADING',
  'LEFT',
  'LIKE',
  'LIMIT',
  'LOCALTIME',
  'LOCALTIMESTAMP',
  'LUN',
  'LUNS',
  'LZO',
  'LZOP',
  'MINUS',
  'MOSTLY13',
  'MOSTLY32',
  'MOSTLY8',
  'NATURAL',
  'NEW',
  'NOT',
  'NOTNULL',
  'NULL',
  'NULLS',
  'OFF',
  'OFFLINE',
  'OFFSET',
  'OLD',
  'ON',
  'ONLY',
  'OPEN',
  'OR',
  'ORDER',
  'OUTER',
  'OVERLAPS',
  'PARALLEL',
  'PARTITION',
  'PERCENT',
  'PLACING',
  'PRIMARY',
  'RAW',
  'READRATIO',
  'RECOVER',
  'REFERENCES',
  'REJECTLOG',
  'RESORT',
  'RESTORE',
  'RETURNING',
  'RIGHT',
  'SELECT',
  'SESSION_USER',
  'SIMILAR',
  'SOME',
  'SYMMETRIC',
  'SYSDATE',
  'SYSTEM',
  'TABLE',
  'TAG',
  'TDES',
  'TEXT255',
  'TEXT32K',
  'THEN',
  'TO',
  'TOP',
  'TRAILING',
  'TRUE',
  'TRUNCATECOLUMNS',
  'UNION',
  'UNIQUE',
  'USER',
  'USING',
  'VARIADIC',
  'VERBOSE',
  'WALLET',
  'WHEN',
  'WHERE',
  'WINDOW',
  'WITH',
  'WITHOUT',
])
