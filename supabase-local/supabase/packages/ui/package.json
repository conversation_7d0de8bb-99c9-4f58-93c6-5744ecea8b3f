{"name": "ui", "version": "0.0.0", "main": "./index.tsx", "types": "./index.tsx", "license": "MIT", "sideEffects": false, "scripts": {"preinstall": "npx only-allow pnpm", "typecheck": "tsc --noEmit", "generate-demo-tailwind-classes": "node internals/tokens/generate-demo-tailwind-classes.js", "cleanse-css-for-tailwind": "node internals/tokens/cleanse-css-for-tailwind.js", "transform-tokens": "node ./transformTokens.js", "extract-design-tokens": "node internals/tokens/extract-design-tokens.js", "generate-styles": "pnpm run extract-design-tokens && pnpm run transform-tokens && pnpm run cleanse-css-for-tailwind && pnpm run generate-demo-tailwind-classes", "clean": "rimraf node_modules", "test": "vitest", "test:ci": "vitest --run --coverage", "test:report": "open coverage/lcov-report/index.html"}, "dependencies": {"@headlessui/react": "^1.7.17", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context": "^1.0.1", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.6", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.9", "autoprefixer": "^10.4.14", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "cmdk": "^1.0.0", "color": "^4.2.3", "date-fns": "^2.30.0", "formik": "^2.2.9", "framer-motion": "^11.0.3", "highlightjs-curl": "^1.3.0", "input-otp": "^1.2.3", "lodash": "^4.17.21", "lucide-react": "^0.436.0", "next-themes": "^0.3.0", "prism-react-renderer": "^2.3.1", "prop-types": "^15.7.2", "react": "catalog:", "react-accessible-treeview": "^2.8.3", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^8.8.0", "react-dom": "catalog:", "react-hook-form": "^7.45.0", "react-intersection-observer": "^9.8.2", "react-medium-image-zoom": "^5.2.4", "react-resizable-panels": "^2.1.4", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^1.13.2", "tailwindcss": "^3.4.1", "vaul": "^0.9.1"}, "devDependencies": {"@ctrl/tinycolor": "^3.4.0", "@testing-library/jest-dom": "^6.6.0", "@testing-library/react": "^16.0.0", "@types/lodash": "4.17.5", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "catalog:", "@types/react-syntax-highlighter": "^15.5.6", "@vitest/coverage-v8": "^3.0.9", "common": "workspace:*", "config": "workspace:*", "glob": "^8.1.0", "style-dictionary": "^3.7.1", "tsconfig": "workspace:*", "typescript": "~5.5.0", "vite": "^6.2.6", "vitest": "^3.0.5"}, "peerDependencies": {"next": "catalog:"}}