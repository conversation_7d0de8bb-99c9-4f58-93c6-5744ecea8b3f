module.exports = {
  'colors-black': {
    cssVariable: 'var(--core-colors-black)',
    value: 'hsl(0, 0%, 0%)',
  },
  'colors-white': {
    cssVariable: 'var(--core-colors-white)',
    value: 'hsl(0, 0%, 100%)',
  },
  'colors-gray-dark-100': {
    cssVariable: 'var(--core-colors-gray-dark-100)',
    value: 'hsl(0, 0%, 8.6%)',
  },
  'colors-gray-dark-200': {
    cssVariable: 'var(--core-colors-gray-dark-200)',
    value: 'hsl(0, 0%, 11%)',
  },
  'colors-gray-dark-300': {
    cssVariable: 'var(--core-colors-gray-dark-300)',
    value: 'hsl(0, 0%, 13.7%)',
  },
  'colors-gray-dark-400': {
    cssVariable: 'var(--core-colors-gray-dark-400)',
    value: 'hsl(0, 0%, 15.7%)',
  },
  'colors-gray-dark-500': {
    cssVariable: 'var(--core-colors-gray-dark-500)',
    value: 'hsl(0, 0%, 18%)',
  },
  'colors-gray-dark-600': {
    cssVariable: 'var(--core-colors-gray-dark-600)',
    value: 'hsl(0, 0%, 20.4%)',
  },
  'colors-gray-dark-700': {
    cssVariable: 'var(--core-colors-gray-dark-700)',
    value: 'hsl(0, 0%, 24.3%)',
  },
  'colors-gray-dark-800': {
    cssVariable: 'var(--core-colors-gray-dark-800)',
    value: 'hsl(0, 0%, 31.4%)',
  },
  'colors-gray-dark-900': {
    cssVariable: 'var(--core-colors-gray-dark-900)',
    value: 'hsl(0, 0%, 43.9%)',
  },
  'colors-gray-dark-1000': {
    cssVariable: 'var(--core-colors-gray-dark-1000)',
    value: 'hsl(0, 0%, 49.4%)',
  },
  'colors-gray-dark-1100': {
    cssVariable: 'var(--core-colors-gray-dark-1100)',
    value: 'hsl(0, 0%, 62.7%)',
  },
  'colors-gray-dark-1200': {
    cssVariable: 'var(--core-colors-gray-dark-1200)',
    value: 'hsl(0, 0%, 92.9%)',
  },
  'colors-gray-dark-alpha-100': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-100)',
    value: 'hsla(0, 0%, 0%, 0)',
  },
  'colors-gray-dark-alpha-200': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-200)',
    value: 'hsla(0, 0%, 100%, 0.03137254901960784)',
  },
  'colors-gray-dark-alpha-300': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-300)',
    value: 'hsla(0, 0%, 100%, 0.058823529411764705)',
  },
  'colors-gray-dark-alpha-400': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-400)',
    value: 'hsla(0, 0%, 100%, 0.0784313725490196)',
  },
  'colors-gray-dark-alpha-500': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-500)',
    value: 'hsla(0, 0%, 100%, 0.10196078431372549)',
  },
  'colors-gray-dark-alpha-600': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-600)',
    value: 'hsla(0, 0%, 100%, 0.12941176470588237)',
  },
  'colors-gray-dark-alpha-700': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-700)',
    value: 'hsla(0, 0%, 100%, 0.16862745098039217)',
  },
  'colors-gray-dark-alpha-800': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-800)',
    value: 'hsla(0, 0%, 100%, 0.25098039215686274)',
  },
  'colors-gray-dark-alpha-900': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-900)',
    value: 'hsla(0, 0%, 100%, 0.38823529411764707)',
  },
  'colors-gray-dark-alpha-1000': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-1000)',
    value: 'hsla(0, 0%, 100%, 0.45098039215686275)',
  },
  'colors-gray-dark-alpha-1100': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-1100)',
    value: 'hsla(0, 0%, 100%, 0.5882352941176471)',
  },
  'colors-gray-dark-alpha-1200': {
    cssVariable: 'var(--core-colors-gray-dark-alpha-1200)',
    value: 'hsla(0, 0%, 100%, 0.9215686274509803)',
  },
  'colors-gray-light-100': {
    cssVariable: 'var(--core-colors-gray-light-100)',
    value: 'hsl(0, 0%, 98.8%)',
  },
  'colors-gray-light-200': {
    cssVariable: 'var(--core-colors-gray-light-200)',
    value: 'hsl(0, 0%, 97.3%)',
  },
  'colors-gray-light-300': {
    cssVariable: 'var(--core-colors-gray-light-300)',
    value: 'hsl(0, 0%, 95.3%)',
  },
  'colors-gray-light-400': {
    cssVariable: 'var(--core-colors-gray-light-400)',
    value: 'hsl(0, 0%, 92.9%)',
  },
  'colors-gray-light-500': {
    cssVariable: 'var(--core-colors-gray-light-500)',
    value: 'hsl(0, 0%, 91%)',
  },
  'colors-gray-light-600': {
    cssVariable: 'var(--core-colors-gray-light-600)',
    value: 'hsl(0, 0%, 88.6%)',
  },
  'colors-gray-light-700': {
    cssVariable: 'var(--core-colors-gray-light-700)',
    value: 'hsl(0, 0%, 85.9%)',
  },
  'colors-gray-light-800': {
    cssVariable: 'var(--core-colors-gray-light-800)',
    value: 'hsl(0, 0%, 78%)',
  },
  'colors-gray-light-900': {
    cssVariable: 'var(--core-colors-gray-light-900)',
    value: 'hsl(0, 0%, 56.1%)',
  },
  'colors-gray-light-1000': {
    cssVariable: 'var(--core-colors-gray-light-1000)',
    value: 'hsl(0, 0%, 52.2%)',
  },
  'colors-gray-light-1100': {
    cssVariable: 'var(--core-colors-gray-light-1100)',
    value: 'hsl(0, 0%, 43.5%)',
  },
  'colors-gray-light-1200': {
    cssVariable: 'var(--core-colors-gray-light-1200)',
    value: 'hsl(0, 0%, 9%)',
  },
  'colors-gray-light-alpha-100': {
    cssVariable: 'var(--core-colors-gray-light-alpha-100)',
    value: 'hsla(0, 0%, 0%, 0.011764705882352941)',
  },
  'colors-gray-light-alpha-200': {
    cssVariable: 'var(--core-colors-gray-light-alpha-200)',
    value: 'hsla(0, 0%, 0%, 0.03137254901960784)',
  },
  'colors-gray-light-alpha-300': {
    cssVariable: 'var(--core-colors-gray-light-alpha-300)',
    value: 'hsla(0, 0%, 0%, 0.050980392156862744)',
  },
  'colors-gray-light-alpha-400': {
    cssVariable: 'var(--core-colors-gray-light-alpha-400)',
    value: 'hsla(0, 0%, 0%, 0.07058823529411765)',
  },
  'colors-gray-light-alpha-500': {
    cssVariable: 'var(--core-colors-gray-light-alpha-500)',
    value: 'hsla(0, 0%, 0%, 0.09019607843137255)',
  },
  'colors-gray-light-alpha-600': {
    cssVariable: 'var(--core-colors-gray-light-alpha-600)',
    value: 'hsla(0, 0%, 0%, 0.10980392156862745)',
  },
  'colors-gray-light-alpha-700': {
    cssVariable: 'var(--core-colors-gray-light-alpha-700)',
    value: 'hsla(0, 0%, 0%, 0.1411764705882353)',
  },
  'colors-gray-light-alpha-800': {
    cssVariable: 'var(--core-colors-gray-light-alpha-800)',
    value: 'hsla(0, 0%, 0%, 0.2196078431372549)',
  },
  'colors-gray-light-alpha-900': {
    cssVariable: 'var(--core-colors-gray-light-alpha-900)',
    value: 'hsla(0, 0%, 0%, 0.4392156862745098)',
  },
  'colors-gray-light-alpha-1000': {
    cssVariable: 'var(--core-colors-gray-light-alpha-1000)',
    value: 'hsla(0, 0%, 0%, 0.47843137254901963)',
  },
  'colors-gray-light-alpha-1100': {
    cssVariable: 'var(--core-colors-gray-light-alpha-1100)',
    value: 'hsla(0, 0%, 0%, 0.5607843137254902)',
  },
  'colors-gray-light-alpha-1200': {
    cssVariable: 'var(--core-colors-gray-light-alpha-1200)',
    value: 'hsla(0, 0%, 0%, 0.9098039215686274)',
  },
  'colors-slate-dark-100': {
    cssVariable: 'var(--core-colors-slate-dark-100)',
    value: 'hsl(200, 6.7%, 8.8%)',
  },
  'colors-slate-dark-200': {
    cssVariable: 'var(--core-colors-slate-dark-200)',
    value: 'hsl(195, 7.1%, 11%)',
  },
  'colors-slate-dark-300': {
    cssVariable: 'var(--core-colors-slate-dark-300)',
    value: 'hsl(192, 7.2%, 13.5%)',
  },
  'colors-slate-dark-400': {
    cssVariable: 'var(--core-colors-slate-dark-400)',
    value: 'hsl(204, 6.2%, 15.9%)',
  },
  'colors-slate-dark-500': {
    cssVariable: 'var(--core-colors-slate-dark-500)',
    value: 'hsl(200, 6.5%, 18%)',
  },
  'colors-slate-dark-600': {
    cssVariable: 'var(--core-colors-slate-dark-600)',
    value: 'hsl(205.70000000000005, 6.7%, 20.6%)',
  },
  'colors-slate-dark-700': {
    cssVariable: 'var(--core-colors-slate-dark-700)',
    value: 'hsl(202.5, 6.5%, 24.3%)',
  },
  'colors-slate-dark-800': {
    cssVariable: 'var(--core-colors-slate-dark-800)',
    value: 'hsl(206.70000000000005, 5.6%, 31.6%)',
  },
  'colors-slate-dark-900': {
    cssVariable: 'var(--core-colors-slate-dark-900)',
    value: 'hsl(205.70000000000005, 6.3%, 43.9%)',
  },
  'colors-slate-dark-1000': {
    cssVariable: 'var(--core-colors-slate-dark-1000)',
    value: 'hsl(207.70000000000005, 5.1%, 49.6%)',
  },
  'colors-slate-dark-1100': {
    cssVariable: 'var(--core-colors-slate-dark-1100)',
    value: 'hsl(207.29999999999995, 5.8%, 62.9%)',
  },
  'colors-slate-dark-1200': {
    cssVariable: 'var(--core-colors-slate-dark-1200)',
    value: 'hsl(210, 5.6%, 92.9%)',
  },
  'colors-slate-dark-alpha-100': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-100)',
    value: 'hsla(0, 0%, 0%, 0)',
  },
  'colors-slate-dark-alpha-200': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-200)',
    value: 'hsla(181.39999999999998, 100%, 91.8%, 0.03137254901960784)',
  },
  'colors-slate-dark-alpha-300': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-300)',
    value: 'hsla(181.60000000000002, 86.4%, 91.4%, 0.058823529411764705)',
  },
  'colors-slate-dark-alpha-400': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-400)',
    value: 'hsla(208.89999999999998, 87.1%, 93.9%, 0.0784313725490196)',
  },
  'colors-slate-dark-alpha-500': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-500)',
    value: 'hsla(200, 88.2%, 93.3%, 0.10980392156862745)',
  },
  'colors-slate-dark-alpha-600': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-600)',
    value: 'hsla(209, 93.9%, 93.5%, 0.1411764705882353)',
  },
  'colors-slate-dark-alpha-700': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-700)',
    value: 'hsla(203.20000000000005, 100%, 93.9%, 0.1803921568627451)',
  },
  'colors-slate-dark-alpha-800': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-800)',
    value: 'hsla(208.79999999999995, 92.6%, 94.7%, 0.25882352941176473)',
  },
  'colors-slate-dark-alpha-900': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-900)',
    value: 'hsla(208, 100%, 94.1%, 0.4117647058823529)',
  },
  'colors-slate-dark-alpha-1000': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-1000)',
    value: 'hsla(210, 100%, 95.3%, 0.47058823529411764)',
  },
  'colors-slate-dark-alpha-1100': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-1100)',
    value: 'hsla(210, 100%, 96.9%, 0.6196078431372549)',
  },
  'colors-slate-dark-alpha-1200': {
    cssVariable: 'var(--core-colors-slate-dark-alpha-1200)',
    value: 'hsla(210, 100%, 99.6%, 0.9294117647058824)',
  },
  'colors-slate-light-100': {
    cssVariable: 'var(--core-colors-slate-light-100)',
    value: 'hsl(210, 33.3%, 98.8%)',
  },
  'colors-slate-light-200': {
    cssVariable: 'var(--core-colors-slate-light-200)',
    value: 'hsl(210, 16.7%, 97.6%)',
  },
  'colors-slate-light-300': {
    cssVariable: 'var(--core-colors-slate-light-300)',
    value: 'hsl(210, 16.7%, 95.3%)',
  },
  'colors-slate-light-400': {
    cssVariable: 'var(--core-colors-slate-light-400)',
    value: 'hsl(210, 11.8%, 93.3%)',
  },
  'colors-slate-light-500': {
    cssVariable: 'var(--core-colors-slate-light-500)',
    value: 'hsl(216, 11.1%, 91.2%)',
  },
  'colors-slate-light-600': {
    cssVariable: 'var(--core-colors-slate-light-600)',
    value: 'hsl(205.70000000000005, 12.3%, 88.8%)',
  },
  'colors-slate-light-700': {
    cssVariable: 'var(--core-colors-slate-light-700)',
    value: 'hsl(210, 11.1%, 85.9%)',
  },
  'colors-slate-light-800': {
    cssVariable: 'var(--core-colors-slate-light-800)',
    value: 'hsl(205, 10.7%, 78%)',
  },
  'colors-slate-light-900': {
    cssVariable: 'var(--core-colors-slate-light-900)',
    value: 'hsl(205.70000000000005, 6.3%, 56.1%)',
  },
  'colors-slate-light-1000': {
    cssVariable: 'var(--core-colors-slate-light-1000)',
    value: 'hsl(205.70000000000005, 5.7%, 52.2%)',
  },
  'colors-slate-light-1100': {
    cssVariable: 'var(--core-colors-slate-light-1100)',
    value: 'hsl(205.70000000000005, 6.3%, 43.5%)',
  },
  'colors-slate-light-1200': {
    cssVariable: 'var(--core-colors-slate-light-1200)',
    value: 'hsl(201.79999999999995, 24.4%, 8.8%)',
  },
  'colors-slate-light-alpha-100': {
    cssVariable: 'var(--core-colors-slate-light-alpha-100)',
    value: 'hsla(209.79999999999995, 92.6%, 26.5%, 0.0196078431372549)',
  },
  'colors-slate-light-alpha-200': {
    cssVariable: 'var(--core-colors-slate-light-alpha-200)',
    value: 'hsla(210, 87.8%, 16.1%, 0.03137254901960784)',
  },
  'colors-slate-light-alpha-300': {
    cssVariable: 'var(--core-colors-slate-light-alpha-300)',
    value: 'hsla(209.60000000000002, 100%, 14.3%, 0.050980392156862744)',
  },
  'colors-slate-light-alpha-400': {
    cssVariable: 'var(--core-colors-slate-light-alpha-400)',
    value: 'hsla(210.60000000000002, 93%, 11.2%, 0.0784313725490196)',
  },
  'colors-slate-light-alpha-500': {
    cssVariable: 'var(--core-colors-slate-light-alpha-500)',
    value: 'hsla(215.29999999999995, 92.7%, 10.8%, 0.10196078431372549)',
  },
  'colors-slate-light-alpha-600': {
    cssVariable: 'var(--core-colors-slate-light-alpha-600)',
    value: 'hsla(205.70000000000005, 96.6%, 11.4%, 0.12941176470588237)',
  },
  'colors-slate-light-alpha-700': {
    cssVariable: 'var(--core-colors-slate-light-alpha-700)',
    value: 'hsla(209.39999999999998, 100%, 10%, 0.1607843137254902)',
  },
  'colors-slate-light-alpha-800': {
    cssVariable: 'var(--core-colors-slate-light-alpha-800)',
    value: 'hsla(204.5, 96.1%, 10%, 0.23921568627450981)',
  },
  'colors-slate-light-alpha-900': {
    cssVariable: 'var(--core-colors-slate-light-alpha-900)',
    value: 'hsla(206, 100%, 5.9%, 0.47058823529411764)',
  },
  'colors-slate-light-alpha-1000': {
    cssVariable: 'var(--core-colors-slate-light-alpha-1000)',
    value: 'hsla(204.39999999999998, 100%, 5.3%, 0.5098039215686274)',
  },
  'colors-slate-light-alpha-1100': {
    cssVariable: 'var(--core-colors-slate-light-alpha-1100)',
    value: 'hsla(205, 100%, 4.7%, 0.5882352941176471)',
  },
  'colors-slate-light-alpha-1200': {
    cssVariable: 'var(--core-colors-slate-light-alpha-1200)',
    value: 'hsla(200, 100%, 2.4%, 0.9294117647058824)',
  },
  'variables-colors-brand-primary': {
    cssVariable: 'var(--core-variables-colors-brand-primary)',
    value: 'hsl(153.10000000000002, 60.2%, 52.7%)',
  },
  'variables-colors-brand-accent': {
    cssVariable: 'var(--core-variables-colors-brand-accent)',
    value: 'hsl(152.89999999999998, 56.1%, 46.5%)',
  },
  'foreground-DEFAULT': {
    cssVariable: 'var(--foreground-default)',
    value: 'hsl(0, 0%, 98%)',
  },
  'foreground-light': {
    cssVariable: 'var(--foreground-light)',
    value: 'hsl(0, 0%, 70.6%)',
  },
  'foreground-lighter': {
    cssVariable: 'var(--foreground-lighter)',
    value: 'hsl(0, 0%, 53.7%)',
  },
  'foreground-muted': {
    cssVariable: 'var(--foreground-muted)',
    value: 'hsl(0, 0%, 30.2%)',
  },
  'foreground-contrast': {
    cssVariable: 'var(--foreground-contrast)',
    value: 'hsl(0, 0%, 8.6%)',
  },
  'background-200': {
    cssVariable: 'var(--background-200)',
    value: 'hsl(0, 0%, 9%)',
  },
  'background-DEFAULT': {
    cssVariable: 'var(--background-default)',
    value: 'hsl(0, 0%, 7.1%)',
  },
  'background-alternative-200': {
    cssVariable: 'var(--background-alternative-200)',
    value: 'hsl(0, 0%, 11%)',
  },
  'background-alternative-DEFAULT': {
    cssVariable: 'var(--background-alternative-default)',
    value: 'hsl(0, 0%, 5.9%)',
  },
  'background-selection': {
    cssVariable: 'var(--background-selection)',
    value: 'hsl(0, 0%, 19.2%)',
  },
  'background-control': {
    cssVariable: 'var(--background-control)',
    value: 'hsl(0, 0%, 14.1%)',
  },
  'background-surface-75': {
    cssVariable: 'var(--background-surface-75)',
    value: 'hsl(0, 0%, 9%)',
  },
  'background-surface-100': {
    cssVariable: 'var(--background-surface-100)',
    value: 'hsl(0, 0%, 12.2%)',
  },
  'background-surface-200': {
    cssVariable: 'var(--background-surface-200)',
    value: 'hsl(0, 0%, 12.9%)',
  },
  'background-surface-300': {
    cssVariable: 'var(--background-surface-300)',
    value: 'hsl(0, 0%, 16.1%)',
  },
  'background-surface-400': {
    cssVariable: 'var(--background-surface-400)',
    value: 'hsl(0, 0%, 16.1%)',
  },
  'background-overlay-DEFAULT': {
    cssVariable: 'var(--background-overlay-default)',
    value: 'hsl(0, 0%, 14.1%)',
  },
  'background-overlay-hover': {
    cssVariable: 'var(--background-overlay-hover)',
    value: 'hsl(0, 0%, 18%)',
  },
  'background-muted': {
    cssVariable: 'var(--background-muted)',
    value: 'hsl(0, 0%, 14.1%)',
  },
  'background-button-DEFAULT': {
    cssVariable: 'var(--background-button-default)',
    value: 'hsl(0, 0%, 18%)',
  },
  'background-dialog-DEFAULT': {
    cssVariable: 'var(--background-dialog-default)',
    value: 'hsl(0, 0%, 7.1%)',
  },
  'background-dash-sidebar': {
    cssVariable: 'var(--background-dash-sidebar)',
    value: 'hsl(0, 0%, 9%)',
  },
  'background-dash-canvas': {
    cssVariable: 'var(--background-dash-canvas)',
    value: 'hsl(0, 0%, 7.1%)',
  },
  'border-DEFAULT': {
    cssVariable: 'var(--border-default)',
    value: 'hsl(0, 0%, 18%)',
  },
  'border-muted': {
    cssVariable: 'var(--border-muted)',
    value: 'hsl(0, 0%, 14.1%)',
  },
  'border-secondary': {
    cssVariable: 'var(--border-secondary)',
    value: 'hsl(0, 0%, 14.1%)',
  },
  'border-overlay': {
    cssVariable: 'var(--border-overlay)',
    value: 'hsl(0, 0%, 20%)',
  },
  'border-control': {
    cssVariable: 'var(--border-control)',
    value: 'hsl(0, 0%, 22.4%)',
  },
  'border-alternative': {
    cssVariable: 'var(--border-alternative)',
    value: 'hsl(0, 0%, 26.7%)',
  },
  'border-strong': {
    cssVariable: 'var(--border-strong)',
    value: 'hsl(0, 0%, 21.2%)',
  },
  'border-stronger': {
    cssVariable: 'var(--border-stronger)',
    value: 'hsl(0, 0%, 27.1%)',
  },
  'border-button-DEFAULT': {
    cssVariable: 'var(--border-button-default)',
    value: 'hsl(0, 0%, 24.3%)',
  },
  'border-button-hover': {
    cssVariable: 'var(--border-button-hover)',
    value: 'hsl(0, 0%, 31.4%)',
  },
  'destructive-200': {
    cssVariable: 'var(--destructive-200)',
    value: 'hsl(10.899999999999977, 23.4%, 9.2%)',
  },
  'destructive-300': {
    cssVariable: 'var(--destructive-300)',
    value: 'hsl(7.5, 51.3%, 15.3%)',
  },
  'destructive-400': {
    cssVariable: 'var(--destructive-400)',
    value: 'hsl(6.699999999999989, 60%, 20.6%)',
  },
  'destructive-500': {
    cssVariable: 'var(--destructive-500)',
    value: 'hsl(7.899999999999977, 71.6%, 29%)',
  },
  'destructive-600': {
    cssVariable: 'var(--destructive-600)',
    value: 'hsl(9.699999999999989, 85.2%, 62.9%)',
  },
  'destructive-DEFAULT': {
    cssVariable: 'var(--destructive-default)',
    value: 'hsl(10.199999999999989, 77.9%, 53.9%)',
  },
  'warning-200': {
    cssVariable: 'var(--warning-200)',
    value: 'hsl(36.60000000000002, 100%, 8%)',
  },
  'warning-300': {
    cssVariable: 'var(--warning-300)',
    value: 'hsl(32.30000000000001, 100%, 10.2%)',
  },
  'warning-400': {
    cssVariable: 'var(--warning-400)',
    value: 'hsl(33.19999999999999, 100%, 14.5%)',
  },
  'warning-500': {
    cssVariable: 'var(--warning-500)',
    value: 'hsl(34.80000000000001, 90.9%, 21.6%)',
  },
  'warning-600': {
    cssVariable: 'var(--warning-600)',
    value: 'hsl(38.89999999999998, 100%, 42.9%)',
  },
  'warning-DEFAULT': {
    cssVariable: 'var(--warning-default)',
    value: 'hsl(38.89999999999998, 100%, 42.9%)',
  },
  'brand-200': {
    cssVariable: 'var(--brand-200)',
    value: 'hsl(162, 100%, 2%)',
  },
  'brand-300': {
    cssVariable: 'var(--brand-300)',
    value: 'hsl(155.10000000000002, 100%, 8%)',
  },
  'brand-400': {
    cssVariable: 'var(--brand-400)',
    value: 'hsl(155.5, 100%, 9.6%)',
  },
  'brand-500': {
    cssVariable: 'var(--brand-500)',
    value: 'hsl(154.89999999999998, 100%, 19.2%)',
  },
  'brand-600': {
    cssVariable: 'var(--brand-600)',
    value: 'hsl(154.89999999999998, 59.5%, 70%)',
  },
  'brand-DEFAULT': {
    cssVariable: 'var(--brand-default)',
    value: 'hsl(153.10000000000002, 60.2%, 52.7%)',
  },
  'brand-button': {
    cssVariable: 'var(--brand-button)',
    value: 'hsl(154.89999999999998, 100%, 19.2%)',
  },
  'brand-link': {
    cssVariable: 'var(--brand-link)',
    value: 'hsl(155, 100%, 38.6%)',
  },
  '_secondary-200': {
    cssVariable: 'var(--secondary-200)',
    value: 'hsl(248, 53.6%, 11%)',
  },
  '_secondary-400': {
    cssVariable: 'var(--secondary-400)',
    value: 'hsl(248.29999999999995, 54.5%, 25.9%)',
  },
  '_secondary-DEFAULT': {
    cssVariable: 'var(--secondary-default)',
    value: 'hsl(247.79999999999995, 100%, 70%)',
  },
  'code_block-1': {
    cssVariable: 'var(--code-block-1)',
    value: 'hsl(170.79999999999995, 43.1%, 61.4%)',
  },
  'code_block-2': {
    cssVariable: 'var(--code-block-2)',
    value: 'hsl(33.19999999999999, 90.3%, 75.7%)',
  },
  'code_block-3': {
    cssVariable: 'var(--code-block-3)',
    value: 'hsl(83.80000000000001, 61.7%, 63.1%)',
  },
  'code_block-4': {
    cssVariable: 'var(--code-block-4)',
    value: 'hsl(276.1, 67.7%, 74.5%)',
  },
  'code_block-5': {
    cssVariable: 'var(--code-block-5)',
    value: 'hsl(13.800000000000011, 89.7%, 69.6%)',
  },
}
