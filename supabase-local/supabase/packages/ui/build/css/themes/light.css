[data-theme='light'],
.light {
  --helpers-os-appearance: Light;
  --code-block-5: 14deg 80.4% 58%;
  --code-block-4: 276.3deg 60% 52.9%;
  --code-block-3: 83.8deg 61.6% 48%;
  --code-block-2: 33.1deg 80% 52.9%;
  --code-block-1: 170.6deg 43.2% 51%;
  --secondary-default: 247.8deg 100% 70%;
  --secondary-400: 248.3deg 54.5% 25.9%;
  --secondary-200: 248deg 53.6% 11%;
  --brand-link: 153.4deg 100% 36.7%;
  --brand-button: 151.8deg 47.1% 40.8%;
  --brand-default: 152.9deg 60% 52.9%;
  --brand-600: 156.5deg 86.5% 26.1%;
  --brand-500: 155.3deg 78.4% 40%;
  --brand-400: 151.3deg 66.9% 66.9%;
  --brand-300: 147.5deg 72% 80.4%;
  --brand-200: 147.6deg 72.5% 90%;
  --warning-default: 38.9deg 100% 57.1%;
  --warning-600: 30.3deg 80.3% 47.8%;
  --warning-500: 36.3deg 85.7% 67.1%;
  --warning-400: 41.9deg 100% 81.8%;
  --warning-300: 44.3deg 100% 91.8%;
  --warning-200: 40deg 81.8% 97.8%;
  --destructive-default: 10.2deg 77.9% 53.9%;
  --destructive-600: 9.9deg 82% 43.5%;
  --destructive-500: 10.4deg 77.1% 79.4%;
  --destructive-400: 7.1deg 91.3% 91%;
  --destructive-300: 7.1deg 100% 96.7%;
  --destructive-200: 0deg 100% 99.4%;
  --border-stronger: 0deg 0% 56.1%;
  --border-strong: 0deg 0% 83.1%;
  --border-default: 0deg 0% 87.5%;
  --background-dialog-default: 0deg 0% 100%;
  --background-muted: 0deg 0% 96.9%;
  --background-surface-400: 0deg 0% 89.8%;
  --background-surface-300: 0deg 0% 92.9%;
  --background-surface-200: 0deg 0% 95.3%;
  --background-surface-100: 0deg 0% 98.8%;
  --background-surface-75: 0deg 0% 100%;
  --background-alternative-default: 0deg 0% 99.2%;
  --background-alternative-200: 0deg 0% 100%;
  --foreground-contrast: 0deg 0% 98.4%;
  --foreground-muted: 0deg 0% 69.8%;
  --foreground-lighter: 0deg 0% 43.9%;
  --foreground-light: 0deg 0% 32.2%;
  --border-button-hover: var(--colors-gray-light-700);
  --border-button-default: var(--colors-gray-light-600);
  --border-alternative: var(--colors-gray-light-500);
  --border-control: var(--colors-gray-light-800);
  --border-overlay: var(--colors-gray-light-500);
  --border-secondary: var(--colors-gray-light-400);
  --border-muted: var(--colors-gray-light-400);
  --background-dash-canvas: var(--colors-gray-light-200);
  --background-dash-sidebar: var(--colors-gray-light-100);
  --background-button-default: var(--colors-gray-light-100);
  --background-overlay-hover: var(--colors-gray-light-300);
  --background-overlay-default: var(--colors-gray-light-100);
  --background-control: var(--colors-gray-light-300);
  --background-selection: var(--colors-gray-light-400);
  --background-default: var(--colors-gray-light-100);
  --background-200: var(--colors-gray-light-200);
  --foreground-default: var(--colors-gray-light-1200);
}
