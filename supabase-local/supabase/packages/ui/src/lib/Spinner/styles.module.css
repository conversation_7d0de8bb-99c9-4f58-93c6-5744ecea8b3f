html {
  background: #009688;
}
html,
body {
  height: 100%;
}
body {
  display: flex;
  align-items: center;
  justify-content: center;
}
.spin-loader {
  border-radius: 80%;
  display: block;
  height: 50px;
  width: 50px;
  position: relative;
  animation: spin 0.675s linear 0s infinite normal;
  background: #26a69a;
}
.spin-loader:before,
.spin-loader:after {
  content: '';
  display: block;
  position: absolute;
}
.spin-loader:before {
  border-radius: 0 90px 90px 0;
  height: 50px;
  width: 50%;
  top: 0;
  right: 0;
  z-index: 1;
  background: #005041;
  background-image: linear-gradient(#26a69a, #005041);
}
.spin-loader:after {
  border-radius: 80%;
  height: 40px;
  width: 40px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  background: #009688;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
