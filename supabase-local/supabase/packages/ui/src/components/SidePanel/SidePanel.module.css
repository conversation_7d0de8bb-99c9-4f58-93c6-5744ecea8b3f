/* 
 Side Panel animations imported first as so placement styles are not affected.
*/

/* overlay animation */
.sbui-sidepanel-overlay--enter {
  @apply ease-out duration-200;
}
.sbui-sidepanel-overlay--enterFrom {
  @apply opacity-0;
}
.sbui-sidepanel-overlay--enterTo {
  @apply opacity-100;
}
.sbui-sidepanel-overlay--leave {
  @apply ease-in duration-200;
}
.sbui-sidepanel-overlay--leaveFrom {
  @apply opacity-100;
}
.sbui-sidepanel-overlay--leaveTo {
  @apply opacity-0;
}

/* panel animation */
.sbui-sidepanel--enter {
  @apply transform transition ease-in-out duration-200 lg:duration-300;
}
.sbui-sidepanel--enterFrom {
  @apply translate-x-full;
}
.sbui-sidepanel--enterFrom--left {
  @apply -translate-x-full;
}
.sbui-sidepanel--enterTo {
  @apply translate-x-0;
}
.sbui-sidepanel--leave {
  @apply transform transition ease-in-out duration-200 lg:duration-300;
}
.sbui-sidepanel--leaveFrom {
  @apply translate-x-0;
}
.sbui-sidepanel--leaveTo {
  @apply translate-x-full;
}
.sbui-sidepanel--leaveTo--left {
  @apply -translate-x-full;
}

/* .sbui-sidepanel-overlay-container {
  @apply fixed inset-0 transition-opacity;
} */

/* .sbui-sidepanel-overlay {
  @apply absolute inset-0 bg-surface-100 opacity-75;
} */

.sbui-sidepanel--left {
  @apply left-0 pr-10;
}

.sbui-sidepanel--right {
  @apply right-0 pl-10;
}

.sbui-sidepanel--medium {
  @apply w-screen max-w-md h-full;
}

.sbui-sidepanel--wide {
  @apply w-screen max-w-2xl h-full;
}

/* .sbui-sidepanel-fixed {
  @apply fixed inset-0 overflow-hidden;
} */

/* .sbui-sidepanel-absolute {
  @apply absolute inset-0 overflow-hidden;
} */

/* .sbui-sidepanel-container {
  @apply absolute inset-y-0 max-w-full flex h-screen;
} */

/* .sbui-sidepanel {
  @apply h-full flex flex-col bg-white dark:bg-dark-700 shadow-xl;
} */

.sbui-sidepanel-header {
  @apply space-y-1 py-6 px-4 bg-gray-50 dark:bg-dark-600 sm:px-6;
}

.sbui-sidepanel-close-container {
  @apply h-7 flex items-center;
}

.sbui-sidepanel-close-container button,
.dark .sbui-sidepanel-close-container button {
  @apply text-gray-300;
  @apply hover:text-gray-400;

  /* @apply dark:text-dark-400; */
  /* @apply dark:hover:text-dark-300; */

  @apply hover:bg-transparent;
}

/* .sbui-sidepanel-content {
  @apply relative flex-1 px-4 sm:px-6;
} */

.sbui-sidepanel-footer-container {
  @apply p-4 bg-gray-50 dark:bg-dark-600;
}

.sbui-sidepanel-footer {
  @apply flex flex-shrink-0;
}

.sbui-sidepanel__trigger {
  @apply border-none bg-transparent p-0 focus:ring-0;
}
