/* .sbui-toggle {
  @apply p-0 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer;
  @apply transition-colors ease-in-out duration-200;
  @apply focus:outline-none bg-200 dark:bg-gray-400;
}

.sbui-toggle--active {
  @apply bg-brand-600;
  @apply dark:bg-brand-600;
}

.sbui-toggle__handle {
  @apply inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 translate-x-0;
}

.sbui-toggle__handle--active {
  @apply translate-x-5;
}

.sbui-toggle:disabled {
  @apply opacity-75 cursor-not-allowed;
  pointer-events: none;
} */

/*
  Toggle sizes
*/

/* tiny */

/* .sbui-toggle--tiny {
  @apply h-4 w-7;
}
.sbui-toggle--tiny .sbui-toggle__handle {
  @apply h-3 w-3;
}
.sbui-toggle--tiny .sbui-toggle__handle--active {
  @apply translate-x-3;
} */

/* small and medium */

/* .sbui-toggle--small,
.sbui-toggle--medium {
  @apply h-6 w-11;
}
.sbui-toggle--small .sbui-toggle__handle,
.sbui-toggle--medium .sbui-toggle__handle {
  @apply h-5 w-5;
}
.sbui-toggle--small .sbui-toggle__handle--active,
.sbui-toggle--medium .sbui-toggle__handle--active {
  @apply translate-x-5;
} */

/* large and xlarge */

/* .sbui-toggle--large,
.sbui-toggle--xlarge {
  @apply h-7 w-12;
}
.sbui-toggle--large .sbui-toggle__handle,
.sbui-toggle--xlarge .sbui-toggle__handle {
  @apply h-6 w-6;
}
.sbui-toggle--large .sbui-toggle__handle--active,
.sbui-toggle--xlarge .sbui-toggle__handle--active {
  @apply translate-x-5;
} */
