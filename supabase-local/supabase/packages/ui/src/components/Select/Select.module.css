.sbui-select-container {
  @apply relative;
}

.sbui-select {
  @apply block w-full bg-white pl-3 pr-10 py-2 text-sm rounded-md shadow-sm transition-all;

  @apply text-input-value-light border border-solid border-input-border-light;
  @apply focus:ring-input-border-focus-light focus:border-input-border-focus-light focus:outline-none;

  @apply dark:bg-transparent dark:text-input-value-dark dark:border-input-border-dark;
  @apply dark:focus:border-input-border-focus-dark dark:focus:ring-input-border-focus-dark;
  -webkit-appearance: none;
  -moz-appearance: none;
  text-overflow: '';
  /* box-shadow: 0 0 0 2px rgba(255, 255, 255, 0); */
  transition: box-shadow 0.3s ease-in-out;

  /* // temporary fix
  //
  // temporary fix for supabase apps
  // tailwind @base styles adds a dropdown chevron
  // using background image as default
  // */
  background-image: none;
}

.sbui-select option {
  @apply bg-white text-gray-800;
}

.sbui-select:focus {
  box-shadow: 0 0 0 2px rgba(62, 207, 142, 0.1);
}

.sbui-select--error {
  @apply border-red-500;
}

.sbui-select--borderless {
  @apply border-transparent shadow-none;
}

/*
  Select sizes
*/

.sbui-select--tiny {
  @apply px-2.5 py-1.5 text-xs;
}
.sbui-select--small {
  @apply px-3 py-2 text-sm leading-4;
}
.sbui-select--medium {
  @apply px-4 py-2 text-sm;
}
.sbui-select--large {
  @apply px-4 py-2 text-base;
}
.sbui-select--xlarge {
  @apply px-6 py-3 text-base;
}

.sbui-select-actions-container {
  @apply absolute inset-y-0 right-0 pl-3 pr-1 mr-5 flex items-center;
}

/*
  Select icon
*/

.sbui-select--with-icon {
  @apply pl-10;
}

/*
  Select Chevron
*/

.sbui-select-chevron-container {
  @apply absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none;
}

.sbui-select-chevron {
  @apply h-5 w-5 text-gray-400;
}
