.sbui-inputnumber-container {
  @apply relative;
}

.sbui-inputnumber {
  @apply block box-border pl-3 pr-3 py-2 w-full rounded-md shadow-sm text-sm border border-solid transition-all;
  @apply bg-white text-input-value-light border-input-border-light;
  @apply focus:ring-input-border-focus-light focus:border-input-border-focus-light focus:outline-none;

  @apply dark:bg-transparent dark:text-input-value-dark dark:border-input-border-dark;
  @apply dark:focus:border-input-border-focus-dark dark:focus:ring-input-border-focus-dark;

  /* box-shadow: 0 0 0 2px rgba(255, 255, 255, 0); */
  font-family: inherit;
  transition: box-shadow 0.3s ease-in-out;

  -moz-appearance: textfield;
}

.sbui-inputnumber::-webkit-inner-spin-button,
.sbui-inputnumber::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.sbui-inputnumber:focus {
  box-shadow: 0 0 0 2px rgba(62, 207, 142, 0.1);
  outline: 0;
}

.sbui-inputnumber--error {
  @apply border-red-500;
}

.sbui-inputnumber--borderless {
  @apply border-transparent shadow-none;
}

.sbui-inputnumber-actions-container {
  @apply absolute inset-y-0 right-0 pl-3 pr-1 flex items-center;
}

.sbui-inputnumber-nav {
  @apply hidden;
}

.sbui-inputnumber:focus + .sbui-inputnumber-nav {
  @apply block;
}

.sbui-inputnumber-button {
  @apply block box-border pl-3 pr-3 py-2 w-full rounded-md text-sm border border-solid;
  @apply bg-transparent border-transparent;
  @apply dark:text-white;

  position: relative;
  cursor: pointer;
  width: 21px;
  text-align: center;
  color: #333;
  font-size: 13px;
  line-height: 1.5;
  padding: 0;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.sbui-inputnumber-button:active {
  background: #eaeaea;
  background-clip: padding-box;
}

.sbui-inputnumber-button-up {
  position: absolute;
  height: 50%;
  top: 0;
  border-radius: 0 0.375rem 0 0;
  line-height: 1.6;
}

.sbui-inputnumber-button-down {
  position: absolute;
  bottom: 0;
  height: 50%;
  border-radius: 0 0 0.375rem 0;
}

/*
    Input sizes
  */

.sbui-inputnumber--tiny {
  @apply px-2.5 py-1.5 text-xs;
}
.sbui-inputnumber--small {
  @apply px-3 py-2 text-sm leading-4;
}
.sbui-inputnumber--medium {
  @apply px-4 py-2 text-sm;
}
.sbui-inputnumber--large {
  @apply px-4 py-2 text-base;
}
.sbui-inputnumber--xlarge {
  @apply px-6 py-3 text-base;
}

.sbui-inputnumber-nav--tiny {
  height: 30px;
}
.sbui-inputnumber-nav--small {
  height: 34px;
}
.sbui-inputnumber-nav--medium {
  height: 38px;
}
.sbui-inputnumber-nav--large {
  height: 42px;
}
.sbui-inputnumber-nav--xlarge {
  height: 50px;
}
/*
    Input icon
  */

.sbui-inputnumber--with-icon {
  @apply pl-10;
}
