/* 
 Modal animations imported first as so placement styles are not affected.
*/

/* overlay animation */
.sbui-modal-overlay--enter {
  @apply ease-out duration-200;
}
.sbui-modal-overlay--enterFrom {
  @apply opacity-0;
}
.sbui-modal-overlay--enterTo {
  @apply opacity-100;
}
.sbui-modal-overlay--leave {
  @apply ease-in duration-150;
}
.sbui-modal-overlay--leaveFrom {
  @apply opacity-100;
}
.sbui-modal-overlay--leaveTo {
  @apply opacity-0;
}

/* modal animation */
.sbui-modal--enter {
  @apply ease-out duration-150 delay-150;
}
.sbui-modal--enterFrom {
  @apply opacity-0 translate-y-4 sm:translate-y-0 transform sm:scale-95;
}
.sbui-modal--enterTo {
  @apply opacity-100 translate-y-0 transform sm:scale-100;
}
.sbui-modal--leave {
  @apply ease-in duration-150;
}
.sbui-modal--leaveFrom {
  @apply opacity-100 translate-y-0 transform sm:scale-100;
}
.sbui-modal--leaveTo {
  @apply opacity-0 translate-y-4 sm:translate-y-0 transform sm:scale-95;
}

.sbui-modal-container {
  @apply fixed z-50 inset-0;
}

.sbui-modal-flex-container {
  @apply flex items-end justify-center min-h-screen pt-4 px-6 pb-20 text-center sm:block sm:p-0;
}

.sbui-modal-overlay-container {
  @apply fixed inset-0 transition-opacity;
}

.sbui-modal-overlay {
  @apply absolute inset-0 bg-gray-900 opacity-75;
}

.sbui-modal-div-trick {
  @apply hidden sm:inline-block sm:align-middle sm:h-screen;
}

/* .sbui-modal {
  @apply inline-block bg-white dark:bg-dark-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all align-middle;
  @apply border-gray-400 border border-solid;
  @apply dark:border-gray-600 border border-solid;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
} */

.sbui-modal--tiny {
  @apply sm:align-middle sm:w-full sm:max-w-xs;
}

.sbui-modal--small {
  @apply sm:align-middle sm:w-full sm:max-w-sm;
}

.sbui-modal--medium {
  @apply sm:align-middle sm:w-full sm:max-w-lg;
}

.sbui-modal--large {
  @apply sm:align-middle sm:w-full max-w-xl;
}

.sbui-modal-content {
  @apply px-6 pt-5 pb-5 sm:p-6;
}

.sbui-modal-footer {
  @apply sm:flex sm:flex-row justify-end;
}

.sbui-modal-footer--with-bg {
  @apply bg-gray-50 dark:bg-dark-600 px-6 py-3 sm:px-6 sm:flex sm:flex-row justify-end;
}

.sbui-modal-icon-container {
  @apply mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10;
}

.sbui-modal-icon-container--danger {
  @apply text-red-600 bg-red-600 bg-opacity-20;
}

.sbui-modal-icon-container--warning {
  @apply text-yellow-500 bg-yellow-500 bg-opacity-20;
}

.sbui-modal-icon-container--success {
  @apply text-brand-300 bg-brand-300 bg-opacity-20;
}

.sbui-modal-close-container {
  @apply absolute right-1 top-1;
}

.sbui-modal-close-container button,
.dark .sbui-modal-close-container button {
  @apply text-gray-300;
  @apply hover:text-gray-400;

  /* @apply dark:text-dark-400; */
  @apply hover:text-dark-300;

  @apply hover:bg-transparent;
}

.sbui-sidepanel__trigger {
  @apply border-none bg-transparent p-0 focus:ring-0;
}
