/* .sbui-listbox-container {
  @apply relative;
} */

/* .sbui-listbox {
  @apply block w-full bg-white pl-3 pr-10 py-2 text-sm rounded-md transition-all;
  @apply text-gray-700 border border-solid border-gray-300;
  @apply cursor-default focus:outline-none focus:ring-1 focus:ring-brand-600 focus:border-brand-600;
  @apply dark:bg-transparent dark:text-white dark:border-gray-500;
  @apply dark:focus:border-brand-300;
  -webkit-appearance: none;
  -moz-appearance: none;
  text-indent: 1px;
  text-overflow: '';
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0);
  transition: box-shadow 0.3s ease-in-out; */

/* // temporary fix
  //
  // temporary fix for supabase apps
  // tailwind @base styles adds a dropdown chevron
  // using background image as default
  // */
/* background-image: none;
} */

.sbui-listbox:focus {
  box-shadow: 0 0 0 2px rgba(62, 207, 142, 0.1);
}

.sbui-listbox--error {
  @apply border-red-500;
}

.sbui-listbox--borderless {
  @apply border-transparent shadow-none;
}

/*
  Select sizes
*/

/* .sbui-listbox--tiny {
  @apply px-2.5 py-1.5 text-xs;
}
.sbui-listbox--small {
  @apply px-3 py-2 text-sm leading-4;
}
.sbui-listbox--medium {
  @apply px-4 py-2 text-sm;
}
.sbui-listbox--large {
  @apply px-4 py-2 text-base;
}
.sbui-listbox--xlarge {
  @apply px-6 py-3 text-base;
} */

/* .sbui-listbox-actions-container {
  @apply absolute inset-y-0 right-0 pl-3 pr-1 mr-5 flex items-center;
} */

/*
  Select icon
*/

/* .sbui-listbox--with-icon {
  @apply pl-10;
} */

/*
  Add on before
*/

/* .sbui-listbox-addonbefore {
  @apply w-full flex flex-row items-center space-x-3;
} */

.sbui-listbox-label {
  @apply truncate;
}

/*
  Select Chevron
*/

.sbui-listbox-chevron-container {
  @apply absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none;
}

.sbui-listbox-chevron {
  @apply h-5 w-5 text-gray-400;
}

/*
  Option
*/

/* .sbui-listbox-option-container {
  @apply list-none p-0 absolute mt-1 w-full bg-white dark:bg-gray-800 shadow-lg border border-solid border-control max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm z-10;
} */

/* .sbui-listbox-option {
  @apply cursor-pointer select-none relative py-2 pl-3 pr-9 text-gray-900 dark:text-white;
} */

/* .sbui-listbox-option--active {
  @apply text-brand-600 dark:text-brand-600 bg-brand-600 bg-opacity-20;
}

.sbui-listbox-option--disabled {
  @apply cursor-not-allowed opacity-50;
} */

/* .sbui-listbox-option__inner {
  @apply flex items-center space-x-3;
} */

/* .sbui-listbox-option__check {
  @apply absolute inset-y-0 right-0 flex items-center pr-3;
}

.sbui-listbox-option__check--active {
  @apply text-brand-600;
} */

.sbui-listbox-option__check__icon {
  @apply h-5 w-5;
}

/*
  Transition
*/

.sbui-listbox-transition--leave {
  @apply transition ease-in duration-100;
}

.sbui-listbox-transition--leave-from {
  @apply opacity-100;
}

.sbui-listbox-transition--leave-to {
  @apply opacity-0;
}
