// @ts-ignore
import IconBase from '../../IconBase'

const src = (
  <path
    d="M12.3806 0.619116V6.85795M9.26117 3.73884L15.5 3.73884M7.20065 12.0306C7.20065 13.8809 5.70066 15.3809 3.85032 15.3809C1.99999 15.3809 0.5 13.8809 0.5 12.0306C0.5 10.1802 1.99999 8.68023 3.85032 8.68023C5.70066 8.68023 7.20065 10.1802 7.20065 12.0306ZM15.5 14.032V10.2887C15.5 9.59957 14.9414 9.04093 14.2522 9.04093H10.5089C9.81981 9.04093 9.26117 9.59957 9.26117 10.2887V14.032C9.26117 14.7211 9.81981 15.2798 10.5089 15.2798H14.2522C14.9414 15.2798 15.5 14.7211 15.5 14.032ZM1.97929 6.85795H5.72258C6.41171 6.85795 6.97035 6.2993 6.97035 5.61018V1.86688C6.97035 1.17776 6.41171 0.619116 5.72258 0.619116H1.97929C1.29016 0.619116 0.731519 1.17776 0.731519 1.86688V5.61018C0.731519 6.2993 1.29016 6.85795 1.97929 6.85795Z"
    strokeMiterlimit="10"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

function IconIntegrations(props: any) {
  return <IconBase src={src} {...props} />
}

export default IconIntegrations
