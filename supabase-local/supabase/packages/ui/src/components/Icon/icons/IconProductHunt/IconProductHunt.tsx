// @ts-ignore
import IconBase from '../../IconBase'

// Import icon path from svg with viewbox of 16x16px
const src = (
  <path
    fill="currentColor"
    d="M7.29978 8.55127H9.56658C9.88484 8.55127 10.1901 8.42484 10.4151 8.1998C10.6401 7.97475 10.7666 7.66953 10.7666 7.35127C10.7666 7.03301 10.6401 6.72779 10.4151 6.50274C10.1901 6.2777 9.88484 6.15127 9.56658 6.15127H7.29978V8.55127Z M8.5 16.5513C12.9184 16.5513 16.5 12.9697 16.5 8.55127C16.5 4.13287 12.9184 0.55127 8.5 0.55127C4.0816 0.55127 0.5 4.13287 0.5 8.55127C0.5 12.9697 4.0816 16.5513 8.5 16.5513ZM5.69978 4.55127H9.56658C10.3092 4.55127 11.0214 4.84627 11.5465 5.37137C12.0716 5.89647 12.3666 6.60866 12.3666 7.35127C12.3666 8.09388 12.0716 8.80607 11.5465 9.33117C11.0214 9.85627 10.3092 10.1513 9.56658 10.1513H7.29978V12.5513H5.69978V4.55127Z"
  />
)

function IconProductHunt(props: any) {
  return <IconBase src={src} stroke="none" {...props} />
}

export default IconProductHunt
