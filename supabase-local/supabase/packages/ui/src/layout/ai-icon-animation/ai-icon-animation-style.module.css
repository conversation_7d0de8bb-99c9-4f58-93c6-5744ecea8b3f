/*
* STEP ONE
*/

@keyframes step1_square1 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  100% {
    transform: rotate(0deg);
    top: 0px;
    left: 0px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes step1_square2 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  100% {
    transform: rotate(0deg);
    top: 0px;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes step1_square3 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  100% {
    transform: rotate(0deg);
    top: 8px;
    left: 0px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes step1_square4 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }

  100% {
    transform: rotate(0deg);
    top: 8px;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

/*
* STEP TWO
*/

@keyframes step2_square1 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 0px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 1px;
    left: 1px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
}

@keyframes step2_square2 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 1px;
    left: 15px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
}

@keyframes step2_square3 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 0px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 15px;
    left: 1px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
}

@keyframes step2_square4 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 15px;
    left: 15px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
}

/*
* STEP THREE
*/

@keyframes step3_square1 {
  0% {
    transform: rotate(45deg);
    top: 0px;
    left: 0px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(0deg);
    top: 0px;
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
}

@keyframes step3_square2 {
  0% {
    transform: rotate(45deg);
    top: 0px;
    left: 16px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(0deg);
    top: 0px;
    left: 18px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
}

@keyframes step3_square3 {
  0% {
    transform: rotate(45deg);
    top: 16px;
    left: 0px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(0deg);
    top: 18px;
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
}

@keyframes step3_square4 {
  0% {
    transform: rotate(45deg);
    top: 16px;
    left: 16px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(0deg);
    top: 18px;
    left: 18px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
}

/*
* STEP FOUR
*/

@keyframes step4_square1 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(0deg);
    top: 0px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes step4_square2 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 18px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(0deg);
    top: 8px;
    left: 16px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes step4_square3 {
  0% {
    transform: rotate(0deg);
    top: 18px;
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(0deg);
    top: 8px;
    left: 0px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes step4_square4 {
  0% {
    transform: rotate(0deg);
    top: 18px;
    left: 18px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(0deg);
    top: 16px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

/* STEP FIVE */

@keyframes step5_square1 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes step5_square2 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 16px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes step5_square3 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 0px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes step5_square4 {
  0% {
    transform: rotate(0deg);
    top: 16px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes spinAiIconContainer {
  0% {
    transform: rotate(0deg);
  }
  40% {
    transform: rotate(-45deg);
  }
  80% {
    transform: rotate(90deg);
  }
  /* 80% {
    transform: rotate(90deg);
  } */
  100% {
    transform: rotate(90deg);
  }
}

.spin-ai-icon-container {
  width: 100%;
  height: 100%;
  /* 
  
    please note
    
    the duration is the setInterval in the component useEffect multiplied by 5 
    this is due having 5 steps in animation sequence

    So if the setInterval is 800ms then the duration below should be is 4000ms

  */
  animation: spinAiIconContainer 2500ms infinite;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
}

.ai-icon__container {
  width: 24px;
  height: 24px;
  /* background: red; */
}

.ai-icon__grid {
  position: relative;
  width: 24px;
  height: 24px;
}

.ai-icon__grid__square {
  position: absolute;
  transition: all 0.2s;
  border: 1px solid hsl(var(--brand-default));
}

.ai-icon__grid__square--static:nth-child(1) {
  transform: rotate(45deg);
  top: 4px;
  left: 4px;
  width: 16px;
  height: 16px;
  border-radius: 0%;
}
.ai-icon__grid__square--static:nth-child(2) {
  transform: rotate(45deg);
  top: 8px;
  left: 8px;
  width: 8px;
  height: 8px;
  border-radius: 100%;
}
.ai-icon__grid__square--static:nth-child(3) {
  transform: rotate(45deg);
  top: 8px;
  left: 8px;
  width: 8px;
  height: 8px;
  border-radius: 100%;
}
.ai-icon__grid__square--static:nth-child(4) {
  transform: rotate(45deg);
  top: 8px;
  left: 8px;
  width: 8px;
  height: 8px;
  border-radius: 100%;
}

.ai-icon__container--allow-hover-effect:hover .ai-icon__grid__square--static:nth-child(1),
[data-selected='true'] .ai-icon__grid__square--static:nth-child(1) {
  transform: rotate(45deg);
  top: 8px;
  left: 8px;
  width: 8px;
  height: 8px;
  border-radius: 0%;
}

.ai-icon__container--allow-hover-effect:hover .ai-icon__grid__square--static:nth-child(2),
[data-selected='true'] .ai-icon__grid__square--static:nth-child(2) {
  transform: rotate(45deg);
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  border-radius: 100%;
}

.ai-icon__container--allow-hover-effect:hover .ai-icon__grid__square--static:nth-child(3),
[data-selected='true'] .ai-icon__grid__square--static:nth-child(3) {
  transform: rotate(45deg);
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  border-radius: 100%;
}

.ai-icon__container--allow-hover-effect:hover .ai-icon__grid__square--static:nth-child(4),
[data-selected='true'] .ai-icon__grid__square--static:nth-child(4) {
  transform: rotate(45deg);
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  border-radius: 100%;
}

.ai-icon__grid__square--step-1:nth-child(1) {
  animation: step1_square1 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 0; */
}
.ai-icon__grid__square--step-1:nth-child(2) {
  animation: step1_square2 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 200ms; */
}
.ai-icon__grid__square--step-1:nth-child(3) {
  animation: step1_square3 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 400ms; */
}
.ai-icon__grid__square--step-1:nth-child(4) {
  animation: step1_square4 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 600ms; */
}

.ai-icon__grid__square--step-2:nth-child(1) {
  animation-delay: 200;
  animation: step2_square1 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-2:nth-child(2) {
  animation-delay: 400ms;
  animation: step2_square2 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-2:nth-child(3) {
  animation-delay: 600ms;
  animation: step2_square3 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-2:nth-child(4) {
  animation-delay: 800ms;
  animation: step2_square4 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}

.ai-icon__grid__square--step-3:nth-child(1) {
  animation: step3_square1 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-3:nth-child(2) {
  animation: step3_square2 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-3:nth-child(3) {
  animation: step3_square3 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-3:nth-child(4) {
  animation: step3_square4 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}

.ai-icon__grid__square--step-4:nth-child(1) {
  animation: step4_square1 0.4s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.ai-icon__grid__square--step-4:nth-child(2) {
  animation: step4_square2 0.4s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.ai-icon__grid__square--step-4:nth-child(3) {
  animation: step4_square3 0.4s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.ai-icon__grid__square--step-4:nth-child(4) {
  animation: step4_square4 0.4s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.ai-icon__grid__square--step-5:nth-child(1) {
  animation: step5_square1 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-5:nth-child(2) {
  animation: step5_square2 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-5:nth-child(3) {
  animation: step5_square3 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--step-5:nth-child(4) {
  animation: step5_square4 0.4s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}

/* EXIT ANIMATIONS */

/*
* STEP ONE
*/

@keyframes exit_step1_square1 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 0px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 4px;
    left: 4px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes exit_step1_square2 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step1_square3 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 0px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step1_square4 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

/*
* STEP TWO
*/

@keyframes exit_step2_square1 {
  0% {
    transform: rotate(45deg);
    top: 1px;
    left: 1px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 4px;
    left: 4px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes exit_step2_square2 {
  0% {
    transform: rotate(45deg);
    top: 1px;
    left: 15px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step2_square3 {
  0% {
    transform: rotate(45deg);
    top: 15px;
    left: 1px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step2_square4 {
  0% {
    transform: rotate(45deg);
    top: 15px;
    left: 15px;
    width: 8px;
    height: 8px;
    border-radius: 0%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

/*
* STEP THREE
*/

@keyframes exit_step3_square1 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 4px;
    left: 4px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes exit_step3_square2 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 18px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step3_square3 {
  0% {
    transform: rotate(0deg);
    top: 18px;
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step3_square4 {
  0% {
    transform: rotate(0deg);
    top: 18px;
    left: 18px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

/*
* STEP FOUR
*/

@keyframes exit_step4_square1 {
  0% {
    transform: rotate(0deg);
    top: 0px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 4px;
    left: 4px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes exit_step4_square2 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 16px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step4_square3 {
  0% {
    transform: rotate(0deg);
    top: 8px;
    left: 0px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step4_square4 {
  0% {
    transform: rotate(0deg);
    top: 16px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

/* STEP FIVE */

@keyframes exit_step5_square1 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 4px;
    left: 4px;
    width: 16px;
    height: 16px;
    border-radius: 0%;
  }
}

@keyframes exit_step5_square2 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step5_square3 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

@keyframes exit_step5_square4 {
  0% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
  100% {
    transform: rotate(45deg);
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}

.ai-icon__grid__square--exiting--step-1:nth-child(1) {
  animation: exit_step1_square1 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 0; */
}
.ai-icon__grid__square--exiting--step-1:nth-child(2) {
  animation: exit_step1_square2 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 200ms; */
}
.ai-icon__grid__square--exiting--step-1:nth-child(3) {
  animation: exit_step1_square3 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 400ms; */
}
.ai-icon__grid__square--exiting--step-1:nth-child(4) {
  animation: exit_step1_square4 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  /* animation-delay: 600ms; */
}

.ai-icon__grid__square--exiting--step-2:nth-child(1) {
  animation-delay: exit_ 200;
  animation: exit_step2_square1 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-2:nth-child(2) {
  animation-delay: exit_ 400ms;
  animation: exit_step2_square2 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-2:nth-child(3) {
  animation-delay: exit_ 600ms;
  animation: exit_step2_square3 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-2:nth-child(4) {
  animation-delay: exit_ 800ms;
  animation: exit_step2_square4 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}

.ai-icon__grid__square--exiting--step-3:nth-child(1) {
  animation: exit_step3_square1 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-3:nth-child(2) {
  animation: exit_step3_square2 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-3:nth-child(3) {
  animation: exit_step3_square3 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-3:nth-child(4) {
  animation: exit_step3_square4 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}

.ai-icon__grid__square--exiting--step-4:nth-child(1) {
  animation: exit_step4_square1 0.5s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.ai-icon__grid__square--exiting--step-4:nth-child(2) {
  animation: exit_step4_square2 0.5s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.ai-icon__grid__square--exiting--step-4:nth-child(3) {
  animation: exit_step4_square3 0.5s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.ai-icon__grid__square--exiting--step-4:nth-child(4) {
  animation: exit_step4_square4 0.5s forwards;
  /* animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1); */
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.ai-icon__grid__square--exiting--step-5:nth-child(1) {
  animation: exit_step5_square1 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-5:nth-child(2) {
  animation: exit_step5_square2 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-5:nth-child(3) {
  animation: exit_step5_square3 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.ai-icon__grid__square--exiting--step-5:nth-child(4) {
  animation: exit_step5_square4 0.5s forwards;
  animation-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
