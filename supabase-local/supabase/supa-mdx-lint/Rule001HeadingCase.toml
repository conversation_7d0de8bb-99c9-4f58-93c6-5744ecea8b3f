# Heading should be sentence case

# Words that may be uppercased even if they are not the first word in the sentence.
# Can also specify a regex that is compatible with the [Rust regex crate](https://docs.rs/regex/latest/regex/).
may_uppercase = [
    "[A-Z0-9]{2,5}s?",
    "APIs",
    "Add-ons?",
    "Amazon RDS",
    "Analytics",
    "Android",
    "Angular",
    "Apple",
    "Audit Logs?",
    "Auth",
    "Auth0",
    "Auth0 Actions?",
    "Azure",
    "Azure Developers?",
    "BigQuery",
    "Bitbucket",
    "Bitbucket Pipelines",
    "Boolean",
    "Branching",
    "Broadcast",
    "CAPTCHA",
    "Channel",
    "ChatGPT",
    "Chrome",
    "Chrome Developer Tools",
    "Clerk",
    "Cloudflare",
    "Cloudflare Workers?",
    "Code Exchange",
    "Colab",
    "Compute",
    "Compute Credits",
    "Compute Hours",
    "Content Delivery Network",
    "Copilot",
    "Cron",
    "Cron Jobs?",
    "Data API",
    "DataDog",
    "Dart",
    "Dashboard",
    "Database Functions?",
    "Deadpool",
    "Deno",
    "DigitalOcean",
    "Discord",
    "Discord Developers?",
    "Disk",
    "Django",
    "Docker",
    "Drain",
    "Drizzle",
    "Edge Functions?",
    "Editor",
    "Egress",
    "Enterprise",
    "Enterprise Plan",
    "Events",
    "Expo",
    "Ethereum",
    "Facebook",
    "Facebook Developers?",
    "Fair Use Policy",
    "Fees",
    "Figma",
    "Figma Developers?",
    "Firebase",
    "Firebase Authentication",
    "Firestore",
    "Flutter",
    "Functions?",
    "Free Plan",
    "Frequently Asked Questions",
    "Git",
    "GitHub",
    "GitHub Actions",
    "GitLab",
    "GoTrue",
    "Google",
    "Google Workspace",
    "Grafana",
    "GraphQL",
    "Heroku",
    "Homebrew",
    "Hooks?",
    "Hours",
    "Hugging Face",
    "I",
    "IPv4",
    "IPv6",
    "IVFFlat",
    "IdP",
    "Inbucket",
    "Index Advisor",
    "IntelliJ",
    "Ionic Angular",
    "Ionic React",
    "Ionic Vue",
    "JavaScript",
    "JSON Web Tokens?",
    "JWTs",
    "Kakao",
    "Kakao Developers?",
    "Kakao Login",
    "Keycloak",
    "Kotlin",
    "Kotlin Multiplatform",
    "Kysely",
    "Large Language Models?",
    "LinkedIn",
    "LinkedIn Developers?",
    "Linux",
    "LlamaIndex",
    "Llamafile",
    "Logs Explorer",
    "Magic Link",
    "Management API",
    "Mixpeek",
    "Mixpeek Embed",
    "MySQL",
    "Navigable Small World",
    "Neon",
    "Next.js",
    "Node.js",
    "Notion",
    "Nuxt",
    "OAuth",
    "Okta",
    "Ollama",
    "OpenAI",
    "Open ID Connect",
    "OrbStack",
    "OrioleDB",
    "PGAudit",
    "Phoenix",
    "Pro Plan",
    "Podman",
    "Poetry",
    "Postgres",
    "Postgres Changes",
    "PostgreSQL",
    "PostgREST",
    "Presence",
    "Prisma",
    "Prometheus",
    "Python",
    "Queues?",
    "Quotas",
    "Query Performance",
    "React",
    "React Email",
    "React Native",
    "Read Replicas?",
    "Reciprocal Ranked Fusion",
    "Redis",
    "RedwoodJS",
    "Remix",
    "Render",
    "Retrieval Plugin",
    "Roboflow Inference",
    "Row Level Security",
    "Send Email Hook",
    "SendGrid",
    "Sentry",
    "Server-Side Auth",
    "Server-Side Rendering",
    "Single Sign-On",
    "Slack",
    "Slack Developers?",
    "Social Login",
    "SolidJS",
    "Spend Cap",
    "Spotify",
    "Spotify Developers?",
    "Sqitch",
    "Storage",
    "Studio",
    "Supabase",
    "Supabase AI Assistant",
    "Supabase Marketplace",
    "Supavisor('s)?",
    "Svelte",
    "SvelteKit",
    "Swift",
    "SwiftUI",
    "Solana",
    "Team Plan",
    "Telegram",
    "Third-Party Auth",
    "TimescaleDB",
    "Transformers.js",
    "Twilio",
    "Twitch",
    "Twitch Developers?",
    "Twitter",
    "Twitter Developers?",
    "TypeScript",
    "Uppy",
    "Upstash",
    "URIs",
    "URLs",
    "Unsplash",
    "Usage",
    "Xcode",
    "Vault",
    "VSCode",
    "Vecs",
    "Vercel",
    "Vercel Marketplace",
    "Visual Studio Code",
    "Vue",
    "Wasm",
    "Web",
    "WebAssembly",
    "WebP",
    "WebSockets?",
    "WebStorm",
    "Web3",
    "Windows",
    "WorkOS",
    "Wrappers",
    "Write-Ahead Log(s|ging)?",
    "Zoom",
    "Zoom Developers?",
]

# Words that may be lowercased even if they are the first word in the sentence.
# Can also specify a regex that is compatible with the [Rust regex crate](https://docs.rs/regex/latest/regex/).
may_lowercase = ["asyncpg", "iOS", "imgproxy"]
