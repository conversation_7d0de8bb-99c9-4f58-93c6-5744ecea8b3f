# Check spelling
# Error message: "Word not found in dictionary"
#
# Allow list: Spellings that are actually correct, though they aren't in the
# dictionary.
#
# Prefixes: Strings that are not standalone words, but that can be used in a
# prefix before a hyphen, such as "pre" or "bi".
#
# Before adding a new word to the allow list, double check that it is in fact
# the correct casing for the word! Especially for styling of company and product
# names, the "official" casing and spacing might not be what you think.

allow_list = [
    "\\[#[A-Za-z0-9-]+\\]",
    "\\$\\$.+?\\$\\$",
    "\\s\\.[a-z]+",
    "\\S+\\.json",
    "\\S+\\.toml",
    "\\S+\\.yaml",
    "[A-Z]{2,5}s?",
    "[A-Za-z0-9_-]+(\\.[A-Z-a-z0-9_-]+)+(\\/[A-Za-z0-9_-]+)*",
    "[Aa]dd-ons?",
    "[Aa]llowlists?",
    "[Aa]utomations?",
    "[Aa]utovacuum(s|ing|ed)?",
    "[Bb]ackend",
    "[Bb]ackoff",
    "[Bb]lockchains?",
    "[Bb]reakpoints?",
    "[Bb]uilt-ins?",
    "[Cc]hangelogs?",
    "[Cc]odebases?",
    "[Cc]odepaths?",
    "[Cc]onfigs?",
    "[Cc]onsecutiveness",
    "[Cc]ooldowns?",
    "[Cc]ron",
    "[Dd]atasets?",
    "[Dd]atasources?",
    "[Dd]e facto",
    "[Dd]enylists?",
    "[Dd]evs?",
    "[Dd]iff(s|ing|ed)?",
    "[Dd]ropdown",
    "[Ee]nqueues?",
    "[Ee]nums?",
    "[Ee]nv",
    "[Ff]atals",
    "[Ff]rontend",
    "[Gg]apless",
    "[Gg]lobs?",
    "[Gg]lobstar",
    "[Gg]zip(s|ped|ping)?",
    "[Hh]r",
    "[Hh]ypertables?",
    "[KMG]bps",
    "[KMG]iB",
    "[Mm]atryoshka",
    "[Mm]essageBird",
    "[Mm]icroservices?",
    "[Mm]iddlewares?",
    "[Mm]onorepos?",
    "[Mm]ultimodal",
    "[Mm]ultipart",
    "[Mm]ultithreading",
    "[Nn]amespace(d|s)?",
    "[Nn]onces?",
    "[Nn]ullable",
    "[Oo]ffboarding",
    "[Oo]h",
    "[Oo]nboard(ing)?",
    "[Oo]vercommit(s|ted|ting)?",
    "[Pp]arams?",
    "[Pp]laintext",
    "[Pp]olyfill(s|ed)?",
    "[Pp]oolers?",
    "[Pp]resign(ed|ing)?",
    "[Pp]sycopg",
    "[Qq]uickstarts?",
    "[Rr]ealtime",
    "[Rr]eauthenticat(e|es|ed|ion)?",
    "[Rr]ebas(e|ed|es|ing)",
    "[Rr]eplayability",
    "[Rr]epos?",
    "[Rr]esultingly",
    "[Rr]untimes?",
    "[Ss]erverless",
    "[Ss]erverside",
    "[Ss]itekeys?",
    "[Ss]tateful",
    "[Ss]tructs?",
    "[Ss]ubcommands?",
    "[Ss]ubdomains?",
    "[Ss]ubfolders?",
    "[Ss]ubmodules?",
    "[Ss]wappiness",
    "[Tt]imebox(ed)?",
    "[Tt]odos?",
    "[Tt]radeoffs?",
    "[Uu]nlink(ing|s|ed)?",
    "[Uu]pserts?",
    "[Uu]ptime",
    "[Ww]aitlists?",
    "[Ww]ebhooks?",
    "Airbyte",
    "AndroidX",
    "AppleAuthentication",
    "Astro",
    "AsyncStorage",
    "Authn",
    "Authy",
    "B-tree",
    "Basejump",
    "BigQuery",
    "Bitbucket",
    "Bitwarden",
    "BotFather",
    "Brevo",
    "CAPTCHA",
    "Cartes Bancaires",
    "ChatGPT",
    "Citus",
    "ClickHouse",
    "Clippy",
    "Cloudflare",
    "Codium",
    "Cognito",
    "Colab",
    "DBeaver",
    "Database Functions?",
    "DataDog",
    "Deadpool",
    "DevTools",
    "DDoS",
    "Deno",
    "DigitalOcean",
    "Django",
    "Docker",
    "Drizzle",
    "ElevenLabs",
    "EnterpriseDB",
    "Entra",
    "ePHI",
    "Erlang",
    "Ethereum",
    "Figma",
    "Firestore",
    "Fivetran",
    "Floyd-Warshall",
    "GDScript",
    "GSSAPI",
    "Git",
    "GitHub",
    "GitLab",
    "GoTrue",
    "Golang",
    "Grafana",
    "GraphQL",
    "Groonga",
    "HackerOne",
    "HashiCorp",
    "Heroku",
    "Homebrew",
    "Hono",
    "Hyperdrive",
    "HypoPG",
    "IdP",
    "ImageMagick",
    "Inbucket",
    "Inferencer",
    "Infisical",
    "IntelliJ",
    "IntelliSense",
    "IOWait",
    "IVFFlat",
    "Jupyter",
    "JWTs",
    "Kakao",
    "Keycloak",
    "Kotlin",
    "Ktor",
    "Kysely",
    "LangChain",
    "Laravel",
    "LineString",
    "LinkedIn",
    "LlamaIndex",
    "Llamafile",
    "Logflare",
    "Lua",
    "Mailgun",
    "Mailtrap",
    "Mansueli",
    "Metabase",
    "Mixpeek",
    "Multiplatform",
    "MySQL",
    "[Nn]amespaces?",
    "Nano",
    "Netlify",
    "Next.js",
    "NoSQL",
    "Node.js",
    "Nuxt",
    "OAuth",
    "Okta",
    "Ollama",
    "OneLogin",
    "OpenAI",
    "OpenID",
    "OrbStack",
    "OrioleDB",
    "OpenTelemetry",
    "PGAudit",
    "PGroonga",
    "PgBouncer",
    "PHI",
    "PingIdentity",
    # For historical purposes
    "POSTGRES",
    "PascalCase",
    "Podman",
    "PostGIS",
    "PostQUEL",
    "PostgREST",
    "Postgres",
    # We prefer Postgres, but check for vocabulary preference in a separate rule
    "PostgreSQL",
    "ProGuard",
    "PubSub",
    "Prisma",
    "README",
    "Redis",
    "RedwoodJS",
    "Roboflow",
    "SDKs",
    "SQLAlchemy",
    "SQLModel",
    "SQLite",
    "SecureStore",
    "SendGrid",
    "Session Timebox",
    "Snaplet",
    "Solana",
    "SolidJS",
    "Spotify",
    "Sqitch",
    "Supavisor",
    "SvelteKit",
    "SwiftUI",
    "Supabase",
    "Remapper",
    "TextLocal",
    "TimescaleDB",
    "Transformers.js",
    "Twilio",
    "Undici",
    "UnionPay",
    "Unsplash",
    "Uppy",
    "Upstash",
    "[Uu]pvote(s|d)?",
    "VSCode",
    "Vecs",
    "Vercel",
    "Vite",
    "Vonage",
    "Vue",
    "Wasm",
    "WebAuthn",
    "WebAssembly",
    "WebP",
    "WebSockets?",
    "WebStorm",
    "WhatsApp",
    "WorkOS",
    "Xcode",
    "Zapier",
    "ZeptoMail",
    "asyncpg",
    "bcrypt",
    "camelCase",
    "dbdev",
    "degit",
    "deno-postgres",
    "dotenv",
    "e.g.",
    "gte-small",
    "hCaptcha",
    "https?:\\/\\/\\S+",
    "i.e.",
    "imgproxy",
    "iOS",
    "localStorage",
    "localhost",
    "macOS",
    "magick-wasm",
    "ms",
    "ngrok",
    "node-postgres",
    "npm",
    "npmrc",
    "npx",
    "ns",
    "pgAdmin",
    "pgAudit",
    "pgTAP",
    "pgloader",
    "pgmq",
    "pgsodium",
    "pgvector",
    "plpgsql",
    "psql",
    "scrypt",
    "sessionStorage",
    "stdin",
    "stdout",
    "[Ss]ubpage",
    "supabase-auth-ui",
    "supabase-csharp",
    "supabase-community",
    "supabase-flutter",
    "supabase-gdscript",
    "supabase-grafana",
    "supabase-go",
    "supabase-js",
    "supabase-kt",
    "supabase-management-js",
    "supabase-py",
    "supabase-rb",
    "supabase-swift",
    "supautils",
    "TanStack",
    "tokio",
    "tsvector",
    "tvOS",
    "uBlock Origin",
    "unbilled",
    "vecs",
    "vs",
    "watchOS",
]

prefixes = ["bi", "over", "pre", "un"]
