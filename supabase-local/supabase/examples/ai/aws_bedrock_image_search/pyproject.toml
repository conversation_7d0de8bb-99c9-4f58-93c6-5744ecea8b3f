[tool.poetry]
name = "image-search"
version = "0.1.0"
description = "Image Search with Supabase Vector"
authors = ["thorwebdev <<EMAIL>>"]
readme = "README.md"
packages = [{include = "image_search"}]

[tool.poetry.dependencies]
python = "^3.10"
matplotlib = "^3.7.1"
vecs = "^0.4.3"
boto3 = "^1.34.59"

[tool.poetry.scripts]
seed = "image_search.main:seed"
search = "image_search.main:search"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
