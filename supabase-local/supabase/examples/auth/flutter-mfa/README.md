# Flutter Supabase MFA Example

![Flutter MFA with Supa<PERSON>](https://raw.githubusercontent.com/supabase/supabase/master/examples/auth/flutter-mfa/images/mfa.png)

A Flutter app demonstrating how to implement Multi-Factor Authentication (MFA) with Supabase and Flutter. A user can sign up, add M<PERSON> via an authenticator app, and only after they have signed in using MFA they can view the content from the database.

- Full tutorial article [here](https://supabase.com/blog/flutter-multi-factor-authentication)

## Getting Started

- Create a new Supabase project [here](https://database.new)
- Add your Supabase credentials to `lib/main.dart`
- Run the following SQL from the SQL editor of your Supabase dashboard to create a table and dummy data

```sql
-- Dummy table that contains "secure" information
create table if not exists public.private_posts (
    id int generated by default as identity primary key,
    content text not null
);

-- Dmmy "secure" data
insert into public.private_posts
    (content)
values
    ('Flutter is awesome!'),
    ('Supabase is awesome!'),
    ('Postg<PERSON> is awesome!');

-- Enable RLS for private_posts table
alter table public.private_posts enable row level security;

-- Create a policy that only allows read if they user has signed in via MFA
create policy "Users can view private_posts if they have signed in via MFA"
  on public.private_posts
  for select
  to authenticated
  using ((select auth.jwt()->>'aal') = 'aal2');
```

- Run the app and test the login flow 🚀

## Resources

- [Flutter Tutorial: building a Flutter chat app article](https://supabase.com/blog/flutter-tutorial-building-a-chat-app)
- [Flutter Authorization with RLS article](https://supabase.com/blog/flutter-authorization-with-rls)
- [Supabase docs for Flutter](https://supabase.com/docs/reference/dart/introduction)
- [Supabase Flutter YouTube playlist](https://www.youtube.com/watch?v=F2j6Q-4nLEE&list=PL5S4mPUpp4OtkMf5LNDLXdTcAp1niHjoL)
