version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: pharmacy_postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: pharmacy_management
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: pharmacy_secure_2024
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c wal_level=logical
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c shared_preload_libraries=pg_stat_statements

  # PostgREST API
  postgrest:
    image: postgrest/postgrest:v12.0.2
    container_name: pharmacy_postgrest
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      PGRST_DB_URI: ******************************************************/pharmacy_management
      PGRST_DB_SCHEMAS: public,api
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: pharmacy-jwt-secret-key-32-characters-minimum-length
      PGRST_DB_USE_LEGACY_GUCS: "false"
    depends_on:
      - postgres

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pharmacy_pgadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: pharmacy_admin_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: pharmacy_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  pgadmin_data:
  redis_data:

networks:
  default:
    name: pharmacy_network
