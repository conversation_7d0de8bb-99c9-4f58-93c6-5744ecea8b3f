#!/bin/bash

# =============================================================================
# RCLONE SETUP SCRIPT FOR PHARMACY BACKUP
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"

# Load configuration
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "ERROR: Configuration file $CONFIG_FILE not found!"
    exit 1
fi

echo "=== Setting up rclone for Pharmacy Backup ==="

# Create rclone config directory
mkdir -p ~/.config/rclone

# =============================================================================
# GOOGLE CLOUD STORAGE SETUP
# =============================================================================

setup_gcs() {
    echo "Setting up Google Cloud Storage..."
    
    cat >> ~/.config/rclone/rclone.conf << EOF

[gcs]
type = google cloud storage
project_number = $GCS_PROJECT_ID
service_account_file = $SCRIPT_DIR/gcs-service-account.json
location = us-central1
storage_class = STANDARD

EOF

    echo "GCS configuration added to rclone.conf"
    echo "Please place your GCS service account JSON file at: $SCRIPT_DIR/gcs-service-account.json"
}

# =============================================================================
# CLOUDFLARE R2 SETUP
# =============================================================================

setup_r2() {
    echo "Setting up Cloudflare R2..."
    
    cat >> ~/.config/rclone/rclone.conf << EOF

[r2]
type = s3
provider = Cloudflare
access_key_id = $R2_ACCESS_KEY_ID
secret_access_key = $R2_SECRET_ACCESS_KEY
endpoint = https://$R2_ACCOUNT_ID.r2.cloudflarestorage.com
acl = private

EOF

    echo "R2 configuration added to rclone.conf"
}

# =============================================================================
# MAIN SETUP
# =============================================================================

main() {
    # Setup GCS if enabled
    if [[ "${ENABLE_GCS_BACKUP:-false}" == "true" ]]; then
        setup_gcs
    fi
    
    # Setup R2 if enabled
    if [[ "${ENABLE_R2_BACKUP:-false}" == "true" ]]; then
        setup_r2
    fi
    
    echo ""
    echo "=== rclone setup completed ==="
    echo ""
    echo "Next steps:"
    echo "1. For GCS: Place your service account JSON at $SCRIPT_DIR/gcs-service-account.json"
    echo "2. For R2: Update R2 credentials in $CONFIG_FILE"
    echo "3. Test connections:"
    echo "   rclone lsd gcs:"
    echo "   rclone lsd r2:"
    echo "4. Create buckets if they don't exist:"
    echo "   rclone mkdir gcs:$GCS_BUCKET_NAME"
    echo "   rclone mkdir r2:$R2_BUCKET_NAME"
}

main
