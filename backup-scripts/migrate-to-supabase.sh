#!/bin/bash

# =============================================================================
# PHARMACY MANAGEMENT MIGRATION TO SUPABASE
# =============================================================================
# Description: Migrate existing pharmacy_management_V2 to Supabase
# Author: Pharmacy Management System
# Version: 1.0
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$SCRIPT_DIR/logs/migration-$(date +%Y%m%d_%H%M%S).log"

# Create logs directory
mkdir -p "$SCRIPT_DIR/logs"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Configuration
OLD_DB_HOST="localhost"
OLD_DB_PORT="5432"
OLD_DB_USER="haint"
OLD_DB_NAME="pharmacy_management_v2_test"
OLD_DB_PASSWORD="your-old-password"

SUPABASE_DB_HOST="supabase-db"
SUPABASE_DB_PORT="5432"
SUPABASE_DB_USER="postgres"
SUPABASE_DB_NAME="pharmacy_management"
SUPABASE_DB_PASSWORD="pharmacy_secure_password_2024"

# =============================================================================
# MIGRATION FUNCTIONS
# =============================================================================

check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if old database is accessible
    if ! PGPASSWORD="$OLD_DB_PASSWORD" pg_isready -h "$OLD_DB_HOST" -p "$OLD_DB_PORT" -U "$OLD_DB_USER" -d "$OLD_DB_NAME" >/dev/null 2>&1; then
        error_exit "Cannot connect to old database: $OLD_DB_NAME"
    fi
    
    # Check if Supabase is running
    if ! docker ps | grep "supabase-db" | grep -q "healthy"; then
        error_exit "Supabase database is not healthy"
    fi
    
    # Check if Supabase database is accessible
    if ! docker exec supabase-db pg_isready -U "$SUPABASE_DB_USER" -d "$SUPABASE_DB_NAME" >/dev/null 2>&1; then
        error_exit "Cannot connect to Supabase database"
    fi
    
    log "Prerequisites check passed"
}

backup_old_database() {
    log "Creating backup of old database..."
    
    local backup_file="$SCRIPT_DIR/temp/old_db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    PGPASSWORD="$OLD_DB_PASSWORD" pg_dump \
        -h "$OLD_DB_HOST" \
        -p "$OLD_DB_PORT" \
        -U "$OLD_DB_USER" \
        -d "$OLD_DB_NAME" \
        --format=custom \
        --compress=9 \
        --file="$backup_file" \
        || error_exit "Failed to backup old database"
    
    log "Old database backed up to: $backup_file"
    echo "$backup_file"
}

export_schema_and_data() {
    log "Exporting schema and data from old database..."
    
    local schema_file="$SCRIPT_DIR/temp/schema_export.sql"
    local data_file="$SCRIPT_DIR/temp/data_export.sql"
    
    # Export schema only
    PGPASSWORD="$OLD_DB_PASSWORD" pg_dump \
        -h "$OLD_DB_HOST" \
        -p "$OLD_DB_PORT" \
        -U "$OLD_DB_USER" \
        -d "$OLD_DB_NAME" \
        --schema-only \
        --no-owner \
        --no-privileges \
        --file="$schema_file" \
        || error_exit "Failed to export schema"
    
    # Export data only (excluding system tables)
    PGPASSWORD="$OLD_DB_PASSWORD" pg_dump \
        -h "$OLD_DB_HOST" \
        -p "$OLD_DB_PORT" \
        -U "$OLD_DB_USER" \
        -d "$OLD_DB_NAME" \
        --data-only \
        --no-owner \
        --no-privileges \
        --exclude-table-data='audit_logs' \
        --exclude-table-data='session_logs' \
        --file="$data_file" \
        || error_exit "Failed to export data"
    
    log "Schema exported to: $schema_file"
    log "Data exported to: $data_file"
    
    echo "$schema_file $data_file"
}

prepare_supabase_schema() {
    log "Preparing Supabase schema..."
    
    local schema_file="$1"
    local supabase_schema="$SCRIPT_DIR/temp/supabase_schema.sql"
    
    # Clean up schema for Supabase compatibility
    cat "$schema_file" | \
        # Remove Supabase system schemas
        grep -v "CREATE SCHEMA auth" | \
        grep -v "CREATE SCHEMA storage" | \
        grep -v "CREATE SCHEMA realtime" | \
        grep -v "CREATE SCHEMA supabase_functions" | \
        grep -v "CREATE SCHEMA _realtime" | \
        grep -v "CREATE SCHEMA vault" | \
        # Remove extensions that Supabase already has
        grep -v "CREATE EXTENSION.*uuid-ossp" | \
        grep -v "CREATE EXTENSION.*pgcrypto" | \
        grep -v "CREATE EXTENSION.*pgjwt" | \
        # Add RLS policies placeholder
        sed '/^$/a\\n-- RLS Policies will be added here\n' \
        > "$supabase_schema"
    
    log "Supabase-compatible schema prepared: $supabase_schema"
    echo "$supabase_schema"
}

import_to_supabase() {
    local schema_file="$1"
    local data_file="$2"
    
    log "Importing schema to Supabase..."
    
    # Import schema
    docker exec -i supabase-db psql -U "$SUPABASE_DB_USER" -d "$SUPABASE_DB_NAME" < "$schema_file" \
        || error_exit "Failed to import schema to Supabase"
    
    log "Importing data to Supabase..."
    
    # Import data
    docker exec -i supabase-db psql -U "$SUPABASE_DB_USER" -d "$SUPABASE_DB_NAME" < "$data_file" \
        || error_exit "Failed to import data to Supabase"
    
    log "Schema and data imported successfully"
}

setup_rls_policies() {
    log "Setting up Row Level Security policies..."
    
    local rls_file="$SCRIPT_DIR/temp/rls_policies.sql"
    
    cat > "$rls_file" << 'EOF'
-- Enable RLS on main tables
ALTER TABLE IF EXISTS branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS users ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS products ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS prescription_items ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized later)
-- Allow authenticated users to read their branch data
CREATE POLICY "Users can view their branch data" ON branches
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can view customers" ON customers
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can view products" ON products
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can view inventory" ON inventory
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- More specific policies can be added based on requirements
EOF

    docker exec -i supabase-db psql -U "$SUPABASE_DB_USER" -d "$SUPABASE_DB_NAME" < "$rls_file" \
        || log "Warning: Some RLS policies may have failed (this is normal if tables don't exist)"
    
    log "RLS policies setup completed"
}

verify_migration() {
    log "Verifying migration..."
    
    # Count tables in Supabase
    local table_count=$(docker exec supabase-db psql -U "$SUPABASE_DB_USER" -d "$SUPABASE_DB_NAME" -t -c "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    " | xargs)
    
    log "Tables in Supabase: $table_count"
    
    # List main tables
    log "Main tables in Supabase:"
    docker exec supabase-db psql -U "$SUPABASE_DB_USER" -d "$SUPABASE_DB_NAME" -c "
        SELECT table_name, 
               (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as columns
        FROM information_schema.tables t
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        ORDER BY table_name;
    " || true
    
    log "Migration verification completed"
}

# =============================================================================
# MAIN MIGRATION PROCESS
# =============================================================================

main() {
    log "=== Starting Pharmacy Management Migration to Supabase ==="
    log "Migration started at: $(date)"
    
    # Create temp directory
    mkdir -p "$SCRIPT_DIR/temp"
    
    # Step 1: Check prerequisites
    log "Step 1: Checking prerequisites..."
    check_prerequisites
    
    # Step 2: Backup old database
    log "Step 2: Backing up old database..."
    local backup_file=$(backup_old_database)
    
    # Step 3: Export schema and data
    log "Step 3: Exporting schema and data..."
    local export_files=$(export_schema_and_data)
    local schema_file=$(echo $export_files | cut -d' ' -f1)
    local data_file=$(echo $export_files | cut -d' ' -f2)
    
    # Step 4: Prepare Supabase-compatible schema
    log "Step 4: Preparing Supabase-compatible schema..."
    local supabase_schema=$(prepare_supabase_schema "$schema_file")
    
    # Step 5: Import to Supabase
    log "Step 5: Importing to Supabase..."
    import_to_supabase "$supabase_schema" "$data_file"
    
    # Step 6: Setup RLS policies
    log "Step 6: Setting up RLS policies..."
    setup_rls_policies
    
    # Step 7: Verify migration
    log "Step 7: Verifying migration..."
    verify_migration
    
    log "=== Migration completed successfully ==="
    log "Migration finished at: $(date)"
    log ""
    log "Next steps:"
    log "1. Review RLS policies in Supabase Studio: http://localhost:3002"
    log "2. Test application connectivity"
    log "3. Update application configuration to use Supabase"
    log "4. Setup backup schedule: ./install-crontab.sh"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

# Show usage if help requested
if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    echo "Usage: $0 [--dry-run]"
    echo ""
    echo "Migrate pharmacy_management_V2 database to Supabase"
    echo ""
    echo "Options:"
    echo "  --dry-run    Show what would be done without making changes"
    echo "  --help       Show this help message"
    exit 0
fi

# Dry run mode
if [[ "${1:-}" == "--dry-run" ]]; then
    log "DRY RUN MODE - No changes will be made"
    log "Would migrate from: $OLD_DB_NAME@$OLD_DB_HOST:$OLD_DB_PORT"
    log "Would migrate to: $SUPABASE_DB_NAME@$SUPABASE_DB_HOST:$SUPABASE_DB_PORT"
    exit 0
fi

# Confirmation prompt
echo "⚠️  WARNING: This will migrate data to Supabase database!"
echo "Source: $OLD_DB_NAME@$OLD_DB_HOST:$OLD_DB_PORT"
echo "Target: $SUPABASE_DB_NAME@$SUPABASE_DB_HOST:$SUPABASE_DB_PORT"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [[ "$confirm" != "yes" ]]; then
    log "Migration cancelled by user"
    exit 0
fi

# Run migration
main
