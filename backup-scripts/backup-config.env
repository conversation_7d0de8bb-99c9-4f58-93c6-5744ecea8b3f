# =============================================================================
# PHARMACY MANAGEMENT BACKUP CONFIGURATION
# =============================================================================

# Database Configuration
DB_HOST="supabase-db"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="pharmacy_secure_password_2024"
DB_NAME="pharmacy_management"

# Backup Settings
BACKUP_RETENTION_DAYS=30
ENABLE_COMPRESSION=true

# Google Cloud Storage Configuration
ENABLE_GCS_BACKUP=false
GCS_BUCKET_NAME="pharmacy-backups-gcs"
GCS_PROJECT_ID="your-gcp-project-id"

# Cloudflare R2 Configuration
ENABLE_R2_BACKUP=false
R2_BUCKET_NAME="pharmacy-backups-r2"
R2_ACCOUNT_ID="your-cloudflare-account-id"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret-key"

# Notification Settings (Optional)
ENABLE_SLACK_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=""

ENABLE_EMAIL_NOTIFICATIONS=false
EMAIL_TO="<EMAIL>"
EMAIL_FROM="<EMAIL>"

# Monitoring
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_URL=""

# Advanced Settings
PARALLEL_UPLOADS=false
ENCRYPTION_ENABLED=false
ENCRYPTION_KEY=""
