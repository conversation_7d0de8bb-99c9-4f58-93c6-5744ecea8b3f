#!/bin/bash

# =============================================================================
# PHARMACY MANAGEMENT RESTORE SCRIPT
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"
LOG_FILE="$SCRIPT_DIR/logs/restore-$(date +%Y%m%d_%H%M%S).log"

# Load configuration
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "ERROR: Configuration file $CONFIG_FILE not found!"
    exit 1
fi

# Create logs directory
mkdir -p "$SCRIPT_DIR/logs"
mkdir -p "$SCRIPT_DIR/temp"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    cleanup
    exit 1
}

# Cleanup function
cleanup() {
    log "Cleaning up temporary files..."
    rm -f "$SCRIPT_DIR/temp/"*.sql*
}

# Trap for cleanup on exit
trap cleanup EXIT

# =============================================================================
# RESTORE FUNCTIONS
# =============================================================================

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --list-gcs           List available backups in Google Cloud Storage"
    echo "  --list-r2            List available backups in Cloudflare R2"
    echo "  --restore-gcs FILE   Restore from GCS backup file"
    echo "  --restore-r2 FILE    Restore from R2 backup file"
    echo "  --restore-local FILE Restore from local backup file"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --list-gcs"
    echo "  $0 --restore-gcs pharmacy-backups/2024/01/pharmacy_backup_20240115_120000.sql.gz"
    echo "  $0 --restore-local /path/to/backup.sql.gz"
}

list_gcs_backups() {
    log "Listing available backups in Google Cloud Storage..."
    rclone ls gcs:$GCS_BUCKET_NAME/pharmacy-backups/ | grep "\.sql\.gz$" | sort -r
}

list_r2_backups() {
    log "Listing available backups in Cloudflare R2..."
    rclone ls r2:$R2_BUCKET_NAME/pharmacy-backups/ | grep "\.sql\.gz$" | sort -r
}

download_backup() {
    local source="$1"
    local filename="$2"
    local local_path="$SCRIPT_DIR/temp/$filename"
    
    log "Downloading backup: $source"
    
    if [[ "$source" == gcs:* ]]; then
        rclone copy "$source" "$SCRIPT_DIR/temp/" || error_exit "Failed to download from GCS"
    elif [[ "$source" == r2:* ]]; then
        rclone copy "$source" "$SCRIPT_DIR/temp/" || error_exit "Failed to download from R2"
    else
        cp "$source" "$local_path" || error_exit "Failed to copy local file"
    fi
    
    echo "$local_path"
}

restore_database() {
    local backup_file="$1"
    
    log "Starting database restore from: $backup_file"
    
    # Check if file exists
    if [[ ! -f "$backup_file" ]]; then
        error_exit "Backup file not found: $backup_file"
    fi
    
    # Decompress if needed
    local sql_file="$backup_file"
    if [[ "$backup_file" == *.gz ]]; then
        log "Decompressing backup file..."
        sql_file="${backup_file%.gz}"
        gunzip -c "$backup_file" > "$sql_file" || error_exit "Failed to decompress backup"
    fi
    
    # Confirm restore
    echo ""
    echo "WARNING: This will COMPLETELY REPLACE the current database!"
    echo "Database: $DB_NAME"
    echo "Host: $DB_HOST:$DB_PORT"
    echo "Backup file: $backup_file"
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        log "Restore cancelled by user"
        exit 0
    fi
    
    # Set PostgreSQL password
    export PGPASSWORD="$DB_PASSWORD"
    
    # Drop existing connections
    log "Terminating existing database connections..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "
        SELECT pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();
    " || true
    
    # Drop and recreate database
    log "Dropping and recreating database..."
    dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" || true
    createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" || error_exit "Failed to create database"
    
    # Restore from backup
    log "Restoring database from backup..."
    if [[ "$sql_file" == *.sql ]]; then
        # Plain SQL format
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file" \
            || error_exit "Failed to restore database"
    else
        # Custom format
        pg_restore -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            --verbose --clean --if-exists "$sql_file" \
            || error_exit "Failed to restore database"
    fi
    
    # Unset password
    unset PGPASSWORD
    
    log "Database restore completed successfully"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    case "${1:-}" in
        --list-gcs)
            list_gcs_backups
            ;;
        --list-r2)
            list_r2_backups
            ;;
        --restore-gcs)
            if [[ -z "${2:-}" ]]; then
                error_exit "Please specify backup file path"
            fi
            local gcs_path="gcs:$GCS_BUCKET_NAME/$2"
            local filename=$(basename "$2")
            local local_file=$(download_backup "$gcs_path" "$filename")
            restore_database "$local_file"
            ;;
        --restore-r2)
            if [[ -z "${2:-}" ]]; then
                error_exit "Please specify backup file path"
            fi
            local r2_path="r2:$R2_BUCKET_NAME/$2"
            local filename=$(basename "$2")
            local local_file=$(download_backup "$r2_path" "$filename")
            restore_database "$local_file"
            ;;
        --restore-local)
            if [[ -z "${2:-}" ]]; then
                error_exit "Please specify local backup file path"
            fi
            restore_database "$2"
            ;;
        --help|*)
            show_usage
            ;;
    esac
}

main "$@"
