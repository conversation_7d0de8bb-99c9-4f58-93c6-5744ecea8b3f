# 🚀 Quick Start Guide - Pharmacy Management System

## 🎯 **TRUY CẬP NGAY**

### **📊 Supabase Studio Dashboard**
```
🌐 URL: http://localhost:3002
👤 Username: pharmacy_admin
🔑 Password: pharmacy_admin_secure_2024
```

### **🔌 API Endpoint**
```
🌐 Base URL: http://localhost:8002
🔑 Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
```

### **🗄️ Database Connection**
```
🏠 Host: localhost
🚪 Port: 5435
🗃️ Database: pharmacy_management
👤 User: postgres
🔑 Password: pharmacy_secure_password_2024
```

## ⚡ **QUICK COMMANDS**

### **🔍 Check System Status**
```bash
cd /root/pharmacy_management/backup-scripts
./backup-monitor.sh health
```

### **💾 Manual Backup**
```bash
./pharmacy-backup.sh
```

### **📋 View Logs**
```bash
# Backup logs
tail -f logs/backup-$(date +%Y%m%d).log

# Monitor logs  
tail -f logs/monitor.log

# <PERSON><PERSON> logs
tail -f logs/cron.log
```

### **🐳 Docker Management**
```bash
# Check containers
docker ps | grep supabase

# Restart Supabase
cd /root/pharmacy_management/supabase-local/supabase/docker
docker compose restart

# View container logs
docker logs supabase-db
docker logs supabase-studio
```

## 🛠️ **DEVELOPMENT WORKFLOW**

### **1. Connect to Database**
```bash
# Using psql
PGPASSWORD="pharmacy_secure_password_2024" psql -h localhost -p 5435 -U postgres -d pharmacy_management

# Using Docker exec
docker exec -it supabase-db psql -U postgres -d pharmacy_management
```

### **2. API Testing**
```bash
# Test API health
curl http://localhost:8002/rest/v1/

# Test with API key
curl -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE" \
     http://localhost:8002/rest/v1/
```

### **3. Schema Management**
```sql
-- Create a test table
CREATE TABLE test_table (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE test_table ENABLE ROW LEVEL SECURITY;

-- Create policy
CREATE POLICY "Allow all for authenticated users" ON test_table
    FOR ALL USING (auth.uid() IS NOT NULL);
```

## 📱 **FRONTEND INTEGRATION**

### **Next.js Setup**
```bash
# Install Supabase client
npm install @supabase/supabase-js

# Environment variables (.env.local)
NEXT_PUBLIC_SUPABASE_URL=http://localhost:8002
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
```

### **Client Configuration**
```javascript
// lib/supabase.js
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

## 🔧 **TROUBLESHOOTING**

### **Common Issues**

#### **🚫 Cannot access Studio**
```bash
# Check if container is running
docker ps | grep supabase-studio

# Check logs
docker logs supabase-studio

# Restart if needed
docker restart supabase-studio
```

#### **🚫 Database connection failed**
```bash
# Check database health
docker exec supabase-db pg_isready -U postgres

# Check if port is open
netstat -tlnp | grep 5435

# Test connection
PGPASSWORD="pharmacy_secure_password_2024" pg_isready -h localhost -p 5435 -U postgres
```

#### **🚫 API not responding**
```bash
# Check Kong gateway
docker logs supabase-kong

# Check PostgREST
docker logs supabase-rest

# Test direct connection
curl http://localhost:8002/rest/v1/
```

#### **🚫 Backup failing**
```bash
# Check disk space
df -h

# Check permissions
ls -la backup-scripts/

# Run health check
./backup-monitor.sh health

# Check logs
tail -f logs/backup-$(date +%Y%m%d).log
```

## 📚 **USEFUL RESOURCES**

### **Documentation**
- 📖 [Supabase Docs](https://supabase.com/docs)
- 📖 [PostgreSQL Docs](https://www.postgresql.org/docs/)
- 📖 [PostgREST API](https://postgrest.org/en/stable/)

### **Local Files**
- 📄 `README.md` - Complete documentation
- 📄 `DEPLOYMENT_SUMMARY.md` - Full deployment details
- 📄 `CLOUD_SETUP.md` - Cloud storage configuration
- 📄 `backup-config.env` - Backup configuration

### **Key Directories**
```
📁 /root/pharmacy_management/
├── 💾 backup-scripts/          # Backup system
├── 🏗️ supabase-local/         # Supabase installation  
├── 📊 pharmacy_management_V2/  # Original schema
└── 📋 logs/                   # System logs
```

## 🎯 **NEXT STEPS**

### **Immediate (Today)**
1. ✅ **Access Studio**: http://localhost:3002
2. ✅ **Test API**: http://localhost:8002/rest/v1/
3. ✅ **Review schema** in Studio
4. ✅ **Test backup**: `./pharmacy-backup.sh`

### **This Week**
1. 🔄 **Migrate data** from old system
2. 🔐 **Setup RLS policies**
3. 🎨 **Build frontend** with Next.js
4. ☁️ **Configure cloud backup**

### **Production Ready**
1. 🌐 **Setup domain & SSL**
2. 🔒 **Security hardening**
3. 📊 **Performance monitoring**
4. 🚀 **Deploy to production**

---

## 🎉 **YOU'RE ALL SET!**

**Pharmacy Management System với Supabase đã sẵn sàng!**

- ✅ **Database**: PostgreSQL 15 với Supabase features
- ✅ **API**: RESTful + Real-time subscriptions  
- ✅ **Dashboard**: Web management interface
- ✅ **Backup**: Automated daily backups
- ✅ **Monitoring**: Health checks & alerts

**Happy coding! 🚀**

---

*Need help? Check logs or run health checks first!*
