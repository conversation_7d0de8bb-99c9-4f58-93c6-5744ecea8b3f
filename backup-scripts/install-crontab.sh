#!/bin/bash

# =============================================================================
# INSTALL CRONTAB FOR PHARMACY BACKUP
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== Installing Pharmacy Backup Crontab ==="

# Create crontab entries
CRONTAB_ENTRIES=$(cat << EOF
# Pharmacy Management Backup Schedule
# Daily backup at 2:00 AM
0 2 * * * $SCRIPT_DIR/pharmacy-backup.sh >> $SCRIPT_DIR/logs/cron.log 2>&1

# Health check every 6 hours
0 */6 * * * $SCRIPT_DIR/backup-monitor.sh health >> $SCRIPT_DIR/logs/monitor.log 2>&1

# Weekly cleanup at 3:00 AM on Sundays
0 3 * * 0 find $SCRIPT_DIR/logs -name "*.log" -mtime +30 -delete

EOF
)

# Backup existing crontab
echo "Backing up existing crontab..."
crontab -l > "$SCRIPT_DIR/crontab.backup" 2>/dev/null || echo "No existing crontab found"

# Add new entries to crontab
echo "Adding backup schedule to crontab..."
(crontab -l 2>/dev/null || true; echo "$CRONTAB_ENTRIES") | crontab -

echo "Crontab installed successfully!"
echo ""
echo "Backup Schedule:"
echo "- Daily backup: 2:00 AM"
echo "- Health checks: Every 6 hours"
echo "- Log cleanup: Weekly on Sunday 3:00 AM"
echo ""
echo "To view current crontab: crontab -l"
echo "To remove crontab: crontab -r"
echo "To restore backup: crontab $SCRIPT_DIR/crontab.backup"
