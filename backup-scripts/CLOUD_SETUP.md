# ☁️ Cloud Storage Setup Guide

Hướng dẫn cấu hình Google Cloud Storage và Cloudflare R2 cho backup tự động.

## 🔧 Google Cloud Storage Setup

### 1. Tạo GCP Project và Service Account

```bash
# 1. Tạo project mới (hoặc sử dụng existing)
gcloud projects create pharmacy-backup-project

# 2. Enable APIs
gcloud services enable storage-component.googleapis.com
gcloud services enable storage-api.googleapis.com

# 3. Tạo service account
gcloud iam service-accounts create pharmacy-backup \
    --display-name="Pharmacy Backup Service Account"

# 4. Tạo và download key
gcloud iam service-accounts keys create gcs-service-account.json \
    --iam-account=<EMAIL>
```

### 2. Tạo Storage Bucket

```bash
# Tạo bucket với lifecycle policy
gsutil mb -p pharmacy-backup-project gs://pharmacy-backups-gcs

# Set lifecycle policy (tự động xóa backup cũ sau 90 ngày)
cat > lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {"age": 90}
      }
    ]
  }
}
EOF

gsutil lifecycle set lifecycle.json gs://pharmacy-backups-gcs
```

### 3. Cấu hình Permissions

```bash
# Grant storage admin role
gcloud projects add-iam-policy-binding pharmacy-backup-project \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.admin"
```

### 4. Cập nhật Configuration

```bash
# Copy service account key
cp gcs-service-account.json /root/pharmacy_management/backup-scripts/

# Update backup-config.env
nano backup-config.env
```

```env
# Google Cloud Storage Configuration
ENABLE_GCS_BACKUP=true
GCS_BUCKET_NAME="pharmacy-backups-gcs"
GCS_PROJECT_ID="pharmacy-backup-project"
```

## 🌐 Cloudflare R2 Setup

### 1. Tạo R2 Bucket

1. Đăng nhập vào [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Chọn **R2 Object Storage**
3. Tạo bucket mới: `pharmacy-backups-r2`
4. Chọn region gần nhất

### 2. Tạo API Token

1. Vào **R2** → **Manage R2 API tokens**
2. Tạo token mới với permissions:
   - **Object Read & Write**
   - **Bucket Read & Write**
3. Lưu lại:
   - Access Key ID
   - Secret Access Key
   - Account ID

### 3. Cập nhật Configuration

```env
# Cloudflare R2 Configuration  
ENABLE_R2_BACKUP=true
R2_BUCKET_NAME="pharmacy-backups-r2"
R2_ACCOUNT_ID="your-cloudflare-account-id"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret-key"
```

### 4. Setup Lifecycle Policy (Optional)

```bash
# Sử dụng rclone để set lifecycle
rclone config create r2-lifecycle s3 \
    provider Cloudflare \
    access_key_id YOUR_ACCESS_KEY \
    secret_access_key YOUR_SECRET_KEY \
    endpoint https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com

# Set lifecycle rule (90 days retention)
aws s3api put-bucket-lifecycle-configuration \
    --endpoint-url https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com \
    --bucket pharmacy-backups-r2 \
    --lifecycle-configuration file://r2-lifecycle.json
```

## 🧪 Testing Cloud Setup

### Test GCS Connection

```bash
# Test rclone GCS
rclone lsd gcs:

# Test upload
echo "test" > test.txt
rclone copy test.txt gcs:pharmacy-backups-gcs/test/
rclone ls gcs:pharmacy-backups-gcs/test/
```

### Test R2 Connection

```bash
# Test rclone R2
rclone lsd r2:

# Test upload
rclone copy test.txt r2:pharmacy-backups-r2/test/
rclone ls r2:pharmacy-backups-r2/test/
```

### Enable Cloud Backup

```bash
# Update config
nano backup-config.env

# Set to true
ENABLE_GCS_BACKUP=true
ENABLE_R2_BACKUP=true

# Test backup
./pharmacy-backup.sh
```

## 💰 Cost Estimation

### Google Cloud Storage

- **Storage**: ~$0.020/GB/month (Standard)
- **Operations**: ~$0.05/10,000 operations
- **Network**: Free egress to same region

**Monthly cost for 10GB backup**: ~$0.20

### Cloudflare R2

- **Storage**: $0.015/GB/month
- **Operations**: Free (Class A: 1M/month, Class B: 10M/month)
- **Egress**: Free

**Monthly cost for 10GB backup**: ~$0.15

## 🔐 Security Best Practices

1. **Rotate API keys** every 90 days
2. **Use least privilege** permissions
3. **Enable bucket versioning** for point-in-time recovery
4. **Monitor access logs** for unusual activity
5. **Encrypt backups** before upload (optional)

## 📊 Monitoring & Alerts

### Setup Monitoring

```bash
# Enable notifications
nano backup-config.env
```

```env
# Notification Settings
ENABLE_SLACK_NOTIFICATIONS=true
SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

ENABLE_EMAIL_NOTIFICATIONS=true
EMAIL_TO="<EMAIL>"
```

### Test Notifications

```bash
# Test Slack
./backup-monitor.sh test-slack

# Test Email
./backup-monitor.sh test-email
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission denied**: Check service account permissions
2. **Bucket not found**: Verify bucket name and region
3. **Network timeout**: Check firewall rules
4. **Quota exceeded**: Monitor storage usage

### Debug Commands

```bash
# Check rclone config
rclone config show

# Test with verbose output
rclone -v copy test.txt gcs:pharmacy-backups-gcs/

# Check logs
tail -f logs/backup-$(date +%Y%m%d).log
```

---

**📞 Support**: Nếu gặp vấn đề, kiểm tra logs và documentation của từng provider.
