[2025-05-24 03:55:47] Performing health check...
[2025-05-24 03:55:47] ERROR: Supabase database is not healthy
[2025-05-24 03:55:47] Cleaning up temporary files...
[2025-05-24 03:55:47] Cleaning up temporary files...
[2025-05-24 03:56:41] Performing health check...
[2025-05-24 03:56:41] ERROR: Supabase database is not healthy
[2025-05-24 03:56:41] Cleaning up temporary files...
[2025-05-24 03:56:41] Cleaning up temporary files...
[2025-05-24 03:57:19] Performing health check...
[2025-05-24 03:57:19] Health check passed
[2025-05-24 03:57:19] Cleaning up temporary files...
[2025-05-24 03:57:33] === Starting Pharmacy Management Backup ===
[2025-05-24 03:57:33] Backup started at: Sat May 24 03:57:33 AM UTC 2025
[2025-05-24 03:57:33] Step 1: Creating PostgreSQL dump...
[2025-05-24 03:57:33] Creating PostgreSQL dump for database: pharmacy_management
[2025-05-24 03:57:33] ERROR: Failed to create PostgreSQL dump
[2025-05-24 03:57:33] Cleaning up temporary files...
[2025-05-24 03:57:33] Cleaning up temporary files...
[2025-05-24 03:58:08] === Starting Pharmacy Management Backup ===
[2025-05-24 03:58:08] Backup started at: Sat May 24 03:58:08 AM UTC 2025
[2025-05-24 03:58:08] Step 1: Creating PostgreSQL dump...
[2025-05-24 03:58:08] Creating PostgreSQL dump for database: pharmacy_management
[2025-05-24 03:58:08] ERROR: Failed to create PostgreSQL dump
[2025-05-24 03:58:08] Cleaning up temporary files...
[2025-05-24 03:58:08] Cleaning up temporary files...
[2025-05-24 03:58:41] === Starting Pharmacy Management Backup ===
[2025-05-24 03:58:41] Backup started at: Sat May 24 03:58:41 AM UTC 2025
[2025-05-24 03:58:41] Step 1: Creating PostgreSQL dump...
[2025-05-24 03:58:41] Creating PostgreSQL dump for database: pharmacy_management
[2025-05-24 03:58:41] PostgreSQL dump created successfully. Size: 180K
[2025-05-24 03:58:41] Step 2: Compressing backup...
[2025-05-24 03:58:41] Compressing backup file...
[2025-05-24 03:58:41] Cleaning up temporary files...
[2025-05-24 03:59:37] === Starting Pharmacy Management Backup ===
[2025-05-24 03:59:37] Backup started at: Sat May 24 03:59:37 AM UTC 2025
[2025-05-24 03:59:37] Step 1: Creating PostgreSQL dump...
[2025-05-24 03:59:37] Creating PostgreSQL dump for database: pharmacy_management
[2025-05-24 03:59:37] PostgreSQL dump created successfully. Size: 180K
[2025-05-24 03:59:37] Step 2: Backup ready for upload...
[2025-05-24 03:59:37] Step 3: Uploading to Google Cloud Storage...
[2025-05-24 03:59:37] Uploading to Google Cloud Storage: gs://pharmacy-backups-gcs/pharmacy-backups/2025/05/pharmacy_backup_20250524_035937.dump
[2025-05-24 03:59:37] ERROR: Failed to upload to Google Cloud Storage
[2025-05-24 03:59:37] Cleaning up temporary files...
[2025-05-24 03:59:37] Cleaning up temporary files...
[2025-05-24 03:59:56] === Starting Pharmacy Management Backup ===
[2025-05-24 03:59:56] Backup started at: Sat May 24 03:59:56 AM UTC 2025
[2025-05-24 03:59:56] Step 1: Creating PostgreSQL dump...
[2025-05-24 03:59:56] Creating PostgreSQL dump for database: pharmacy_management
[2025-05-24 03:59:56] PostgreSQL dump created successfully. Size: 180K
[2025-05-24 03:59:56] Step 2: Backup ready for upload...
[2025-05-24 03:59:56] Step 5: Cleaning up old backups...
[2025-05-24 03:59:56] Cleaning up local backups older than 30 days...
[2025-05-24 03:59:56] Cleanup completed
[2025-05-24 03:59:56] === Backup completed successfully ===
[2025-05-24 03:59:56] Backup finished at: Sat May 24 03:59:56 AM UTC 2025
[2025-05-24 03:59:56] Cleaning up temporary files...
