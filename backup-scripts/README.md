# 🏥 Pharmacy Management Backup System

Hệ thống backup tự động cho Supabase PostgreSQL với khả năng lưu trữ trên Google Cloud Storage và Cloudflare R2.

## 📋 Tính năng

- ✅ **Backup tự động hàng ngày** PostgreSQL database
- ☁️ **Dual cloud storage**: Google Cloud Storage + Cloudflare R2
- 🗜️ **Nén dữ liệu** để tiết kiệm không gian
- 📊 **Monitoring & Health checks**
- 🔔 **Notifications** qua Slack và Email
- 🔄 **Easy restore** từ bất kỳ backup nào
- 🗂️ **Retention policies** tự động dọn dẹp
- 📝 **Detailed logging** cho troubleshooting

## 🚀 Cài đặt nhanh

```bash
# 1. Chạy script setup
cd backup-scripts
chmod +x setup-backup.sh
./setup-backup.sh

# 2. Cấu hình cloud credentials (nếu cần)
nano backup-config.env

# 3. Test backup
./pharmacy-backup.sh
```

## ⚙️ C<PERSON><PERSON> hình

### Database Settings
```bash
DB_HOST="localhost"
DB_PORT="5435"
DB_USER="postgres"
DB_PASSWORD="your-password"
DB_NAME="pharmacy_management"
```

### Google Cloud Storage
```bash
ENABLE_GCS_BACKUP=true
GCS_BUCKET_NAME="pharmacy-backups-gcs"
GCS_PROJECT_ID="your-gcp-project-id"
```

### Cloudflare R2
```bash
ENABLE_R2_BACKUP=true
R2_BUCKET_NAME="pharmacy-backups-r2"
R2_ACCOUNT_ID="your-account-id"
R2_ACCESS_KEY_ID="your-access-key"
R2_SECRET_ACCESS_KEY="your-secret-key"
```

## 📅 Lịch trình Backup

| Thời gian | Tác vụ | Mô tả |
|-----------|--------|-------|
| 2:00 AM | Daily Backup | Backup database hàng ngày |
| Every 6h | Health Check | Kiểm tra tình trạng hệ thống |
| Sunday 3:00 AM | Cleanup | Dọn dẹp logs cũ |

## 🛠️ Sử dụng

### Backup thủ công
```bash
./pharmacy-backup.sh
```

### Kiểm tra tình trạng
```bash
./backup-monitor.sh health
```

### Liệt kê backups
```bash
# Google Cloud Storage
./pharmacy-restore.sh --list-gcs

# Cloudflare R2
./pharmacy-restore.sh --list-r2
```

### Restore database
```bash
# Từ GCS
./pharmacy-restore.sh --restore-gcs pharmacy-backups/2024/01/pharmacy_backup_20240115_120000.sql.gz

# Từ R2
./pharmacy-restore.sh --restore-r2 pharmacy-backups/2024/01/pharmacy_backup_20240115_120000.sql.gz

# Từ file local
./pharmacy-restore.sh --restore-local /path/to/backup.sql.gz
```

## 📊 Monitoring

### Health Check
```bash
./backup-monitor.sh health
```

### Test Notifications
```bash
# Test Slack
./backup-monitor.sh test-slack

# Test Email
./backup-monitor.sh test-email
```

### Logs
```bash
# Backup logs
tail -f logs/backup-$(date +%Y%m%d).log

# Monitor logs
tail -f logs/monitor.log

# Cron logs
tail -f logs/cron.log
```

## 🔧 Troubleshooting

### Database connection issues
```bash
# Test connection
pg_isready -h localhost -p 5435 -U postgres -d pharmacy_management

# Check Supabase status
docker ps | grep supabase-db
```

### Cloud storage issues
```bash
# Test rclone connections
rclone lsd gcs:
rclone lsd r2:

# Check rclone config
rclone config show
```

### Permission issues
```bash
# Fix script permissions
chmod +x *.sh

# Check crontab
crontab -l
```

## 📁 Cấu trúc thư mục

```
backup-scripts/
├── pharmacy-backup.sh      # Script backup chính
├── pharmacy-restore.sh     # Script restore
├── backup-monitor.sh       # Monitoring & notifications
├── setup-backup.sh         # Setup tự động
├── setup-rclone.sh         # Cấu hình rclone
├── install-crontab.sh      # Cài đặt crontab
├── backup-config.env       # File cấu hình
├── logs/                   # Thư mục logs
│   ├── backup-YYYYMMDD.log
│   ├── monitor.log
│   └── cron.log
└── temp/                   # Thư mục tạm
```

## 🔐 Bảo mật

- Passwords được lưu trong environment variables
- Backup files được nén và có thể mã hóa
- Cloud storage sử dụng private buckets
- Logs không chứa sensitive information

## 📞 Hỗ trợ

Nếu gặp vấn đề:

1. Kiểm tra logs trong thư mục `logs/`
2. Chạy health check: `./backup-monitor.sh health`
3. Test từng component riêng biệt
4. Kiểm tra cấu hình trong `backup-config.env`

## 📈 Nâng cấp

Để nâng cấp hệ thống backup:

1. Backup cấu hình hiện tại
2. Download phiên bản mới
3. Merge cấu hình
4. Test trước khi deploy

---

**🏥 Pharmacy Management System Backup v1.0**
