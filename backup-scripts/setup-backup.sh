#!/bin/bash

# =============================================================================
# PHARMACY BACKUP SETUP SCRIPT
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🏥 === Pharmacy Management Backup Setup ==="
echo ""

# Make scripts executable
echo "📝 Making scripts executable..."
chmod +x "$SCRIPT_DIR"/*.sh

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p "$SCRIPT_DIR/logs"
mkdir -p "$SCRIPT_DIR/temp"

# Check dependencies
echo "🔍 Checking dependencies..."

check_command() {
    if ! command -v "$1" &> /dev/null; then
        echo "❌ $1 is not installed"
        return 1
    else
        echo "✅ $1 is available"
        return 0
    fi
}

MISSING_DEPS=()

check_command "pg_dump" || MISSING_DEPS+=("postgresql-client")
check_command "rclone" || MISSING_DEPS+=("rclone")
check_command "gzip" || MISSING_DEPS+=("gzip")
check_command "docker" || MISSING_DEPS+=("docker")

if [[ ${#MISSING_DEPS[@]} -gt 0 ]]; then
    echo ""
    echo "❌ Missing dependencies: ${MISSING_DEPS[*]}"
    echo "Please install them and run this script again."
    exit 1
fi

echo ""
echo "✅ All dependencies are available!"

# Configuration check
echo ""
echo "⚙️ Checking configuration..."

CONFIG_FILE="$SCRIPT_DIR/backup-config.env"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "❌ Configuration file not found: $CONFIG_FILE"
    exit 1
fi

source "$CONFIG_FILE"

# Validate required variables
REQUIRED_VARS=(
    "DB_HOST"
    "DB_PORT" 
    "DB_USER"
    "DB_PASSWORD"
    "DB_NAME"
)

MISSING_VARS=()
for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        MISSING_VARS+=("$var")
    fi
done

if [[ ${#MISSING_VARS[@]} -gt 0 ]]; then
    echo "❌ Missing required configuration variables: ${MISSING_VARS[*]}"
    echo "Please update $CONFIG_FILE"
    exit 1
fi

echo "✅ Configuration is valid!"

# Test database connection
echo ""
echo "🔌 Testing database connection..."

export PGPASSWORD="$DB_PASSWORD"
if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
    echo "✅ Database connection successful!"
else
    echo "❌ Cannot connect to database!"
    echo "Please check your database configuration."
    unset PGPASSWORD
    exit 1
fi
unset PGPASSWORD

# Setup rclone if cloud backup is enabled
echo ""
if [[ "${ENABLE_GCS_BACKUP:-false}" == "true" ]] || [[ "${ENABLE_R2_BACKUP:-false}" == "true" ]]; then
    echo "☁️ Setting up cloud storage..."
    
    if [[ ! -f ~/.config/rclone/rclone.conf ]]; then
        echo "📋 Running rclone setup..."
        "$SCRIPT_DIR/setup-rclone.sh"
    else
        echo "✅ rclone configuration already exists"
    fi
    
    # Test cloud connections
    if [[ "${ENABLE_GCS_BACKUP:-false}" == "true" ]]; then
        echo "🧪 Testing GCS connection..."
        if rclone lsd gcs: >/dev/null 2>&1; then
            echo "✅ GCS connection successful!"
            
            # Create bucket if it doesn't exist
            if ! rclone lsd gcs: | grep -q "$GCS_BUCKET_NAME"; then
                echo "📦 Creating GCS bucket: $GCS_BUCKET_NAME"
                rclone mkdir "gcs:$GCS_BUCKET_NAME" || echo "⚠️ Could not create bucket (may already exist)"
            fi
        else
            echo "⚠️ GCS connection failed - please check configuration"
        fi
    fi
    
    if [[ "${ENABLE_R2_BACKUP:-false}" == "true" ]]; then
        echo "🧪 Testing R2 connection..."
        if rclone lsd r2: >/dev/null 2>&1; then
            echo "✅ R2 connection successful!"
            
            # Create bucket if it doesn't exist
            if ! rclone lsd r2: | grep -q "$R2_BUCKET_NAME"; then
                echo "📦 Creating R2 bucket: $R2_BUCKET_NAME"
                rclone mkdir "r2:$R2_BUCKET_NAME" || echo "⚠️ Could not create bucket (may already exist)"
            fi
        else
            echo "⚠️ R2 connection failed - please check configuration"
        fi
    fi
fi

# Test backup
echo ""
echo "🧪 Running test backup..."
if "$SCRIPT_DIR/pharmacy-backup.sh" --health-check; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    exit 1
fi

# Install crontab
echo ""
read -p "📅 Do you want to install the backup schedule (crontab)? (y/n): " install_cron
if [[ "$install_cron" =~ ^[Yy]$ ]]; then
    "$SCRIPT_DIR/install-crontab.sh"
fi

# Final summary
echo ""
echo "🎉 === Backup Setup Complete! ==="
echo ""
echo "📋 Summary:"
echo "  • Backup scripts installed in: $SCRIPT_DIR"
echo "  • Database: $DB_NAME on $DB_HOST:$DB_PORT"
echo "  • GCS Backup: ${ENABLE_GCS_BACKUP:-false}"
echo "  • R2 Backup: ${ENABLE_R2_BACKUP:-false}"
echo "  • Retention: ${BACKUP_RETENTION_DAYS:-30} days"
echo ""
echo "🚀 Next Steps:"
echo "  1. Run manual backup: $SCRIPT_DIR/pharmacy-backup.sh"
echo "  2. Check logs: tail -f $SCRIPT_DIR/logs/backup-\$(date +%Y%m%d).log"
echo "  3. Monitor health: $SCRIPT_DIR/backup-monitor.sh"
echo "  4. List backups: $SCRIPT_DIR/pharmacy-restore.sh --list-gcs"
echo ""
echo "📚 Documentation:"
echo "  • Configuration: $CONFIG_FILE"
echo "  • Logs directory: $SCRIPT_DIR/logs/"
echo "  • Restore guide: $SCRIPT_DIR/pharmacy-restore.sh --help"
echo ""
echo "✅ Backup system is ready!"
