#!/bin/bash

# =============================================================================
# PHARMACY BACKUP MONITORING & NOTIFICATION SCRIPT
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"

# Load configuration
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "ERROR: Configuration file $CONFIG_FILE not found!"
    exit 1
fi

# =============================================================================
# NOTIFICATION FUNCTIONS
# =============================================================================

send_slack_notification() {
    local message="$1"
    local status="${2:-info}"

    if [[ "${ENABLE_SLACK_NOTIFICATIONS:-false}" != "true" ]] || [[ -z "${SLACK_WEBHOOK_URL:-}" ]]; then
        return 0
    fi

    local color="good"
    local emoji=":white_check_mark:"

    case "$status" in
        "error")
            color="danger"
            emoji=":x:"
            ;;
        "warning")
            color="warning"
            emoji=":warning:"
            ;;
    esac

    local payload=$(cat << EOF
{
    "attachments": [
        {
            "color": "$color",
            "title": "${emoji} Pharmacy Backup Notification",
            "text": "$message",
            "footer": "Pharmacy Management System",
            "ts": $(date +%s)
        }
    ]
}
EOF
)

    curl -X POST -H 'Content-type: application/json' \
        --data "$payload" \
        "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
}

send_email_notification() {
    local subject="$1"
    local message="$2"
    local status="${3:-info}"

    if [[ "${ENABLE_EMAIL_NOTIFICATIONS:-false}" != "true" ]]; then
        return 0
    fi

    # Simple email using mail command (requires mailutils)
    echo "$message" | mail -s "$subject" "$EMAIL_TO" 2>/dev/null || true
}

# =============================================================================
# MONITORING FUNCTIONS
# =============================================================================

check_backup_status() {
    local log_file="$SCRIPT_DIR/logs/backup-$(date +%Y%m%d).log"

    if [[ ! -f "$log_file" ]]; then
        echo "No backup log found for today"
        return 1
    fi

    if grep -q "Backup completed successfully" "$log_file"; then
        echo "Backup completed successfully"
        return 0
    elif grep -q "ERROR:" "$log_file"; then
        echo "Backup failed with errors"
        return 1
    else
        echo "Backup status unknown"
        return 1
    fi
}

check_backup_age() {
    local max_age_hours="${1:-25}" # Default 25 hours (daily backup + 1 hour buffer)

    # Check GCS backup age
    if [[ "${ENABLE_GCS_BACKUP:-false}" == "true" ]]; then
        local latest_gcs=$(rclone ls gcs:$GCS_BUCKET_NAME/pharmacy-backups/ | grep "\.sql\.gz$" | sort -k2 -r | head -1 | awk '{print $2}')
        if [[ -n "$latest_gcs" ]]; then
            # Extract timestamp from filename (assuming format: pharmacy_backup_YYYYMMDD_HHMMSS.sql.gz)
            local timestamp=$(echo "$latest_gcs" | grep -o '[0-9]\{8\}_[0-9]\{6\}' | head -1)
            if [[ -n "$timestamp" ]]; then
                local backup_date=$(echo "$timestamp" | sed 's/_/ /')
                local backup_epoch=$(date -d "$backup_date" +%s 2>/dev/null || echo "0")
                local current_epoch=$(date +%s)
                local age_hours=$(( (current_epoch - backup_epoch) / 3600 ))

                if [[ $age_hours -gt $max_age_hours ]]; then
                    echo "GCS backup is $age_hours hours old (max: $max_age_hours)"
                    return 1
                fi
            fi
        fi
    fi

    return 0
}

check_storage_space() {
    local min_free_gb="${1:-10}" # Minimum 10GB free space

    local available_gb=$(df "$SCRIPT_DIR" | tail -1 | awk '{print int($4/1024/1024)}')

    if [[ $available_gb -lt $min_free_gb ]]; then
        echo "Low disk space: ${available_gb}GB available (min: ${min_free_gb}GB)"
        return 1
    fi

    return 0
}

check_database_health() {
    # Check if database is accessible
    if ! docker exec supabase-db pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
        echo "Database is not accessible"
        return 1
    fi

    # Check database size
    local db_size=$(docker exec supabase-db psql -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT pg_size_pretty(pg_database_size('$DB_NAME'));
    " 2>/dev/null | xargs)

    echo "Database is healthy. Size: $db_size"
    return 0
}

# =============================================================================
# MAIN MONITORING
# =============================================================================

run_health_checks() {
    local errors=()
    local warnings=()
    local info=()

    echo "=== Pharmacy Backup Health Check ==="
    echo "Timestamp: $(date)"
    echo ""

    # Check backup status
    if ! backup_status=$(check_backup_status); then
        errors+=("Backup Status: $backup_status")
    else
        info+=("Backup Status: $backup_status")
    fi

    # Check backup age
    if ! backup_age_result=$(check_backup_age); then
        warnings+=("Backup Age: $backup_age_result")
    else
        info+=("Backup Age: Recent backups found")
    fi

    # Check storage space
    if ! storage_result=$(check_storage_space); then
        warnings+=("Storage: $storage_result")
    else
        info+=("Storage: Sufficient space available")
    fi

    # Check database health
    if ! db_health=$(check_database_health); then
        errors+=("Database: $db_health")
    else
        info+=("Database: $db_health")
    fi

    # Print results
    if [[ ${#info[@]} -gt 0 ]]; then
        echo "✅ INFO:"
        printf '  %s\n' "${info[@]}"
        echo ""
    fi

    if [[ ${#warnings[@]} -gt 0 ]]; then
        echo "⚠️  WARNINGS:"
        printf '  %s\n' "${warnings[@]}"
        echo ""
    fi

    if [[ ${#errors[@]} -gt 0 ]]; then
        echo "❌ ERRORS:"
        printf '  %s\n' "${errors[@]}"
        echo ""
    fi

    # Send notifications
    if [[ ${#errors[@]} -gt 0 ]]; then
        local error_msg="Pharmacy backup health check failed:\n$(printf '%s\n' "${errors[@]}")"
        send_slack_notification "$error_msg" "error"
        send_email_notification "Pharmacy Backup - Critical Issues" "$error_msg" "error"
        return 1
    elif [[ ${#warnings[@]} -gt 0 ]]; then
        local warning_msg="Pharmacy backup health check warnings:\n$(printf '%s\n' "${warnings[@]}")"
        send_slack_notification "$warning_msg" "warning"
        send_email_notification "Pharmacy Backup - Warnings" "$warning_msg" "warning"
        return 0
    else
        local success_msg="Pharmacy backup health check passed. All systems operational."
        send_slack_notification "$success_msg" "info"
        return 0
    fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

case "${1:-health}" in
    "health"|"check")
        run_health_checks
        ;;
    "test-slack")
        send_slack_notification "Test notification from Pharmacy Backup Monitor" "info"
        echo "Slack test notification sent"
        ;;
    "test-email")
        send_email_notification "Test Email" "This is a test email from Pharmacy Backup Monitor" "info"
        echo "Email test notification sent"
        ;;
    *)
        echo "Usage: $0 [health|check|test-slack|test-email]"
        exit 1
        ;;
esac
