#!/bin/bash

# =============================================================================
# PHARMACY MANAGEMENT SUPABASE BACKUP SCRIPT
# =============================================================================
# Description: Daily backup script for Supabase PostgreSQL to Google Cloud & Cloudflare R2
# Author: Pharmacy Management System
# Version: 1.0
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/backup-config.env"
LOG_FILE="$SCRIPT_DIR/logs/backup-$(date +%Y%m%d).log"

# Load configuration
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "ERROR: Configuration file $CONFIG_FILE not found!"
    exit 1
fi

# Create logs directory
mkdir -p "$SCRIPT_DIR/logs"
mkdir -p "$SCRIPT_DIR/temp"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    cleanup
    exit 1
}

# Cleanup function
cleanup() {
    log "Cleaning up temporary files..."
    rm -f "$SCRIPT_DIR/temp/"*.sql*
}

# Trap for cleanup on exit
trap cleanup EXIT

# =============================================================================
# MAIN BACKUP FUNCTION
# =============================================================================

main() {
    log "=== Starting Pharmacy Management Backup ==="
    log "Backup started at: $(date)"

    # Generate backup filename with timestamp
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILENAME="pharmacy_backup_${TIMESTAMP}.dump"
    BACKUP_PATH="$SCRIPT_DIR/temp/$BACKUP_FILENAME"

    # Step 1: Create PostgreSQL dump
    log "Step 1: Creating PostgreSQL dump..."
    create_postgres_dump "$BACKUP_PATH"

    # Step 2: Additional compression (optional)
    log "Step 2: Backup ready for upload..."

    # Step 3: Upload to Google Cloud Storage
    if [[ "${ENABLE_GCS_BACKUP:-false}" == "true" ]]; then
        log "Step 3: Uploading to Google Cloud Storage..."
        upload_to_gcs "$BACKUP_PATH" "$BACKUP_FILENAME"
    fi

    # Step 4: Upload to Cloudflare R2
    if [[ "${ENABLE_R2_BACKUP:-false}" == "true" ]]; then
        log "Step 4: Uploading to Cloudflare R2..."
        upload_to_r2 "$BACKUP_PATH" "$BACKUP_FILENAME"
    fi

    # Step 5: Cleanup old backups
    log "Step 5: Cleaning up old backups..."
    cleanup_old_backups

    log "=== Backup completed successfully ==="
    log "Backup finished at: $(date)"
}

# =============================================================================
# BACKUP FUNCTIONS
# =============================================================================

create_postgres_dump() {
    local backup_path="$1"

    log "Creating PostgreSQL dump for database: $DB_NAME"

    # Create dump using docker exec to avoid network issues
    docker exec supabase-db pg_dump \
        --username="$DB_USER" \
        --dbname="$DB_NAME" \
        --format=custom \
        --compress=9 \
        --verbose \
        --exclude-table-data='audit_logs' \
        --exclude-table-data='session_logs' \
        > "$backup_path" \
        || error_exit "Failed to create PostgreSQL dump"

    local file_size=$(du -h "$backup_path" | cut -f1)
    log "PostgreSQL dump created successfully. Size: $file_size"
}

compress_backup() {
    local source_file="$1"
    local target_file="$2"

    log "Compressing backup file..."

    if [[ "$source_file" != "$target_file" ]]; then
        gzip -9 "$source_file" || error_exit "Failed to compress backup"
        mv "${source_file}.gz" "$target_file"
    else
        log "Source and target are the same, skipping compression"
    fi

    local compressed_size=$(du -h "$target_file" | cut -f1)
    log "Backup compressed successfully. Size: $compressed_size"
}

upload_to_gcs() {
    local local_file="$1"
    local remote_filename="$2"
    local remote_path="gs://$GCS_BUCKET_NAME/pharmacy-backups/$(date +%Y/%m)/$remote_filename"

    log "Uploading to Google Cloud Storage: $remote_path"

    # Upload using rclone (configured for GCS)
    rclone copy "$local_file" "gcs:$GCS_BUCKET_NAME/pharmacy-backups/$(date +%Y/%m)/" \
        --progress \
        --stats=10s \
        || error_exit "Failed to upload to Google Cloud Storage"

    log "Successfully uploaded to Google Cloud Storage"
}

upload_to_r2() {
    local local_file="$1"
    local remote_filename="$2"
    local remote_path="pharmacy-backups/$(date +%Y/%m)/$remote_filename"

    log "Uploading to Cloudflare R2: $remote_path"

    # Upload using rclone (configured for R2)
    rclone copy "$local_file" "r2:$R2_BUCKET_NAME/pharmacy-backups/$(date +%Y/%m)/" \
        --progress \
        --stats=10s \
        || error_exit "Failed to upload to Cloudflare R2"

    log "Successfully uploaded to Cloudflare R2"
}

cleanup_old_backups() {
    local retention_days="${BACKUP_RETENTION_DAYS:-30}"

    log "Cleaning up local backups older than $retention_days days..."

    # Clean local logs
    find "$SCRIPT_DIR/logs" -name "backup-*.log" -mtime +$retention_days -delete 2>/dev/null || true

    # Clean remote backups (GCS)
    if [[ "${ENABLE_GCS_BACKUP:-false}" == "true" ]]; then
        log "Cleaning up old GCS backups..."
        # This would require more complex logic to handle date-based cleanup
        # For now, we'll rely on GCS lifecycle policies
    fi

    # Clean remote backups (R2)
    if [[ "${ENABLE_R2_BACKUP:-false}" == "true" ]]; then
        log "Cleaning up old R2 backups..."
        # This would require more complex logic to handle date-based cleanup
        # For now, we'll rely on R2 lifecycle policies
    fi

    log "Cleanup completed"
}

# =============================================================================
# HEALTH CHECK FUNCTION
# =============================================================================

health_check() {
    log "Performing health check..."

    # Check if Supabase is running
    if ! docker ps | grep "supabase-db" | grep -q "healthy"; then
        error_exit "Supabase database is not healthy"
    fi

    # Test database connection
    if ! docker exec supabase-db pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
        error_exit "Cannot connect to database"
    fi

    log "Health check passed"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

# Check if running as health check
if [[ "${1:-}" == "--health-check" ]]; then
    health_check
    exit 0
fi

# Run main backup
main
