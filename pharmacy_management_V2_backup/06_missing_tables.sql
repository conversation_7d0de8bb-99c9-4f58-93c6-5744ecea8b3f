-- =====================================================
-- PHARMACY MANAGEMENT V2 - MISSING TABLES
-- =====================================================
-- Additional tables needed for new procedures
-- =====================================================

-- Table: adjustment_types (Inventory adjustment types)
CREATE TABLE IF NOT EXISTS adjustment_types (
    adjustment_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    type_code VARCHAR(50) NOT NULL,
    type_name VARCHAR(255) NOT NULL,
    description TEXT,
    adjustment_direction VARCHAR(20) NOT NULL, -- INCREASE, DECREASE
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_adjustment_types_tenant_code UNIQUE(tenant_id, type_code),
    CONSTRAINT chk_adjustment_direction CHECK (adjustment_direction IN ('INCREASE', 'DECREASE'))
);

-- Table: inventory_adjustments (Inventory adjustment records)
CREATE TABLE IF NOT EXISTS inventory_adjustments (
    adjustment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    adjustment_number VARCHAR(100) NOT NULL,
    batch_id UUID NOT NULL REFERENCES inventory_batches(batch_id),
    adjustment_type_id UUID NOT NULL REFERENCES adjustment_types(adjustment_type_id),
    quantity_before DECIMAL(10,2) NOT NULL,
    quantity_change DECIMAL(10,2) NOT NULL,
    quantity_after DECIMAL(10,2) NOT NULL,
    reason TEXT NOT NULL,
    performed_by UUID NOT NULL REFERENCES users(user_id),
    approved_by UUID REFERENCES users(user_id),
    adjustment_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_inventory_adjustments_tenant_number UNIQUE(tenant_id, adjustment_number)
);

-- Table: return_reasons (Return reason codes)
CREATE TABLE IF NOT EXISTS return_reasons (
    return_reason_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    reason_code VARCHAR(50) NOT NULL,
    reason_name VARCHAR(255) NOT NULL,
    description TEXT,
    affects_inventory BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_return_reasons_tenant_code UNIQUE(tenant_id, reason_code)
);

-- Table: transaction_returns (Return transaction headers)
CREATE TABLE IF NOT EXISTS transaction_returns (
    return_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    return_number VARCHAR(100) NOT NULL,
    original_transaction_id UUID NOT NULL REFERENCES sales_transactions(transaction_id),
    customer_id UUID REFERENCES customers(customer_id),
    return_reason_id UUID NOT NULL REFERENCES return_reasons(return_reason_id),
    return_date DATE DEFAULT CURRENT_DATE,
    total_return_amount DECIMAL(15,2) DEFAULT 0,
    refund_amount DECIMAL(15,2) DEFAULT 0,
    refund_method VARCHAR(50), -- CASH, CARD, STORE_CREDIT
    processed_by UUID NOT NULL REFERENCES users(user_id),
    approved_by UUID REFERENCES users(user_id),
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, APPROVED, COMPLETED, CANCELLED
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_transaction_returns_tenant_number UNIQUE(tenant_id, return_number)
);

-- Table: transaction_return_items (Return transaction line items)
CREATE TABLE IF NOT EXISTS transaction_return_items (
    return_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    return_id UUID NOT NULL REFERENCES transaction_returns(return_id),
    original_transaction_item_id UUID NOT NULL REFERENCES sales_transaction_items(transaction_item_id),
    quantity_returned DECIMAL(10,2) NOT NULL,
    quantity_in_base_units DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (quantity_returned * unit_price) STORED,
    return_to_inventory BOOLEAN DEFAULT TRUE,
    condition_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Table: drug_interactions (Drug interaction database)
CREATE TABLE IF NOT EXISTS drug_interactions (
    interaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    medicine1_id UUID NOT NULL REFERENCES medicines(medicine_id),
    medicine2_id UUID NOT NULL REFERENCES medicines(medicine_id),
    interaction_type VARCHAR(50) NOT NULL, -- MAJOR, MODERATE, MINOR
    severity VARCHAR(20) NOT NULL, -- HIGH, MEDIUM, LOW
    description TEXT NOT NULL,
    clinical_effect TEXT,
    management_recommendation TEXT,
    evidence_level VARCHAR(20), -- ESTABLISHED, PROBABLE, THEORETICAL
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_drug_interactions_pair UNIQUE(medicine1_id, medicine2_id),
    CONSTRAINT chk_drug_interactions_different CHECK (medicine1_id != medicine2_id),
    CONSTRAINT chk_interaction_type CHECK (interaction_type IN ('MAJOR', 'MODERATE', 'MINOR')),
    CONSTRAINT chk_severity CHECK (severity IN ('HIGH', 'MEDIUM', 'LOW'))
);

-- Table: insurance_plans (Insurance plan definitions)
CREATE TABLE IF NOT EXISTS insurance_plans (
    insurance_plan_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_code VARCHAR(50) NOT NULL UNIQUE,
    plan_name VARCHAR(255) NOT NULL,
    provider_name VARCHAR(255) NOT NULL,
    coverage_percentage DECIMAL(5,2) DEFAULT 0,
    max_coverage_amount DECIMAL(15,2),
    deductible_amount DECIMAL(15,2) DEFAULT 0,
    copay_amount DECIMAL(15,2) DEFAULT 0,
    plan_type VARCHAR(50), -- GOVERNMENT, PRIVATE, CORPORATE
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Table: customer_insurance (Customer insurance information)
CREATE TABLE IF NOT EXISTS customer_insurance (
    customer_insurance_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id),
    insurance_plan_id UUID NOT NULL REFERENCES insurance_plans(insurance_plan_id),
    policy_number VARCHAR(100) NOT NULL,
    group_number VARCHAR(100),
    member_id VARCHAR(100),
    effective_date DATE NOT NULL,
    expiry_date DATE,
    is_primary BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_customer_insurance_policy UNIQUE(customer_id, insurance_plan_id, policy_number)
);

-- Table: insurance_covered_medicines (Medicines covered by insurance plans)
CREATE TABLE IF NOT EXISTS insurance_covered_medicines (
    coverage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    insurance_plan_id UUID NOT NULL REFERENCES insurance_plans(insurance_plan_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    coverage_percentage DECIMAL(5,2),
    max_coverage_amount DECIMAL(15,2),
    requires_prior_authorization BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE,
    
    CONSTRAINT uk_insurance_medicine_coverage UNIQUE(insurance_plan_id, medicine_id)
);

-- Insert default adjustment types
INSERT INTO adjustment_types (tenant_id, type_code, type_name, description, adjustment_direction)
SELECT 
    t.tenant_id,
    unnest(ARRAY['DAMAGE', 'EXPIRED', 'THEFT', 'RECOUNT', 'RETURN', 'TRANSFER_IN', 'TRANSFER_OUT']),
    unnest(ARRAY['Damaged Goods', 'Expired Items', 'Theft/Loss', 'Physical Recount', 'Customer Return', 'Transfer In', 'Transfer Out']),
    unnest(ARRAY['Items damaged during handling', 'Items past expiry date', 'Items lost or stolen', 'Adjustment from physical count', 'Items returned by customer', 'Items transferred from another location', 'Items transferred to another location']),
    unnest(ARRAY['DECREASE', 'DECREASE', 'DECREASE', 'INCREASE', 'INCREASE', 'INCREASE', 'DECREASE'])
FROM tenants t WHERE t.is_active = TRUE;

-- Insert default return reasons
INSERT INTO return_reasons (tenant_id, reason_code, reason_name, description, affects_inventory)
SELECT 
    t.tenant_id,
    unnest(ARRAY['DEFECTIVE', 'WRONG_ITEM', 'CUSTOMER_CHANGE', 'EXPIRED', 'DAMAGED']),
    unnest(ARRAY['Defective Product', 'Wrong Item', 'Customer Change of Mind', 'Expired Product', 'Damaged Product']),
    unnest(ARRAY['Product has manufacturing defects', 'Wrong item was dispensed', 'Customer decided to return', 'Product expired before use', 'Product was damaged']),
    unnest(ARRAY[TRUE, TRUE, TRUE, FALSE, FALSE])
FROM tenants t WHERE t.is_active = TRUE;

-- Insert sample insurance plans
INSERT INTO insurance_plans (plan_code, plan_name, provider_name, coverage_percentage, max_coverage_amount, plan_type)
VALUES 
('BHYT', 'Bảo hiểm Y tế Xã hội', 'Bảo hiểm Xã hội Việt Nam', 80.0, 5000000, 'GOVERNMENT'),
('BHTN', 'Bảo hiểm Tai nạn', 'Bảo hiểm Xã hội Việt Nam', 100.0, 10000000, 'GOVERNMENT'),
('PRIVATE_01', 'Bảo hiểm Sức khỏe Cá nhân', 'Bảo Việt', 70.0, 20000000, 'PRIVATE');

-- Create indexes
CREATE INDEX idx_adjustment_types_tenant ON adjustment_types(tenant_id);
CREATE INDEX idx_inventory_adjustments_tenant ON inventory_adjustments(tenant_id);
CREATE INDEX idx_inventory_adjustments_batch ON inventory_adjustments(batch_id);
CREATE INDEX idx_return_reasons_tenant ON return_reasons(tenant_id);
CREATE INDEX idx_transaction_returns_tenant ON transaction_returns(tenant_id);
CREATE INDEX idx_transaction_returns_original ON transaction_returns(original_transaction_id);
CREATE INDEX idx_drug_interactions_medicine1 ON drug_interactions(medicine1_id);
CREATE INDEX idx_drug_interactions_medicine2 ON drug_interactions(medicine2_id);
CREATE INDEX idx_insurance_plans_active ON insurance_plans(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_customer_insurance_customer ON customer_insurance(customer_id);
CREATE INDEX idx_insurance_covered_medicines_plan ON insurance_covered_medicines(insurance_plan_id);
CREATE INDEX idx_insurance_covered_medicines_medicine ON insurance_covered_medicines(medicine_id);
