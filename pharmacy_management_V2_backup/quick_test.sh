#!/bin/bash

# =====================================================
# PHARMACY MANAGEMENT V2 - QUICK TEST RUNNER
# =====================================================
# Quick test runner for existing database
# =====================================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
DB_NAME="${DB_NAME:-pharmacy_management_v2_test}"
EXPECTED_TESTS=29

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_header() {
    echo ""
    print_color $CYAN "========================================"
    print_color $CYAN "$1"
    print_color $CYAN "========================================"
    echo ""
}

# Main execution
main() {
    print_header "PHARMACY MANAGEMENT V2 - QUICK TEST"
    
    print_color $BLUE "🧪 Running tests on existing database: $DB_NAME"
    print_color $BLUE "📋 Expected tests: $EXPECTED_TESTS"
    echo ""
    
    # Check if database exists
    if ! psql -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        print_color $RED "❌ Database '$DB_NAME' not found or not accessible"
        print_color $YELLOW "   Please ensure:"
        print_color $YELLOW "   1. Database exists"
        print_color $YELLOW "   2. You have access permissions"
        print_color $YELLOW "   3. PostgreSQL is running"
        echo ""
        print_color $BLUE "   To create and setup database, run:"
        print_color $BLUE "   ./run_comprehensive_tests.sh"
        exit 1
    fi
    
    print_color $GREEN "✅ Database connection successful"
    
    # Apply any missing fixes first
    print_color $YELLOW "🔧 Applying fixes and missing components..."
    
    if [ -f "06_missing_tables.sql" ]; then
        print_color $BLUE "   📄 Applying missing tables..."
        psql -d "$DB_NAME" -f "06_missing_tables.sql" > /dev/null 2>&1 || true
    fi
    
    if [ -f "test_fixes.sql" ]; then
        print_color $BLUE "   📄 Applying test fixes..."
        psql -d "$DB_NAME" -f "test_fixes.sql" > /dev/null 2>&1 || true
    fi
    
    if [ -f "06_core_procedures.sql" ]; then
        print_color $BLUE "   📄 Updating procedures..."
        psql -d "$DB_NAME" -f "06_core_procedures.sql" > /dev/null 2>&1 || true
    fi
    
    print_color $GREEN "✅ Fixes applied"
    
    # Run tests
    print_header "RUNNING TEST SUITE"
    
    print_color $YELLOW "⏳ Executing all test scenarios..."
    
    # Create temporary output file
    local temp_output="/tmp/pharmacy_test_output_$(date +%s).log"
    
    if psql -d "$DB_NAME" -f "08_test_suite.sql" > "$temp_output" 2>&1; then
        # Display output
        cat "$temp_output"
        
        # Parse results
        local total_tests=$(grep "Total Tests:" "$temp_output" | tail -1 | sed 's/.*Total Tests: \([0-9]\+\).*/\1/' || echo "0")
        local passed_tests=$(grep "Passed:" "$temp_output" | tail -1 | sed 's/.*Passed: \([0-9]\+\).*/\1/' || echo "0")
        local failed_tests=$(grep "Failed:" "$temp_output" | tail -1 | sed 's/.*Failed: \([0-9]\+\).*/\1/' || echo "0")
        local success_rate=$(grep "Success Rate:" "$temp_output" | tail -1 | sed 's/.*Success Rate: \([0-9.]\+\)%.*/\1/' || echo "0")
        
        # Results summary
        print_header "QUICK TEST RESULTS"
        
        print_color $BLUE "📊 Summary:"
        print_color $BLUE "   Total Tests: $total_tests (Expected: $EXPECTED_TESTS)"
        print_color $GREEN "   Passed: $passed_tests"
        print_color $RED "   Failed: $failed_tests"
        print_color $YELLOW "   Success Rate: $success_rate%"
        echo ""
        
        # Status
        if [ "$failed_tests" -eq 0 ] && [ "$total_tests" -eq "$EXPECTED_TESTS" ]; then
            print_color $GREEN "🎉 ALL TESTS PASSED!"
            print_color $GREEN "   System is working correctly"
            echo ""
            print_color $CYAN "✅ FEATURES VERIFIED:"
            print_color $GREEN "   • Purchase Order Management"
            print_color $GREEN "   • Prescription Processing"
            print_color $GREEN "   • Inventory Management"
            print_color $GREEN "   • Return Processing"
            print_color $GREEN "   • Daily Reporting"
            print_color $GREEN "   • Data Backup"
            print_color $GREEN "   • Drug Interaction Checking"
            print_color $GREEN "   • Insurance Processing"
            
            rm -f "$temp_output"
            exit 0
        else
            print_color $YELLOW "⚠️  Issues detected:"
            
            if [ "$failed_tests" -gt 0 ]; then
                print_color $RED "   • $failed_tests tests failed"
                echo ""
                print_color $CYAN "📋 Failed tests:"
                grep "✗.*FAILED" "$temp_output" | head -5 | while read line; do
                    print_color $RED "   $line"
                done
            fi
            
            if [ "$total_tests" -ne "$EXPECTED_TESTS" ]; then
                print_color $YELLOW "   • Test count mismatch (expected $EXPECTED_TESTS, got $total_tests)"
            fi
            
            echo ""
            print_color $BLUE "🔧 Suggested fixes:"
            print_color $BLUE "   1. Run full setup: ./run_comprehensive_tests.sh"
            print_color $BLUE "   2. Check database schema completeness"
            print_color $BLUE "   3. Verify all procedures are installed"
            
            rm -f "$temp_output"
            exit 1
        fi
    else
        print_color $RED "❌ Test execution failed"
        
        if [ -f "$temp_output" ]; then
            print_color $YELLOW "Error details:"
            tail -10 "$temp_output" | while read line; do
                print_color $RED "   $line"
            done
        fi
        
        print_color $BLUE "🔧 Try running full setup:"
        print_color $BLUE "   ./run_comprehensive_tests.sh"
        
        rm -f "$temp_output"
        exit 1
    fi
}

# Help function
show_help() {
    print_header "PHARMACY MANAGEMENT V2 - QUICK TEST HELP"
    
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -d, --db NAME  Database name (default: pharmacy_management_v2)"
    echo ""
    echo "Environment Variables:"
    echo "  DB_NAME        Database name"
    echo "  PGUSER         PostgreSQL username"
    echo "  PGPASSWORD     PostgreSQL password"
    echo "  PGHOST         PostgreSQL host"
    echo "  PGPORT         PostgreSQL port"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run with default database"
    echo "  $0 -d my_pharmacy_db         # Run with custom database"
    echo "  DB_NAME=test_db $0           # Run with environment variable"
    echo ""
    echo "This script runs tests on an existing database."
    echo "For full setup, use: ./run_comprehensive_tests.sh"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--db)
            DB_NAME="$2"
            shift 2
            ;;
        *)
            print_color $RED "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Execute main function
main "$@"
