=========================================
PHARMACY MANAGEMENT V2 - TEST EXECUTION LOG
Started: Fri May 23 04:48:31 PM UTC 2025
=========================================
2025-05-23 16:48:31 - Starting comprehensive test execution
2025-05-23 16:48:31 - Database: localhost:5432/pharmacy_management_v2_test
2025-05-23 16:48:31 - User: haint
                                                           version                                                           
-----------------------------------------------------------------------------------------------------------------------------
 PostgreSQL 15.12 (Debian 15.12-1.pgdg120+1) on aarch64-unknown-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
(1 row)

2025-05-23 16:48:31 - Database connection successful
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:41: ERROR:  relation "tenants" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:44: ERROR:  relation "idx_tenants_code" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:45: ERROR:  relation "idx_tenants_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:46: ERROR:  relation "idx_tenants_subscription" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:72: ERROR:  relation "tenant_settings" already exists
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:129: ERROR:  relation "idx_tenant_settings_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:130: ERROR:  relation "idx_tenant_settings_category" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:131: ERROR:  relation "idx_tenant_settings_key" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:132: ERROR:  relation "idx_tenant_settings_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:153: ERROR:  relation "audit_logs" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:156: ERROR:  relation "idx_audit_tenant_table" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:157: ERROR:  relation "idx_audit_record" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:158: ERROR:  relation "idx_audit_user" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:159: ERROR:  relation "idx_audit_created_at" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:181: ERROR:  relation "roles" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:213: ERROR:  relation "users" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:216: ERROR:  relation "idx_users_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:217: ERROR:  relation "idx_users_username" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:218: ERROR:  relation "idx_users_email" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:219: ERROR:  relation "idx_users_employee_code" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:220: ERROR:  relation "idx_users_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:244: ERROR:  relation "medicine_categories" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:261: ERROR:  relation "manufacturers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:270: ERROR:  relation "dosage_forms" already exists
INSERT 0 10
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:334: ERROR:  relation "medicines" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:34: ERROR:  relation "packaging_units" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:37: ERROR:  relation "idx_packaging_medicine" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:38: ERROR:  relation "idx_packaging_tenant_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:82: ERROR:  relation "suppliers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:85: ERROR:  relation "idx_suppliers_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:86: ERROR:  relation "idx_suppliers_code" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:87: ERROR:  relation "idx_suppliers_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:88: ERROR:  relation "idx_suppliers_preferred" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:127: ERROR:  relation "purchase_orders" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:159: ERROR:  relation "purchase_order_items" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:185: ERROR:  relation "goods_receipts" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:215: ERROR:  relation "goods_receipt_items" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:269: ERROR:  relation "inventory_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:272: ERROR:  relation "idx_batches_tenant_medicine" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:273: ERROR:  relation "idx_batches_expiry" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:274: ERROR:  relation "idx_batches_batch_number" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:275: ERROR:  relation "idx_batches_supplier" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:276: ERROR:  relation "idx_batches_quality" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:278: ERROR:  relation "idx_batches_available" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:300: ERROR:  relation "adjustment_types" already exists
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:342: ERROR:  relation "inventory_adjustments" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:345: ERROR:  relation "idx_adjustments_tenant_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:346: ERROR:  relation "idx_adjustments_batch" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:347: ERROR:  relation "idx_adjustments_type" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/02_inventory_schema.sql:348: ERROR:  relation "idx_adjustments_performed_by" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:30: ERROR:  relation "customer_types" already exists
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:70: ERROR:  relation "loyalty_tiers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:125: ERROR:  relation "customers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:128: ERROR:  relation "idx_customers_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:129: ERROR:  relation "idx_customers_code" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:130: ERROR:  relation "idx_customers_phone" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:131: ERROR:  relation "idx_customers_email" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:132: ERROR:  relation "idx_customers_name" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:133: ERROR:  relation "idx_customers_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:157: ERROR:  relation "loyalty_transactions" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:184: ERROR:  relation "price_lists" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:217: ERROR:  relation "medicine_prices" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:248: ERROR:  relation "doctors" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:287: ERROR:  relation "prescriptions" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:312: ERROR:  relation "prescription_items" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/03_customer_sales_schema.sql:338: ERROR:  relation "payment_methods" already exists
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:76: ERROR:  relation "sales_transactions" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:79: ERROR:  relation "idx_sales_transactions_tenant_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:80: ERROR:  relation "idx_sales_transactions_customer" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:81: ERROR:  relation "idx_sales_transactions_cashier" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:82: ERROR:  relation "idx_sales_transactions_status" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:83: ERROR:  relation "idx_sales_transactions_payment_status" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:84: ERROR:  relation "idx_sales_transactions_prescription" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:147: ERROR:  relation "sales_transaction_items" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:150: ERROR:  relation "idx_sales_items_transaction" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:151: ERROR:  relation "idx_sales_items_medicine" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:152: ERROR:  relation "idx_sales_items_batch" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:153: ERROR:  relation "idx_sales_items_prescription" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:196: ERROR:  relation "transaction_payments" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:199: ERROR:  relation "idx_transaction_payments_transaction" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:200: ERROR:  relation "idx_transaction_payments_method" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:201: ERROR:  relation "idx_transaction_payments_status" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:202: ERROR:  relation "idx_transaction_payments_reference" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:220: ERROR:  relation "return_reasons" already exists
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:265: ERROR:  relation "transaction_returns" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:296: ERROR:  relation "transaction_return_items" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:340: ERROR:  relation "daily_sales_summary" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:368: ERROR:  relation "medicine_sales_summary" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:371: ERROR:  relation "idx_daily_summary_tenant_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:372: ERROR:  relation "idx_medicine_summary_tenant_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/04_sales_transactions_schema.sql:373: ERROR:  relation "idx_medicine_summary_medicine_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:12: ERROR:  relation "idx_medicines_tenant_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:13: ERROR:  relation "idx_medicines_registration_number" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:14: ERROR:  relation "idx_medicines_barcode" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:15: ERROR:  relation "idx_medicines_generic_name" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:16: ERROR:  relation "idx_medicines_brand_name" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:17: ERROR:  relation "idx_medicines_category" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:18: ERROR:  relation "idx_medicines_manufacturer" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:19: ERROR:  relation "idx_medicines_prescription_required" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:22: ERROR:  relation "idx_medicines_name_gin" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:23: ERROR:  relation "idx_medicines_generic_gin" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:27: ERROR:  relation "idx_batches_fefo" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:29: ERROR:  relation "idx_batches_low_stock" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:31: ERROR:  relation "idx_batches_near_expiry" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:34: ERROR:  relation "idx_customers_search_name" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:35: ERROR:  relation "idx_customers_phone_partial" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:36: ERROR:  relation "idx_customers_loyalty_points" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:39: ERROR:  relation "idx_sales_date_range" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:40: ERROR:  relation "idx_sales_customer_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:41: ERROR:  relation "idx_sales_cashier_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:42: ERROR:  relation "idx_sales_amount_range" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:45: ERROR:  relation "idx_prescriptions_customer_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:46: ERROR:  relation "idx_prescriptions_doctor_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:47: ERROR:  relation "idx_prescriptions_status_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:48: ERROR:  relation "idx_prescriptions_valid_until" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:51: ERROR:  relation "idx_audit_logs_table_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:52: ERROR:  relation "idx_audit_logs_user_date" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:61: ERROR:  relation "uk_price_lists_default" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:66: ERROR:  relation "uk_packaging_default_purchase" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:70: ERROR:  relation "uk_packaging_default_sale" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:74: ERROR:  relation "uk_packaging_default_dispensing" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:80: ERROR:  relation "uk_medicine_prices_no_overlap" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:84: ERROR:  relation "uk_batches_medicine_batch_number" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:92: ERROR:  constraint "chk_medicines_strength_format" for relation "medicines" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:95: ERROR:  constraint "chk_customers_phone_format" for relation "customers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:98: ERROR:  constraint "chk_customers_email_format" for relation "customers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:101: ERROR:  constraint "chk_users_username_format" for relation "users" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:104: ERROR:  constraint "chk_users_password_strength" for relation "users" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:108: ERROR:  constraint "chk_batches_expiry_reasonable" for relation "inventory_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:112: ERROR:  constraint "chk_batches_manufacturing_date" for relation "inventory_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:116: ERROR:  constraint "chk_prescriptions_valid_until_reasonable" for relation "prescriptions" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:124: ERROR:  constraint "fk_users_tenant" for relation "users" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:127: ERROR:  constraint "fk_users_role" for relation "users" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:130: ERROR:  constraint "fk_medicines_tenant" for relation "medicines" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:133: ERROR:  constraint "fk_batches_supplier" for relation "inventory_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:137: ERROR:  constraint "fk_batches_medicine_restrict" for relation "inventory_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:141: ERROR:  constraint "chk_customers_delete_balance" for relation "customers" already exists
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:158: ERROR:  trigger "trg_tenants_updated_at" for relation "tenants" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:161: ERROR:  trigger "trg_users_updated_at" for relation "users" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:164: ERROR:  trigger "trg_medicines_updated_at" for relation "medicines" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:167: ERROR:  trigger "trg_customers_updated_at" for relation "customers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:170: ERROR:  trigger "trg_suppliers_updated_at" for relation "suppliers" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:173: ERROR:  trigger "trg_inventory_batches_updated_at" for relation "inventory_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:176: ERROR:  trigger "trg_sales_transactions_updated_at" for relation "sales_transactions" already exists
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:203: ERROR:  trigger "trg_inventory_batches_check_negative" for relation "inventory_batches" already exists
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:253: ERROR:  trigger "trg_sales_transactions_loyalty_points" for relation "sales_transactions" already exists
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/05_indexes_constraints.sql:294: ERROR:  trigger "trg_sales_transaction_items_inventory" for relation "sales_transaction_items" already exists
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:85: ERROR:  relation "v_medicine_details" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:103: ERROR:  relation "v_low_stock_medicines" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:164: ERROR:  relation "v_batch_details" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:179: ERROR:  relation "v_expiring_batches" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:244: ERROR:  relation "v_customer_summary" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:307: ERROR:  relation "v_transaction_details" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:353: ERROR:  relation "v_daily_sales_report" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:457: ERROR:  relation "v_medicine_sales_performance" already exists
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:540: ERROR:  cannot change name of view column "near_expiry" to "priority_order"
HINT:  Use ALTER VIEW ... RENAME COLUMN ... to change name of view column instead.
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:655: ERROR:  function pg_catalog.extract(unknown, integer) does not exist
LINE 18:     AVG(EXTRACT(DAYS FROM (gr.receipt_date - po.order_date))...
                 ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:21: NOTICE:  relation "adjustment_types" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:40: NOTICE:  relation "inventory_adjustments" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:55: NOTICE:  relation "return_reasons" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:76: NOTICE:  relation "transaction_returns" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:91: NOTICE:  relation "transaction_return_items" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:111: NOTICE:  relation "drug_interactions" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:128: NOTICE:  relation "insurance_plans" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:145: NOTICE:  relation "customer_insurance" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:160: NOTICE:  relation "insurance_covered_medicines" already exists, skipping
CREATE TABLE
INSERT 0 0
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:187: ERROR:  duplicate key value violates unique constraint "insurance_plans_plan_code_key"
DETAIL:  Key (plan_code)=(BHYT) already exists.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:190: ERROR:  relation "idx_adjustment_types_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:191: ERROR:  relation "idx_inventory_adjustments_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:192: ERROR:  relation "idx_inventory_adjustments_batch" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:193: ERROR:  relation "idx_return_reasons_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:194: ERROR:  relation "idx_transaction_returns_tenant" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:195: ERROR:  relation "idx_transaction_returns_original" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:196: ERROR:  relation "idx_drug_interactions_medicine1" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:197: ERROR:  relation "idx_drug_interactions_medicine2" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:198: ERROR:  relation "idx_insurance_plans_active" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:199: ERROR:  relation "idx_customer_insurance_customer" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:200: ERROR:  relation "idx_insurance_covered_medicines_plan" already exists
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:201: ERROR:  relation "idx_insurance_covered_medicines_medicine" already exists
DO
CREATE FUNCTION
CREATE FUNCTION
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:109: ERROR:  relation "transaction_statuses" does not exist
LINE 1: INSERT INTO transaction_statuses (tenant_id, status_code, st...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:125: NOTICE:  relation "doctors" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:137: ERROR:  cannot insert a non-DEFAULT value into column "full_name"
DETAIL:  Column "full_name" is a generated column.
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:149: ERROR:  column "discount_percentage" of relation "customer_types" does not exist
LINE 1: ... customer_types (tenant_id, type_code, type_name, discount_p...
                                                             ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:162: ERROR:  column "min_points" of relation "loyalty_tiers" does not exist
LINE 1: ...O loyalty_tiers (tenant_id, tier_code, tier_name, min_points...
                                                             ^
DO
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:197: NOTICE:  relation "idx_purchase_orders_tenant" already exists, skipping
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:198: NOTICE:  relation "idx_purchase_order_items_po" already exists, skipping
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:199: NOTICE:  relation "idx_sales_transactions_tenant" already exists, skipping
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:200: NOTICE:  relation "idx_sales_transaction_items_transaction" already exists, skipping
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:201: NOTICE:  relation "idx_prescriptions_tenant" already exists, skipping
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:202: NOTICE:  relation "idx_prescription_items_prescription" already exists, skipping
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:204: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Test fixes applied successfully!';
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:205: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Please run the missing tables script: 06_missi...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:206: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Then re-run the test suite: 08_test_suite.sql'...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:55: NOTICE:  Existing data cleared successfully
DO
INSERT 0 5
INSERT 0 4
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11111111-1111-1111-1111-111111111111)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 2 PASSED: Users created successfully (Admin ID: 33333333-3333-3333-3333-333333333333)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 3 PASSED: Medicines created successfully (Test ID: 88888888-8888-8888-8888-888888888888)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 4: Suppliers and Customers Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 4 PASSED: Suppliers and customers setup completed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 5: Inventory Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 5 PASSED: Inventory batches created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Total available stock: 3500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 6: Data Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 6 PASSED: All test data validated successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Tenants: 1, Users: 3, Medicines: 3, Customers: 2, Suppliers: 2, Batches: 3
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 7: Basic Sales Transaction
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 7 PASSED: Sales transaction created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Transaction ID: 36f58bbd-3e3d-4879-abcb-8c66a09da659, Amount: 70000.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Remaining stock: 980.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✗ TEST 9 FAILED: Insufficient stock not properly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✗ TEST 10 FAILED: Unique constraints not enforced
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Passed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Failed: 2
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Success Rate: 80.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Basic test suite completed. Skipping advanced tests for now.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  COMPREHENSIVE TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Passed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Failed: 2
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Success Rate: 80.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
DO
2025-05-23 16:48:32 - Some tests failed
