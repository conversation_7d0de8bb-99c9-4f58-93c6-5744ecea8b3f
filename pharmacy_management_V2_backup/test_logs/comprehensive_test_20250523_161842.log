=========================================
PHARMACY MANAGEMENT V2 - TEST EXECUTION LOG
Started: Fri May 23 04:18:42 PM UTC 2025
=========================================
                                                           version                                                           
-----------------------------------------------------------------------------------------------------------------------------
 PostgreSQL 15.12 (Debian 15.12-1.pgdg120+1) on aarch64-unknown-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
(1 row)

2025-05-23 16:18:42 - Database connection successful
DROP DATABASE
2025-05-23 16:18:42 - Dropped existing database: pharmacy_management_v2_test
CREATE DATABASE
2025-05-23 16:18:42 - Created database: pharmacy_management_v2_test
CREATE EXTENSION
CREATE EXTENSION
2025-05-23 16:18:42 - Database extensions enabled
2025-05-23 16:18:42 - Executing schema file: 01_core_schema.sql
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
2025-05-23 16:18:42 - Successfully executed: 01_core_schema.sql
2025-05-23 16:18:42 - Executing schema file: 02_inventory_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:18:42 - Successfully executed: 02_inventory_schema.sql
2025-05-23 16:18:42 - Executing schema file: 03_customer_sales_schema.sql
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
2025-05-23 16:18:42 - Successfully executed: 03_customer_sales_schema.sql
2025-05-23 16:18:42 - Executing schema file: 04_sales_transactions_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:18:42 - Successfully executed: 04_sales_transactions_schema.sql
2025-05-23 16:18:42 - Executing schema file: 05_indexes_constraints.sql
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
2025-05-23 16:18:42 - Successfully executed: 05_indexes_constraints.sql
2025-05-23 16:18:42 - Executing fix file: 06_missing_tables.sql
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:21: NOTICE:  relation "adjustment_types" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:40: NOTICE:  relation "inventory_adjustments" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:55: NOTICE:  relation "return_reasons" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:76: NOTICE:  relation "transaction_returns" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:91: NOTICE:  relation "transaction_return_items" already exists, skipping
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
INSERT 0 0
INSERT 0 3
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:18:42 - Successfully executed: 06_missing_tables.sql
2025-05-23 16:18:42 - Executing fix file: test_fixes.sql
DO
CREATE FUNCTION
CREATE FUNCTION
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:109: ERROR:  relation "transaction_statuses" does not exist
LINE 1: INSERT INTO transaction_statuses (tenant_id, status_code, st...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:125: NOTICE:  relation "doctors" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:137: ERROR:  cannot insert a non-DEFAULT value into column "full_name"
DETAIL:  Column "full_name" is a generated column.
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:149: ERROR:  column "discount_percentage" of relation "customer_types" does not exist
LINE 1: ... customer_types (tenant_id, type_code, type_name, discount_p...
                                                             ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:162: ERROR:  column "min_points" of relation "loyalty_tiers" does not exist
LINE 1: ...O loyalty_tiers (tenant_id, tier_code, tier_name, min_points...
                                                             ^
DO
CREATE FUNCTION
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:204: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Test fixes applied successfully!';
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:205: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Please run the missing tables script: 06_missi...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:206: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Then re-run the test suite: 08_test_suite.sql'...
        ^
2025-05-23 16:18:42 - Successfully executed: test_fixes.sql
2025-05-23 16:18:42 - Executing procedure/view file: 06_core_procedures.sql
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
2025-05-23 16:18:42 - Successfully executed: 06_core_procedures.sql
2025-05-23 16:18:42 - Executing procedure/view file: 07_views_reporting.sql
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:540: ERROR:  cannot change name of view column "near_expiry" to "priority_order"
HINT:  Use ALTER VIEW ... RENAME COLUMN ... to change name of view column instead.
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:655: ERROR:  function pg_catalog.extract(unknown, integer) does not exist
LINE 18:     AVG(EXTRACT(DAYS FROM (gr.receipt_date - po.order_date))...
                 ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
2025-05-23 16:18:42 - Successfully executed: 07_views_reporting.sql
2025-05-23 16:18:42 - Schema deployment completed successfully
2025-05-23 16:18:42 - Starting comprehensive test execution
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11902d39-a393-4572-b206-3232fd83e3c6)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 2 PASSED: User created successfully (ID: ae8bf92b-fb26-48c2-b41e-c069900af1ce)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 3 PASSED: Medicine created successfully (ID: c26bb32b-211c-47f6-85c3-3a73f1a7eb25)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Message: Medicine created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 4: Packaging Units
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 4 PASSED: Packaging units created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 5: Supplier and Inventory
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 5 PASSED: Goods received successfully (Receipt ID: dd035a0c-801e-4b9b-b730-4892b793cad8)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Batch ID: a0f0a3af-c292-4e4c-8acf-07b1c2043f7f, Available Stock: 500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 6: Customer Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 6 PASSED: Customer created successfully (ID: 7ed429db-da67-4436-a4c3-2207e754a3a1)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 7: Sales Transaction Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 7 FAILED: Sales transaction processing failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error processing transaction: function get_next_sequence_number(uuid, unknown, unknown) is not unique
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 9 FAILED: Insufficient stock not properly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 10 PASSED: Unique constraints working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Passed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Failed: 2
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Success Rate: 80.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 11 PASSED: Price list management working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Calculated price: 1500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 12: Inventory Adjustment
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 12 PASSED: Inventory adjustment working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Stock before: 600.00, Stock after: 590.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 13: Prescription Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 13 PASSED: Prescription processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 14: Loyalty Points Calculation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 14 FAILED: Loyalty points not calculated correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Points before: 0.00, Points after: 0.00, Expected increase: 24.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 15: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Error: null value in column "original_transaction_id" of relation "transaction_returns" violates not-null constraint
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  State: 23502
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
2025-05-23 16:18:42 - Test Results - Total: 10, Passed: 8, Failed: 2, Success Rate: 80.0%
2025-05-23 16:18:42 - Warning: Test count mismatch
2025-05-23 16:18:42 - Some tests failed or test count mismatch
DROP DATABASE
2025-05-23 16:18:42 - Test execution completed in 0s
2025-05-23 16:18:42 - Final result: FAILED
