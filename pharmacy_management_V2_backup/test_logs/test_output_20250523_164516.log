psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:56: ERROR:  relation "user_sessions" does not exist
LINE 1: DELETE FROM user_sessions
                    ^
QUERY:  DELETE FROM user_sessions
CONTEXT:  PL/pgSQL function inline_code_block line 31 at SQL statement
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:64: ERROR:  duplicate key value violates unique constraint "dosage_forms_pkey"
DETAIL:  Key (dosage_form_id)=(44444444-4444-4444-4444-444444444444) already exists.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:72: ERROR:  duplicate key value violates unique constraint "manufacturers_pkey"
DETAIL:  Key (manufacturer_id)=(66666666-6666-6666-6666-666666666666) already exists.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11111111-1111-1111-1111-111111111111)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 2 PASSED: Users created successfully (Admin ID: 33333333-3333-3333-3333-333333333333)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 3 PASSED: Medicines created successfully (Test ID: 88888888-8888-8888-8888-888888888888)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 4: Suppliers and Customers Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 4 PASSED: Suppliers and customers setup completed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 5: Inventory Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 5 PASSED: Inventory batches created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:    Total available stock: 3500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 6: Data Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 6 PASSED: All test data validated successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:    Tenants: 1, Users: 3, Medicines: 3, Customers: 2, Suppliers: 2, Batches: 3
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 7: Basic Sales Transaction
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✗ TEST 7 FAILED: Sales transaction failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:    Error: cannot insert a non-DEFAULT value into column "total_amount"
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✗ TEST 9 FAILED: Insufficient stock not properly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ✗ TEST 10 FAILED: Unique constraints not enforced
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  Passed: 7
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  Failed: 3
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  Success Rate: 70.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  Error: duplicate key value violates unique constraint "uk_customer_types_tenant_code"
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  State: 23505
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1662: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
