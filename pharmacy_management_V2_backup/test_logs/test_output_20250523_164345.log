psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:55: ERROR:  relation "customer_prescriptions" does not exist
LINE 1: DELETE FROM customer_prescriptions
                    ^
QUERY:  DELETE FROM customer_prescriptions
CONTEXT:  PL/pgSQL function inline_code_block line 8 at SQL statement
INSERT 0 5
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:70: ERROR:  column "base_unit_equivalent" of relation "packaging_units" does not exist
LINE 1: ...g_units (packaging_unit_id, unit_code, unit_name, base_unit_...
                                                             ^
INSERT 0 4
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11111111-1111-1111-1111-111111111111)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ✓ TEST 2 PASSED: Users created successfully (Admin ID: 33333333-3333-3333-3333-333333333333)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ✓ TEST 3 PASSED: Medicines created successfully (Test ID: 88888888-8888-8888-8888-888888888888)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 4: Suppliers and Customers Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  -------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Error: column "minimum_points" of relation "loyalty_tiers" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  State: 42703
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
