psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:55: NOTICE:  Existing data cleared successfully
DO
INSERT 0 5
INSERT 0 4
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11111111-1111-1111-1111-111111111111)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 2 PASSED: Users created successfully (Admin ID: 33333333-3333-3333-3333-333333333333)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 3 PASSED: Medicines created successfully (Test ID: 88888888-8888-8888-8888-888888888888)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 4: Suppliers and Customers Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 4 PASSED: Suppliers and customers setup completed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 5: Inventory Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 5 PASSED: Inventory batches created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Total available stock: 3500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 6: Data Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 6 PASSED: All test data validated successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Tenants: 1, Users: 3, Medicines: 3, Customers: 2, Suppliers: 2, Batches: 3
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 7: Basic Sales Transaction
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 7 PASSED: Sales transaction created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Transaction ID: 36f58bbd-3e3d-4879-abcb-8c66a09da659, Amount: 70000.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Remaining stock: 980.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✗ TEST 9 FAILED: Insufficient stock not properly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ✗ TEST 10 FAILED: Unique constraints not enforced
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Passed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Failed: 2
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Success Rate: 80.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Basic test suite completed. Skipping advanced tests for now.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  COMPREHENSIVE TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Passed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Failed: 2
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  Success Rate: 80.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1651: NOTICE:  ========================================
DO
