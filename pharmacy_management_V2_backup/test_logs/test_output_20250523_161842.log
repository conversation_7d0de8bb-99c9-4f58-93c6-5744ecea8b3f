psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11902d39-a393-4572-b206-3232fd83e3c6)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 2 PASSED: User created successfully (ID: ae8bf92b-fb26-48c2-b41e-c069900af1ce)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 3 PASSED: Medicine created successfully (ID: c26bb32b-211c-47f6-85c3-3a73f1a7eb25)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Message: Medicine created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 4: Packaging Units
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 4 PASSED: Packaging units created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 5: Supplier and Inventory
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 5 PASSED: Goods received successfully (Receipt ID: dd035a0c-801e-4b9b-b730-4892b793cad8)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Batch ID: a0f0a3af-c292-4e4c-8acf-07b1c2043f7f, Available Stock: 500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 6: Customer Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 6 PASSED: Customer created successfully (ID: 7ed429db-da67-4436-a4c3-2207e754a3a1)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 7: Sales Transaction Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 7 FAILED: Sales transaction processing failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error processing transaction: function get_next_sequence_number(uuid, unknown, unknown) is not unique
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 9 FAILED: Insufficient stock not properly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 10 PASSED: Unique constraints working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Passed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Failed: 2
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Success Rate: 80.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 11 PASSED: Price list management working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Calculated price: 1500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 12: Inventory Adjustment
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 12 PASSED: Inventory adjustment working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Stock before: 600.00, Stock after: 590.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 13: Prescription Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 13 PASSED: Prescription processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 14: Loyalty Points Calculation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 14 FAILED: Loyalty points not calculated correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Points before: 0.00, Points after: 0.00, Expected increase: 24.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 15: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Error: null value in column "original_transaction_id" of relation "transaction_returns" violates not-null constraint
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  State: 23502
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
