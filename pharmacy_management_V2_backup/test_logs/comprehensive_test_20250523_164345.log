=========================================
PHARMACY MANAGEMENT V2 - TEST EXECUTION LOG
Started: Fri May 23 04:43:45 PM UTC 2025
=========================================
2025-05-23 16:43:45 - Starting comprehensive test execution
2025-05-23 16:43:45 - Database: localhost:5432/pharmacy_management_v2_test
2025-05-23 16:43:45 - User: haint
                                                           version                                                           
-----------------------------------------------------------------------------------------------------------------------------
 PostgreSQL 15.12 (<PERSON><PERSON> 15.12-1.pgdg120+1) on aarch64-unknown-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
(1 row)

2025-05-23 16:43:45 - Database connection successful
CREATE EXTENSION
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:540: ERROR:  cannot change name of view column "near_expiry" to "priority_order"
HINT:  Use ALTER VIEW ... RENAME COLUMN ... to change name of view column instead.
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:655: ERROR:  function pg_catalog.extract(unknown, integer) does not exist
LINE 18:     AVG(EXTRACT(DAYS FROM (gr.receipt_date - po.order_date))...
                 ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:21: NOTICE:  relation "adjustment_types" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:40: NOTICE:  relation "inventory_adjustments" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:55: NOTICE:  relation "return_reasons" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:76: NOTICE:  relation "transaction_returns" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:91: NOTICE:  relation "transaction_return_items" already exists, skipping
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
INSERT 0 0
INSERT 0 3
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
DO
CREATE FUNCTION
CREATE FUNCTION
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:109: ERROR:  relation "transaction_statuses" does not exist
LINE 1: INSERT INTO transaction_statuses (tenant_id, status_code, st...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:125: NOTICE:  relation "doctors" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:137: ERROR:  cannot insert a non-DEFAULT value into column "full_name"
DETAIL:  Column "full_name" is a generated column.
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:149: ERROR:  column "discount_percentage" of relation "customer_types" does not exist
LINE 1: ... customer_types (tenant_id, type_code, type_name, discount_p...
                                                             ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:162: ERROR:  column "min_points" of relation "loyalty_tiers" does not exist
LINE 1: ...O loyalty_tiers (tenant_id, tier_code, tier_name, min_points...
                                                             ^
DO
CREATE FUNCTION
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:204: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Test fixes applied successfully!';
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:205: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Please run the missing tables script: 06_missi...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:206: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Then re-run the test suite: 08_test_suite.sql'...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:55: ERROR:  relation "customer_prescriptions" does not exist
LINE 1: DELETE FROM customer_prescriptions
                    ^
QUERY:  DELETE FROM customer_prescriptions
CONTEXT:  PL/pgSQL function inline_code_block line 8 at SQL statement
INSERT 0 5
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:70: ERROR:  column "base_unit_equivalent" of relation "packaging_units" does not exist
LINE 1: ...g_units (packaging_unit_id, unit_code, unit_name, base_unit_...
                                                             ^
INSERT 0 4
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 11111111-1111-1111-1111-111111111111)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ✓ TEST 2 PASSED: Users created successfully (Admin ID: 33333333-3333-3333-3333-333333333333)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ✓ TEST 3 PASSED: Medicines created successfully (Test ID: 88888888-8888-8888-8888-888888888888)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 4: Suppliers and Customers Setup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  -------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Error: column "minimum_points" of relation "loyalty_tiers" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  State: 42703
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
2025-05-23 16:43:45 - Some tests failed
