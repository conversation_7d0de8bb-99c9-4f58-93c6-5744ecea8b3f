2025-05-23 16:03:04 - Starting comprehensive test execution
2025-05-23 16:03:04 - Database: localhost:5432/pharmacy_comprehensive_test
2025-05-23 16:03:04 - User: haint
NOTICE:  database "pharmacy_comprehensive_test" does not exist, skipping
DROP DATABASE
CREATE DATABASE
CREATE EXTENSION
CREATE EXTENSION
2025-05-23 16:03:04 - Executing schema file: 01_core_schema.sql
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
2025-05-23 16:03:04 - Executing schema file: 02_inventory_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:03:04 - Executing schema file: 03_customer_sales_schema.sql
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
2025-05-23 16:03:04 - Executing schema file: 04_sales_transactions_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:03:04 - Executing schema file: 05_indexes_constraints.sql
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
2025-05-23 16:03:04 - Executing schema file: 06_core_procedures.sql
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
2025-05-23 16:03:04 - Executing schema file: 07_views_reporting.sql
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:540: ERROR:  cannot change name of view column "near_expiry" to "priority_order"
HINT:  Use ALTER VIEW ... RENAME COLUMN ... to change name of view column instead.
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/07_views_reporting.sql:655: ERROR:  function pg_catalog.extract(unknown, integer) does not exist
LINE 18:     AVG(EXTRACT(DAYS FROM (gr.receipt_date - po.order_date))...
                 ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 2ff41ec4-cd19-4132-aa3a-058b92200817)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 2 PASSED: User created successfully (ID: 559c067b-8da3-463c-8b8d-ee68fc1dff42)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 3 PASSED: Medicine created successfully (ID: 6cadd4ae-e143-433a-8251-7e8c3af3ad96)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Message: Medicine created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 4: Packaging Units
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 4 PASSED: Packaging units created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 5: Supplier and Inventory
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 5 PASSED: Goods received successfully (Receipt ID: e3afb7d8-7e60-4a60-b727-c6fe5b9cab77)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Batch ID: bcba13c5-760e-4754-a6b8-52da1e5e7815, Available Stock: 500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 6: Customer Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 6 PASSED: Customer created successfully (ID: a5f9e615-8249-4402-8448-7b20523cd4c4)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 7: Sales Transaction Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 7 PASSED: Sales transaction processed successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Transaction ID: 8370f010-2d82-45ae-bce5-e2518272f586, Total: 24000
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Remaining stock after sale: 460.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ⚠ Inventory reduction may be incorrect
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 9 PASSED: Insufficient stock correctly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error message: Insufficient stock for medicine: 6cadd4ae-e143-433a-8251-7e8c3af3ad96
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 10 PASSED: Unique constraints working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Passed: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Failed: 0
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Success Rate: 100.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  🎉 ALL TESTS PASSED! System is ready for production.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 11 PASSED: Price list management working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Calculated price: 1500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 12: Inventory Adjustment
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 12 PASSED: Inventory adjustment working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Stock before: 560.00, Stock after: 550.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 13: Prescription Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 13 PASSED: Prescription processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 14: Loyalty Points Calculation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 14 PASSED: Loyalty points calculation working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Points before: 48.00, Points after: 77.50, Expected increase: 24.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 15: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 15 PASSED: Return processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Stock before return: 540.00, Stock after return: 550.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 16: Audit Trail Verification
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 16 PASSED: Audit trail working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 17: Performance Benchmark
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 17 PASSED: Performance benchmark met
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Search duration: 0.793 ms, Results: 1
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 18: Data Validation and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ----------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ✓ Negative inventory constraint working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ✓ Email format validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ✓ Manufacturing date validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ✓ Expiry date validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 18 PASSED: All data validation constraints working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 19: Zero Price Sales Policy
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ✓ Zero price sales correctly blocked when disabled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    ✓ Full discount correctly blocked when disabled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 19 FAILED: Zero price sales policy not working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 20: Purchase Order Creation & Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 20 FAILED: Purchase order creation failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error creating purchase order: column reference "purchase_order_id" is ambiguous
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 21: Purchase Order Approval Workflow
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 21 FAILED: Purchase order approval failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Purchase order not found
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 22: Prescription Sales Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 22 FAILED: Prescription sales processing failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error processing prescription sale: column reference "transaction_id" is ambiguous
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 23: Advanced Inventory Adjustments
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 23 FAILED: Inventory adjustment failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error processing inventory adjustment: column reference "adjustment_id" is ambiguous
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 24: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 24 FAILED: Return processing failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error processing return transaction: column reference "return_id" is ambiguous
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 25: Daily Reporting
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 25 FAILED: Daily reporting failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Error: Error generating daily summary: aggregate function calls cannot be nested
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 26: Data Backup
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 26 PASSED: Data backup working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Backup status: COMPLETED
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 27: Prescription Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✗ TEST 27 FAILED: Prescription validation failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Errors: ["Error validating prescription: record \"v_prescription\" has no field \"expiry_date\""]
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 28: Drug Interactions
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  --------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 28 PASSED: Drug interaction checking working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Message: No drug interactions detected
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 29: Insurance Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ✓ TEST 29 PASSED: Insurance processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:    Coverage amount: 0.00, Message: Coverage calculation error
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  COMPREHENSIVE TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Total Tests: 29
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Passed: 21
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Failed: 8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Success Rate: 72.4%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
DO
DROP DATABASE
