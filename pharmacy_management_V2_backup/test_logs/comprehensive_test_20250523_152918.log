2025-05-23 15:29:18 - Starting comprehensive test execution
2025-05-23 15:29:18 - Database: localhost:5432/pharmacy_comprehensive_test
2025-05-23 15:29:18 - User: haint
NOTICE:  database "pharmacy_comprehensive_test" does not exist, skipping
DROP DATABASE
CREATE DATABASE
CREATE EXTENSION
CREATE EXTENSION
2025-05-23 15:29:19 - Executing schema file: 01_core_schema.sql
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
2025-05-23 15:29:19 - Executing schema file: 02_inventory_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 15:29:19 - Executing schema file: 03_customer_sales_schema.sql
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
2025-05-23 15:29:19 - Executing schema file: 04_sales_transactions_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 15:29:19 - Executing schema file: 05_indexes_constraints.sql
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
2025-05-23 15:29:19 - Executing schema file: 06_core_procedures.sql
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
2025-05-23 15:29:19 - Executing schema file: 07_views_reporting.sql
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 84943b1e-9dea-4da2-a4ad-8ebd1ea2f4f3)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 2 PASSED: User created successfully (ID: b5cb6622-3b18-4cd1-9e17-6081ee7bb385)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 3 PASSED: Medicine created successfully (ID: a6e1e05e-506f-4699-8fe1-2f484a4de947)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Message: Medicine created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 4: Packaging Units
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 4 PASSED: Packaging units created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 5: Supplier and Inventory
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 5 PASSED: Goods received successfully (Receipt ID: 21e14bdc-b1fd-4540-b5d6-b10106e9826b)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Batch ID: d90c0d30-a8a5-4d58-9607-8465488bd89e, Available Stock: 500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 6: Customer Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 6 PASSED: Customer created successfully (ID: 115cba52-60b9-4e29-8c04-d491cc680644)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 7: Sales Transaction Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✗ TEST 7 FAILED: Sales transaction processing failed
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Error: Error processing transaction: column reference "transaction_id" is ambiguous
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 9 PASSED: Insufficient stock correctly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Error message: Insufficient stock for medicine: a6e1e05e-506f-4699-8fe1-2f484a4de947
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 10 PASSED: Unique constraints working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Passed: 9
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Failed: 1
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Success Rate: 90.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ⚠️  Some tests failed. Please review and fix issues before production deployment.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 11 PASSED: Price list management working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Calculated price: 1500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 12: Inventory Adjustment
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 12 PASSED: Inventory adjustment working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Stock before: 600.00, Stock after: 590.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 13: Prescription Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 13 PASSED: Prescription processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 14: Loyalty Points Calculation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✗ TEST 14 FAILED: Loyalty points not calculated correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Points before: 0.00, Points after: 0.00, Expected increase: 24.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 15: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Error: null value in column "original_transaction_id" of relation "transaction_returns" violates not-null constraint
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  State: 23502
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
DROP DATABASE
