=========================================
PHARMACY MANAGEMENT V2 - TEST EXECUTION LOG
Started: Fri May 23 04:43:21 PM UTC 2025
=========================================
2025-05-23 16:43:21 - Starting comprehensive test execution
2025-05-23 16:43:21 - Database: localhost:5432/pharmacy_management_v2_test
2025-05-23 16:43:21 - User: haint
                                                           version                                                           
-----------------------------------------------------------------------------------------------------------------------------
 PostgreSQL 15.12 (<PERSON><PERSON> 15.12-1.pgdg120+1) on aarch64-unknown-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
(1 row)

2025-05-23 16:43:21 - Database connection successful
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:21: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     adjustment_type_id UUID PRIMARY KEY DEFAULT uuid_generat...
                                                        ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:40: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     adjustment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(...
                                                   ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:55: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     return_reason_id UUID PRIMARY KEY DEFAULT uuid_generate_...
                                                      ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:76: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     return_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                               ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:91: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     return_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4...
                                                    ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:111: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     interaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4...
                                                    ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:128: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     insurance_plan_id UUID PRIMARY KEY DEFAULT uuid_generate...
                                                       ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:145: ERROR:  function uuid_generate_v4() does not exist
LINE 2: ...   customer_insurance_id UUID PRIMARY KEY DEFAULT uuid_gener...
                                                             ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:160: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     coverage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                                 ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:170: ERROR:  relation "adjustment_types" does not exist
LINE 1: INSERT INTO adjustment_types (tenant_id, type_code, type_nam...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:180: ERROR:  relation "return_reasons" does not exist
LINE 1: INSERT INTO return_reasons (tenant_id, reason_code, reason_n...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:187: ERROR:  relation "insurance_plans" does not exist
LINE 1: INSERT INTO insurance_plans (plan_code, plan_name, provider_...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:190: ERROR:  relation "adjustment_types" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:191: ERROR:  relation "inventory_adjustments" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:192: ERROR:  relation "inventory_adjustments" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:193: ERROR:  relation "return_reasons" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:194: ERROR:  relation "transaction_returns" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:195: ERROR:  relation "transaction_returns" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:196: ERROR:  relation "drug_interactions" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:197: ERROR:  relation "drug_interactions" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:198: ERROR:  relation "insurance_plans" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:199: ERROR:  relation "customer_insurance" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:200: ERROR:  relation "insurance_covered_medicines" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:201: ERROR:  relation "insurance_covered_medicines" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:15: NOTICE:  Creating missing tables...
DO
CREATE FUNCTION
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:98: ERROR:  relation "payment_methods" does not exist
LINE 1: INSERT INTO payment_methods (tenant_id, method_code, method_...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:109: ERROR:  relation "transaction_statuses" does not exist
LINE 1: INSERT INTO transaction_statuses (tenant_id, status_code, st...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:125: ERROR:  function uuid_generate_v4() does not exist
LINE 2:     doctor_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                               ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:137: ERROR:  relation "doctors" does not exist
LINE 1: INSERT INTO doctors (tenant_id, doctor_code, full_name, lice...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:149: ERROR:  relation "customer_types" does not exist
LINE 1: INSERT INTO customer_types (tenant_id, type_code, type_name,...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:162: ERROR:  relation "loyalty_tiers" does not exist
LINE 1: INSERT INTO loyalty_tiers (tenant_id, tier_code, tier_name, ...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:183: ERROR:  relation "purchase_order_items" does not exist
CONTEXT:  SQL statement "ALTER TABLE purchase_order_items ADD COLUMN line_number INTEGER DEFAULT 1"
PL/pgSQL function inline_code_block line 8 at SQL statement
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:197: ERROR:  relation "purchase_orders" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:198: ERROR:  relation "purchase_order_items" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:199: ERROR:  relation "sales_transactions" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:200: ERROR:  relation "sales_transaction_items" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:201: ERROR:  relation "prescriptions" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:202: ERROR:  relation "prescription_items" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:204: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Test fixes applied successfully!';
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:205: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Please run the missing tables script: 06_missi...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:206: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Then re-run the test suite: 08_test_suite.sql'...
        ^
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:55: ERROR:  relation "loyalty_transactions" does not exist
LINE 1: DELETE FROM loyalty_transactions
                    ^
QUERY:  DELETE FROM loyalty_transactions
CONTEXT:  PL/pgSQL function inline_code_block line 7 at SQL statement
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:63: ERROR:  relation "dosage_forms" does not exist
LINE 1: INSERT INTO dosage_forms (dosage_form_id, form_code, form_na...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:70: ERROR:  relation "packaging_units" does not exist
LINE 1: INSERT INTO packaging_units (packaging_unit_id, unit_code, u...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:76: ERROR:  relation "manufacturers" does not exist
LINE 1: INSERT INTO manufacturers (manufacturer_id, manufacturer_cod...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Error: relation "tenants" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  State: 42P01
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
2025-05-23 16:43:22 - Some tests failed
