psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:55: ERROR:  relation "loyalty_transactions" does not exist
LINE 1: DELETE FROM loyalty_transactions
                    ^
QUERY:  DELETE FROM loyalty_transactions
CONTEXT:  PL/pgSQL function inline_code_block line 7 at SQL statement
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:63: ERROR:  relation "dosage_forms" does not exist
LINE 1: INSERT INTO dosage_forms (dosage_form_id, form_code, form_na...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:70: ERROR:  relation "packaging_units" does not exist
LINE 1: INSERT INTO packaging_units (packaging_unit_id, unit_code, u...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:76: ERROR:  relation "manufacturers" does not exist
LINE 1: INSERT INTO manufacturers (manufacturer_id, manufacturer_cod...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Error: relation "tenants" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  State: 42P01
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1655: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
