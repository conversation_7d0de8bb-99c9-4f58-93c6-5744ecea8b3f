2025-05-23 15:16:39 - Starting comprehensive test execution
2025-05-23 15:16:39 - Database: localhost:5432/pharmacy_comprehensive_test
2025-05-23 15:16:39 - User: haint
NOTICE:  database "pharmacy_comprehensive_test" does not exist, skipping
DROP DATABASE
CREATE DATABASE
CREATE EXTENSION
CREATE EXTENSION
2025-05-23 15:16:39 - Executing schema file: 01_core_schema.sql
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
2025-05-23 15:16:40 - Executing schema file: 02_inventory_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 15:16:40 - Executing schema file: 03_customer_sales_schema.sql
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
2025-05-23 15:16:40 - Executing schema file: 04_sales_transactions_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 15:16:40 - Executing schema file: 05_indexes_constraints.sql
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
2025-05-23 15:16:40 - Executing schema file: 06_core_procedures.sql
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
2025-05-23 15:16:40 - Executing schema file: 07_views_reporting.sql
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 94d52916-d2c8-4de8-9263-886aae540030)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 2 PASSED: User created successfully (ID: 009db0fb-0aea-41ed-8e07-0c129db97b8a)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 3 PASSED: Medicine created successfully (ID: be64d998-37f1-4c75-9aaa-cf53651d1217)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Message: Medicine created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 4: Packaging Units
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 4 PASSED: Packaging units created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 5: Supplier and Inventory
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 5 PASSED: Goods received successfully (Receipt ID: 4f1b4fad-2f96-4855-a3a8-920a9a4f7a9f)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Batch ID: 3541fd75-8a00-45e8-8c40-8d431fcbcc31, Available Stock: 500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 6: Customer Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 6 PASSED: Customer created successfully (ID: 97cf4356-3368-467e-8c8c-43c93c0409ef)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 7: Sales Transaction Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 7 PASSED: Sales transaction processed successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Transaction ID: 658499ca-1cba-44b1-81c6-9086cefa9f14, Total: 24000
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Remaining stock after sale: 480.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Inventory correctly reduced
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 9 PASSED: Insufficient stock correctly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Error message: Insufficient stock for medicine: be64d998-37f1-4c75-9aaa-cf53651d1217
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 10 PASSED: Unique constraints working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Passed: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Failed: 0
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Success Rate: 100.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  🎉 ALL TESTS PASSED! System is ready for production.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 11 PASSED: Price list management working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Calculated price: 1500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 12: Inventory Adjustment
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 12 PASSED: Inventory adjustment working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Stock before: 580.00, Stock after: 570.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 13: Prescription Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 13 PASSED: Prescription processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 14: Loyalty Points Calculation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 14 PASSED: Loyalty points calculation working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Points before: 0.00, Points after: 24.00, Expected increase: 24.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 15: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 15 PASSED: Return processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Stock before return: 565.00, Stock after return: 575.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 16: Audit Trail Verification
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 16 PASSED: Audit trail working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 17: Performance Benchmark
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 17 PASSED: Performance benchmark met
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Search duration: 0.848 ms, Results: 1
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 18: Data Validation and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ----------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Negative inventory constraint working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Email format validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Manufacturing date validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Expiry date validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 17 PASSED: All data validation constraints working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 19: Zero Price Sales Policy
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Zero price sales correctly blocked when disabled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Full discount correctly blocked when disabled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✗ TEST 19 FAILED: Zero price sales policy not working correctly
DO
DROP DATABASE
