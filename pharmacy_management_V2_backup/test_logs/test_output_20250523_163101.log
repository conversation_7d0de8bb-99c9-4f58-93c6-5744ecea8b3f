psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  💥 CRITICAL ERROR IN TEST SUITE:
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Error: relation "tenants" does not exist
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  State: 42P01
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1566: NOTICE:  Test execution aborted. Please fix the error and retry.
DO
