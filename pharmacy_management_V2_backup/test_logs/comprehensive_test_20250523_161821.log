=========================================
PHARMACY MANAGEMENT V2 - TEST EXECUTION LOG
Started: Fri May 23 04:18:21 PM UTC 2025
=========================================
                                                           version                                                           
-----------------------------------------------------------------------------------------------------------------------------
 PostgreSQL 15.12 (Debian 15.12-1.pgdg120+1) on aarch64-unknown-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
(1 row)

2025-05-23 16:18:21 - Database connection successful
NOTICE:  database "pharmacy_management_v2_test" does not exist, skipping
DROP DATABASE
2025-05-23 16:18:21 - Dropped existing database: pharmacy_management_v2_test
CREATE DATABASE
2025-05-23 16:18:21 - Created database: pharmacy_management_v2_test
CREATE EXTENSION
CREATE EXTENSION
2025-05-23 16:18:21 - Database extensions enabled
2025-05-23 16:18:21 - Executing schema file: 01_core_schema.sql
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
2025-05-23 16:18:21 - Successfully executed: 01_core_schema.sql
2025-05-23 16:18:21 - Executing schema file: 02_inventory_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:18:21 - Successfully executed: 02_inventory_schema.sql
2025-05-23 16:18:21 - Executing schema file: 03_customer_sales_schema.sql
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
2025-05-23 16:18:21 - Successfully executed: 03_customer_sales_schema.sql
2025-05-23 16:18:21 - Executing schema file: 04_sales_transactions_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:18:21 - Successfully executed: 04_sales_transactions_schema.sql
2025-05-23 16:18:21 - Executing schema file: 05_indexes_constraints.sql
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
2025-05-23 16:18:21 - Successfully executed: 05_indexes_constraints.sql
2025-05-23 16:18:21 - Executing fix file: 06_missing_tables.sql
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:21: NOTICE:  relation "adjustment_types" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:40: NOTICE:  relation "inventory_adjustments" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:55: NOTICE:  relation "return_reasons" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:76: NOTICE:  relation "transaction_returns" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/06_missing_tables.sql:91: NOTICE:  relation "transaction_return_items" already exists, skipping
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
INSERT 0 0
INSERT 0 3
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 16:18:21 - Successfully executed: 06_missing_tables.sql
2025-05-23 16:18:21 - Executing fix file: test_fixes.sql
DO
CREATE FUNCTION
CREATE FUNCTION
INSERT 0 0
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:109: ERROR:  relation "transaction_statuses" does not exist
LINE 1: INSERT INTO transaction_statuses (tenant_id, status_code, st...
                    ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:125: NOTICE:  relation "doctors" already exists, skipping
CREATE TABLE
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:137: ERROR:  cannot insert a non-DEFAULT value into column "full_name"
DETAIL:  Column "full_name" is a generated column.
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:149: ERROR:  column "discount_percentage" of relation "customer_types" does not exist
LINE 1: ... customer_types (tenant_id, type_code, type_name, discount_p...
                                                             ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:162: ERROR:  column "min_points" of relation "loyalty_tiers" does not exist
LINE 1: ...O loyalty_tiers (tenant_id, tier_code, tier_name, min_points...
                                                             ^
DO
CREATE FUNCTION
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:204: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Test fixes applied successfully!';
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:205: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Please run the missing tables script: 06_missi...
        ^
psql:/root/pharmacy_management/pharmacy_management_V2/test_fixes.sql:206: ERROR:  syntax error at or near "RAISE"
LINE 1: RAISE NOTICE 'Then re-run the test suite: 08_test_suite.sql'...
        ^
2025-05-23 16:18:21 - Successfully executed: test_fixes.sql
2025-05-23 16:18:21 - Executing procedure/view file: 06_core_procedures.sql
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
2025-05-23 16:18:21 - Successfully executed: 06_core_procedures.sql
2025-05-23 16:18:21 - Executing procedure/view file: 07_views_reports.sql
psql: error: /root/pharmacy_management/pharmacy_management_V2/07_views_reports.sql: No such file or directory
2025-05-23 16:18:21 - Failed to execute: 07_views_reports.sql
