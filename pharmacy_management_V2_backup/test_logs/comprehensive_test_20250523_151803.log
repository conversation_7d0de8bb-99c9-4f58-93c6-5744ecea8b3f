2025-05-23 15:18:03 - Starting comprehensive test execution
2025-05-23 15:18:03 - Database: localhost:5432/pharmacy_comprehensive_test
2025-05-23 15:18:03 - User: haint
NOTICE:  database "pharmacy_comprehensive_test" does not exist, skipping
DROP DATABASE
CREATE DATABASE
CREATE EXTENSION
CREATE EXTENSION
2025-05-23 15:18:03 - Executing schema file: 01_core_schema.sql
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:13: NOTICE:  extension "uuid-ossp" already exists, skipping
CREATE EXTENSION
psql:/root/pharmacy_management/pharmacy_management_V2/01_core_schema.sql:14: NOTICE:  extension "pgcrypto" already exists, skipping
CREATE EXTENSION
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 10
CREATE TABLE
2025-05-23 15:18:03 - Executing schema file: 02_inventory_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 15:18:03 - Executing schema file: 03_customer_sales_schema.sql
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
INSERT 0 0
2025-05-23 15:18:03 - Executing schema file: 04_sales_transactions_schema.sql
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE TABLE
INSERT 0 0
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE TABLE
CREATE INDEX
CREATE INDEX
CREATE INDEX
2025-05-23 15:18:03 - Executing schema file: 05_indexes_constraints.sql
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
CREATE INDEX
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
CREATE FUNCTION
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
CREATE FUNCTION
CREATE TRIGGER
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
ALTER TABLE
2025-05-23 15:18:03 - Executing schema file: 06_core_procedures.sql
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
CREATE FUNCTION
2025-05-23 15:18:03 - Executing schema file: 07_views_reporting.sql
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
CREATE VIEW
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  PHARMACY MANAGEMENT V2 - TEST SUITE
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 1: Tenant Setup and Validation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 1 PASSED: Tenant created successfully (ID: 09c3af7a-4d72-4617-a036-99565e9e8d42)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 2: User Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 2 PASSED: User created successfully (ID: aecb98f7-334f-4102-a34e-298091d393c3)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 3: Medicine Creation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 3 PASSED: Medicine created successfully (ID: 756aa7e2-8ea9-4fe0-8b97-8592ea5280b8)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Message: Medicine created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 4: Packaging Units
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 4 PASSED: Packaging units created successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 5: Supplier and Inventory
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 5 PASSED: Goods received successfully (Receipt ID: 564e3f3c-ac31-4aa9-9468-99a8aef8f86c)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Batch ID: 9a63b75e-4f1f-417e-9b2f-3f055f15c053, Available Stock: 500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 6: Customer Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 6 PASSED: Customer created successfully (ID: b18c6f6c-ea84-4c5c-b51f-3d351e5079a9)
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 7: Sales Transaction Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 7 PASSED: Sales transaction processed successfully
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Transaction ID: 2dfd567a-b62d-425a-98a7-1d42d35e71c1, Total: 24000
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Remaining stock after sale: 480.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Inventory correctly reduced
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 8: Stock Validation and FEFO
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 8 PASSED: FEFO working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Selected batch with expiry: 2025-11-23
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 9: Insufficient Stock Handling
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 9 PASSED: Insufficient stock correctly handled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Error message: Insufficient stock for medicine: 756aa7e2-8ea9-4fe0-8b97-8592ea5280b8
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 10: Data Integrity and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 10 PASSED: Unique constraints working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST SUITE SUMMARY
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Total Tests: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Passed: 10
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Failed: 0
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  Success Rate: 100.0%
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  🎉 ALL TESTS PASSED! System is ready for production.
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ========================================
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 11: Price List Management
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 11 PASSED: Price list management working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Calculated price: 1500.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 12: Inventory Adjustment
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -----------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 12 PASSED: Inventory adjustment working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Stock before: 580.00, Stock after: 570.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 13: Prescription Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  --------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 13 PASSED: Prescription processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 14: Loyalty Points Calculation
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 14 PASSED: Loyalty points calculation working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Points before: 0.00, Points after: 24.00, Expected increase: 24.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 15: Return Processing
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 15 PASSED: Return processing working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Stock before return: 565.00, Stock after return: 575.00
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 16: Audit Trail Verification
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ---------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 16 PASSED: Audit trail working correctly
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 17: Performance Benchmark
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 17 PASSED: Performance benchmark met
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    Search duration: 0.884 ms, Results: 1
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 18: Data Validation and Constraints
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ----------------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Negative inventory constraint working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Email format validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Manufacturing date validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Expiry date validation working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✓ TEST 17 PASSED: All data validation constraints working
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  TEST 19: Zero Price Sales Policy
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  -------------------------------
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Zero price sales correctly blocked when disabled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:    ✓ Full discount correctly blocked when disabled
psql:/root/pharmacy_management/pharmacy_management_V2/08_test_suite.sql:1092: NOTICE:  ✗ TEST 19 FAILED: Zero price sales policy not working correctly
DO
DROP DATABASE
