-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - CUSTOMER & SALES SCHEMA
-- =====================================================
-- Enhanced customer management and sales processing
-- with flexible pricing and loyalty systems
-- =====================================================

-- =====================================================
-- CUSTOMER MANAGEMENT
-- =====================================================

-- Table: customer_types (Customer classification)
CREATE TABLE customer_types (
    customer_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    type_code VARCHAR(20) NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_discount_percentage DECIMAL(5,2) DEFAULT 0,
    loyalty_points_multiplier DECIMAL(3,2) DEFAULT 1.0,
    credit_limit DECIMAL(15,2) DEFAULT 0,
    payment_terms_days INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,

    CONSTRAINT uk_customer_types_tenant_code UNIQUE(tenant_id, type_code),
    CONSTRAINT chk_customer_types_discount CHECK (
        default_discount_percentage >= 0 AND default_discount_percentage <= 100
    ),
    CONSTRAINT chk_customer_types_multiplier CHECK (loyalty_points_multiplier >= 0)
);

-- Insert default customer types
INSERT INTO customer_types (tenant_id, type_code, type_name, description, default_discount_percentage, loyalty_points_multiplier)
SELECT
    t.tenant_id,
    unnest(ARRAY['REGULAR', 'VIP', 'WHOLESALE', 'INSURANCE', 'EMPLOYEE']),
    unnest(ARRAY['Regular Customer', 'VIP Customer', 'Wholesale Customer', 'Insurance Customer', 'Employee']),
    unnest(ARRAY['Standard retail customer', 'High-value customer', 'Bulk purchase customer', 'Insurance covered customer', 'Company employee']),
    unnest(ARRAY[0, 5, 10, 0, 15]),
    unnest(ARRAY[1.0, 1.5, 0.5, 1.0, 1.2])
FROM tenants t WHERE t.is_active = TRUE;

-- =====================================================
-- LOYALTY PROGRAM
-- =====================================================

-- Table: loyalty_tiers (Enhanced loyalty tiers)
CREATE TABLE loyalty_tiers (
    tier_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    tier_code VARCHAR(20) NOT NULL,
    tier_name VARCHAR(100) NOT NULL,
    min_points_required DECIMAL(10,2) NOT NULL,
    max_points_limit DECIMAL(10,2),
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    points_multiplier DECIMAL(3,2) DEFAULT 1.0,
    special_benefits JSONB DEFAULT '[]',
    tier_color VARCHAR(20), -- For UI display
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,

    CONSTRAINT uk_loyalty_tiers_tenant_code UNIQUE(tenant_id, tier_code),
    CONSTRAINT chk_loyalty_tiers_points CHECK (
        min_points_required >= 0 AND
        (max_points_limit IS NULL OR max_points_limit > min_points_required)
    ),
    CONSTRAINT chk_loyalty_tiers_discount CHECK (
        discount_percentage >= 0 AND discount_percentage <= 100
    )
);

-- Table: customers (Enhanced customer information)
CREATE TABLE customers (
    customer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    customer_code VARCHAR(50) NOT NULL,
    customer_type_id UUID REFERENCES customer_types(customer_type_id),
    title VARCHAR(20), -- Mr, Mrs, Dr, etc.
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(255) GENERATED ALWAYS AS (
        TRIM(COALESCE(title || ' ', '') || first_name || ' ' || last_name)
    ) STORED,
    date_of_birth DATE,
    gender VARCHAR(10) CHECK (gender IN ('MALE', 'FEMALE', 'OTHER')),
    national_id VARCHAR(50), -- CMND/CCCD
    phone_number VARCHAR(20),
    email VARCHAR(255),
    address_line1 TEXT,
    address_line2 TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Vietnam',
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    allergies TEXT, -- Known drug allergies
    medical_conditions TEXT, -- Chronic conditions
    insurance_provider VARCHAR(255),
    insurance_number VARCHAR(100),
    insurance_expiry_date DATE,
    preferred_language VARCHAR(10) DEFAULT 'vi',
    communication_preferences JSONB DEFAULT '{"email": true, "sms": true, "phone": false}',
    loyalty_points DECIMAL(10,2) DEFAULT 0,
    loyalty_tier_id UUID REFERENCES loyalty_tiers(tier_id),
    total_purchases DECIMAL(15,2) DEFAULT 0,
    last_purchase_date DATE,
    registration_date DATE DEFAULT CURRENT_DATE,
    referred_by_customer_id UUID REFERENCES customers(customer_id),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_customers_tenant_code UNIQUE(tenant_id, customer_code),
    CONSTRAINT uk_customers_tenant_phone UNIQUE(tenant_id, phone_number),
    CONSTRAINT uk_customers_tenant_email UNIQUE(tenant_id, email),
    CONSTRAINT uk_customers_tenant_national_id UNIQUE(tenant_id, national_id),
    CONSTRAINT chk_customers_loyalty_points CHECK (loyalty_points >= 0),
    CONSTRAINT chk_customers_balance CHECK (current_balance >= -credit_limit)
);

-- Indexes for customers
CREATE INDEX idx_customers_tenant ON customers(tenant_id);
CREATE INDEX idx_customers_code ON customers(tenant_id, customer_code);
CREATE INDEX idx_customers_phone ON customers(phone_number);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_name ON customers(tenant_id, last_name, first_name);
CREATE INDEX idx_customers_active ON customers(tenant_id, is_active);

-- Table: loyalty_transactions (Points transaction history)
CREATE TABLE loyalty_transactions (
    loyalty_transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    customer_id UUID NOT NULL REFERENCES customers(customer_id),
    transaction_type VARCHAR(20) NOT NULL, -- EARN, REDEEM, EXPIRE, ADJUST
    points_change DECIMAL(10,2) NOT NULL,
    points_balance_before DECIMAL(10,2) NOT NULL,
    points_balance_after DECIMAL(10,2) NOT NULL,
    reference_type VARCHAR(50), -- SALE, ADJUSTMENT, EXPIRY
    reference_id UUID,
    description TEXT,
    expiry_date DATE, -- For earned points
    processed_by UUID REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_loyalty_trans_balance CHECK (
        points_balance_after = points_balance_before + points_change
    ),
    CONSTRAINT chk_loyalty_trans_type CHECK (
        transaction_type IN ('EARN', 'REDEEM', 'EXPIRE', 'ADJUST')
    )
);

-- =====================================================
-- PRICING MANAGEMENT
-- =====================================================

-- Table: price_lists (Flexible pricing system)
CREATE TABLE price_lists (
    price_list_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    price_list_code VARCHAR(50) NOT NULL,
    price_list_name VARCHAR(255) NOT NULL,
    description TEXT,
    customer_type_id UUID REFERENCES customer_types(customer_type_id),
    effective_from DATE NOT NULL DEFAULT CURRENT_DATE,
    effective_to DATE,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_price_lists_tenant_code UNIQUE(tenant_id, price_list_code),
    CONSTRAINT chk_price_lists_dates CHECK (
        effective_to IS NULL OR effective_to > effective_from
    )
);

-- Table: medicine_prices (Medicine pricing by price list)
CREATE TABLE medicine_prices (
    price_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    price_list_id UUID NOT NULL REFERENCES price_lists(price_list_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    packaging_unit_id UUID NOT NULL REFERENCES packaging_units(packaging_unit_id),
    unit_price DECIMAL(12,2) NOT NULL,
    min_quantity DECIMAL(10,2) DEFAULT 1,
    max_quantity DECIMAL(10,2),
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    effective_from DATE NOT NULL DEFAULT CURRENT_DATE,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_medicine_prices UNIQUE(tenant_id, price_list_id, medicine_id, packaging_unit_id, effective_from),
    CONSTRAINT chk_medicine_prices_unit_price CHECK (unit_price >= 0),
    CONSTRAINT chk_medicine_prices_quantities CHECK (
        min_quantity > 0 AND
        (max_quantity IS NULL OR max_quantity >= min_quantity)
    ),
    CONSTRAINT chk_medicine_prices_discount CHECK (
        discount_percentage >= 0 AND discount_percentage <= 100
    ),
    CONSTRAINT chk_medicine_prices_dates CHECK (
        effective_to IS NULL OR effective_to > effective_from
    )
);

-- =====================================================
-- PRESCRIPTION MANAGEMENT
-- =====================================================

-- Table: doctors (Doctor information)
CREATE TABLE doctors (
    doctor_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    doctor_code VARCHAR(50) NOT NULL,
    title VARCHAR(20), -- Dr, Prof, etc.
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(255) GENERATED ALWAYS AS (
        TRIM(COALESCE(title || ' ', '') || first_name || ' ' || last_name)
    ) STORED,
    license_number VARCHAR(100) NOT NULL,
    specialization VARCHAR(255),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    clinic_name VARCHAR(255),
    clinic_address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_doctors_tenant_code UNIQUE(tenant_id, doctor_code),
    CONSTRAINT uk_doctors_tenant_license UNIQUE(tenant_id, license_number)
);

-- Table: prescriptions (Enhanced prescription management)
CREATE TABLE prescriptions (
    prescription_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    prescription_number VARCHAR(100) NOT NULL,
    customer_id UUID REFERENCES customers(customer_id),
    doctor_id UUID REFERENCES doctors(doctor_id),
    prescription_date DATE NOT NULL DEFAULT CURRENT_DATE,
    valid_until DATE,
    diagnosis TEXT,
    patient_weight DECIMAL(5,2), -- For dosage calculations
    patient_age INTEGER, -- For age-specific dosing
    allergies TEXT,
    special_instructions TEXT,
    is_emergency BOOLEAN DEFAULT FALSE,
    is_repeat_prescription BOOLEAN DEFAULT FALSE,
    original_prescription_id UUID REFERENCES prescriptions(prescription_id),
    max_repeats INTEGER DEFAULT 0,
    repeats_used INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'ACTIVE', -- ACTIVE, DISPENSED, EXPIRED, CANCELLED
    dispensed_by UUID REFERENCES users(user_id),
    dispensed_at TIMESTAMPTZ,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_prescriptions_tenant_number UNIQUE(tenant_id, prescription_number),
    CONSTRAINT chk_prescriptions_repeats CHECK (
        repeats_used >= 0 AND
        repeats_used <= max_repeats AND
        max_repeats >= 0
    ),
    CONSTRAINT chk_prescriptions_dates CHECK (
        valid_until IS NULL OR valid_until >= prescription_date
    )
);

-- Table: prescription_items (Prescription line items)
CREATE TABLE prescription_items (
    prescription_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    prescription_id UUID NOT NULL REFERENCES prescriptions(prescription_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    quantity_prescribed DECIMAL(10,2) NOT NULL,
    dosage_instructions TEXT NOT NULL,
    frequency VARCHAR(100), -- "3 times daily", "Every 8 hours"
    duration_days INTEGER,
    route_of_administration VARCHAR(100), -- "Oral", "Topical", "Injection"
    special_instructions TEXT,
    quantity_dispensed DECIMAL(10,2) DEFAULT 0,
    is_substitution_allowed BOOLEAN DEFAULT TRUE,
    substituted_medicine_id UUID REFERENCES medicines(medicine_id),
    substitution_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_prescription_items_quantities CHECK (
        quantity_prescribed > 0 AND
        quantity_dispensed >= 0 AND
        quantity_dispensed <= quantity_prescribed
    )
);

-- =====================================================
-- PAYMENT METHODS
-- =====================================================

-- Table: payment_methods (Enhanced payment methods)
CREATE TABLE payment_methods (
    payment_method_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    method_code VARCHAR(20) NOT NULL,
    method_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_cash BOOLEAN DEFAULT FALSE,
    is_credit BOOLEAN DEFAULT FALSE,
    requires_reference BOOLEAN DEFAULT FALSE,
    processing_fee_percentage DECIMAL(5,2) DEFAULT 0,
    processing_fee_fixed DECIMAL(10,2) DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,

    CONSTRAINT uk_payment_methods_tenant_code UNIQUE(tenant_id, method_code),
    CONSTRAINT chk_payment_methods_fees CHECK (
        processing_fee_percentage >= 0 AND
        processing_fee_fixed >= 0
    )
);

-- Insert default payment methods
INSERT INTO payment_methods (tenant_id, method_code, method_name, is_cash, is_credit, requires_reference)
SELECT
    t.tenant_id,
    unnest(ARRAY['CASH', 'CARD', 'TRANSFER', 'CREDIT', 'INSURANCE']),
    unnest(ARRAY['Cash', 'Credit/Debit Card', 'Bank Transfer', 'Credit Account', 'Insurance']),
    unnest(ARRAY[TRUE, FALSE, FALSE, FALSE, FALSE]),
    unnest(ARRAY[FALSE, FALSE, FALSE, TRUE, FALSE]),
    unnest(ARRAY[FALSE, TRUE, TRUE, FALSE, TRUE])
FROM tenants t WHERE t.is_active = TRUE;
