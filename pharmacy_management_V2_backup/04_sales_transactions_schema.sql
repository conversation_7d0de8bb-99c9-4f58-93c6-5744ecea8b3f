-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - SALES TRANSACTIONS SCHEMA
-- =====================================================
-- Enhanced sales processing with flexible payment handling
-- and comprehensive transaction tracking
-- =====================================================

-- =====================================================
-- SALES TRANSACTIONS
-- =====================================================

-- Table: sales_transactions (Enhanced sales transaction headers)
CREATE TABLE sales_transactions (
    transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    transaction_number VARCHAR(100) NOT NULL,
    transaction_type VARCHAR(50) DEFAULT 'SALE', -- SALE, RETURN, EXCHANGE
    customer_id UUID REFERENCES customers(customer_id),
    prescription_id UUID REFERENCES prescriptions(prescription_id),
    cashier_id UUID NOT NULL REFERENCES users(user_id),
    pharmacist_id UUID REFERENCES users(user_id), -- Required for prescription sales
    transaction_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Financial amounts
    subtotal_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) GENERATED ALWAYS AS (
        subtotal_amount - discount_amount + tax_amount
    ) STORED,
    
    -- Payment information
    amount_paid DECIMAL(15,2) DEFAULT 0,
    amount_due DECIMAL(15,2) GENERATED ALWAYS AS (
        subtotal_amount - discount_amount + tax_amount - amount_paid
    ) STORED,
    change_amount DECIMAL(15,2) DEFAULT 0,
    
    -- Loyalty and points
    loyalty_points_earned DECIMAL(10,2) DEFAULT 0,
    loyalty_points_redeemed DECIMAL(10,2) DEFAULT 0,
    loyalty_discount_amount DECIMAL(15,2) DEFAULT 0,
    
    -- Transaction status and workflow
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, COMPLETED, CANCELLED, RETURNED
    payment_status VARCHAR(50) DEFAULT 'UNPAID', -- UNPAID, PARTIAL, PAID, OVERPAID
    
    -- References and notes
    reference_transaction_id UUID REFERENCES sales_transactions(transaction_id), -- For returns/exchanges
    external_reference VARCHAR(100), -- Insurance claim number, etc.
    notes TEXT,
    
    -- Metadata
    pos_terminal_id VARCHAR(50),
    shift_id UUID,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT uk_sales_transactions_tenant_number UNIQUE(tenant_id, transaction_number),
    CONSTRAINT chk_sales_amounts CHECK (
        subtotal_amount >= 0 AND
        discount_amount >= 0 AND
        discount_percentage >= 0 AND discount_percentage <= 100 AND
        tax_amount >= 0 AND
        amount_paid >= 0 AND
        change_amount >= 0 AND
        loyalty_points_earned >= 0 AND
        loyalty_points_redeemed >= 0 AND
        loyalty_discount_amount >= 0
    )
);

-- Indexes for sales_transactions
CREATE INDEX idx_sales_transactions_tenant_date ON sales_transactions(tenant_id, transaction_date);
CREATE INDEX idx_sales_transactions_customer ON sales_transactions(customer_id);
CREATE INDEX idx_sales_transactions_cashier ON sales_transactions(cashier_id);
CREATE INDEX idx_sales_transactions_status ON sales_transactions(tenant_id, status);
CREATE INDEX idx_sales_transactions_payment_status ON sales_transactions(tenant_id, payment_status);
CREATE INDEX idx_sales_transactions_prescription ON sales_transactions(prescription_id);

-- =====================================================
-- SALES TRANSACTION ITEMS
-- =====================================================

-- Table: sales_transaction_items (Enhanced transaction line items)
CREATE TABLE sales_transaction_items (
    transaction_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    transaction_id UUID NOT NULL REFERENCES sales_transactions(transaction_id),
    line_number INTEGER NOT NULL,
    
    -- Product information
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    batch_id UUID NOT NULL REFERENCES inventory_batches(batch_id),
    packaging_unit_id UUID NOT NULL REFERENCES packaging_units(packaging_unit_id),
    
    -- Quantities
    quantity_sold DECIMAL(10,2) NOT NULL,
    quantity_in_base_units DECIMAL(10,2) NOT NULL,
    quantity_returned DECIMAL(10,2) DEFAULT 0,
    
    -- Pricing
    unit_price DECIMAL(12,2) NOT NULL,
    original_unit_price DECIMAL(12,2), -- Before any discounts
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (
        (quantity_sold * unit_price) - discount_amount
    ) STORED,
    
    -- Cost information (for profit calculation)
    unit_cost DECIMAL(12,2),
    total_cost DECIMAL(15,2) GENERATED ALWAYS AS (
        quantity_sold * COALESCE(unit_cost, 0)
    ) STORED,
    
    -- Prescription reference
    prescription_item_id UUID REFERENCES prescription_items(prescription_item_id),
    
    -- Item-specific information
    expiry_date DATE, -- Copied from batch for historical record
    batch_number VARCHAR(100), -- Copied from batch for historical record
    notes TEXT,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_sales_items_transaction_line UNIQUE(transaction_id, line_number),
    CONSTRAINT chk_sales_items_quantities CHECK (
        quantity_sold > 0 AND
        quantity_in_base_units > 0 AND
        quantity_returned >= 0 AND
        quantity_returned <= quantity_sold
    ),
    CONSTRAINT chk_sales_items_pricing CHECK (
        unit_price >= 0 AND
        (original_unit_price IS NULL OR original_unit_price >= 0) AND
        discount_percentage >= 0 AND discount_percentage <= 100 AND
        discount_amount >= 0 AND
        (unit_cost IS NULL OR unit_cost >= 0)
    )
);

-- Indexes for sales_transaction_items
CREATE INDEX idx_sales_items_transaction ON sales_transaction_items(transaction_id);
CREATE INDEX idx_sales_items_medicine ON sales_transaction_items(medicine_id);
CREATE INDEX idx_sales_items_batch ON sales_transaction_items(batch_id);
CREATE INDEX idx_sales_items_prescription ON sales_transaction_items(prescription_item_id);

-- =====================================================
-- PAYMENT PROCESSING
-- =====================================================

-- Table: transaction_payments (Multiple payment methods per transaction)
CREATE TABLE transaction_payments (
    payment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    transaction_id UUID NOT NULL REFERENCES sales_transactions(transaction_id),
    payment_sequence INTEGER NOT NULL,
    payment_method_id UUID NOT NULL REFERENCES payment_methods(payment_method_id),
    
    -- Payment amounts
    payment_amount DECIMAL(15,2) NOT NULL,
    processing_fee DECIMAL(10,2) DEFAULT 0,
    net_amount DECIMAL(15,2) GENERATED ALWAYS AS (
        payment_amount - processing_fee
    ) STORED,
    
    -- Payment details
    reference_number VARCHAR(100), -- Card transaction ID, check number, etc.
    authorization_code VARCHAR(100),
    card_last_four VARCHAR(4),
    card_type VARCHAR(50),
    
    -- Status and processing
    payment_status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, APPROVED, DECLINED, CANCELLED
    processed_at TIMESTAMPTZ,
    processed_by UUID REFERENCES users(user_id),
    
    -- External system integration
    external_transaction_id VARCHAR(100),
    gateway_response JSONB,
    
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_transaction_payments_sequence UNIQUE(transaction_id, payment_sequence),
    CONSTRAINT chk_transaction_payments_amount CHECK (
        payment_amount > 0 AND processing_fee >= 0
    )
);

-- Indexes for transaction_payments
CREATE INDEX idx_transaction_payments_transaction ON transaction_payments(transaction_id);
CREATE INDEX idx_transaction_payments_method ON transaction_payments(payment_method_id);
CREATE INDEX idx_transaction_payments_status ON transaction_payments(payment_status);
CREATE INDEX idx_transaction_payments_reference ON transaction_payments(reference_number);

-- =====================================================
-- RETURNS AND EXCHANGES
-- =====================================================

-- Table: return_reasons (Standardized return reasons)
CREATE TABLE return_reasons (
    return_reason_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    reason_code VARCHAR(20) NOT NULL,
    reason_name VARCHAR(100) NOT NULL,
    description TEXT,
    requires_approval BOOLEAN DEFAULT FALSE,
    affects_inventory BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    
    CONSTRAINT uk_return_reasons_tenant_code UNIQUE(tenant_id, reason_code)
);

-- Insert default return reasons
INSERT INTO return_reasons (tenant_id, reason_code, reason_name, description, requires_approval, affects_inventory)
SELECT 
    t.tenant_id,
    unnest(ARRAY['DEFECTIVE', 'EXPIRED', 'WRONG_ITEM', 'CUSTOMER_CHANGE', 'DOCTOR_CHANGE', 'ADVERSE_REACTION']),
    unnest(ARRAY['Defective Product', 'Expired Product', 'Wrong Item Dispensed', 'Customer Changed Mind', 'Doctor Changed Prescription', 'Adverse Reaction']),
    unnest(ARRAY['Product is damaged or defective', 'Product has expired', 'Wrong medication was dispensed', 'Customer no longer needs the medication', 'Doctor changed the prescription', 'Patient had adverse reaction']),
    unnest(ARRAY[FALSE, FALSE, TRUE, FALSE, FALSE, TRUE]),
    unnest(ARRAY[FALSE, FALSE, TRUE, TRUE, TRUE, FALSE])
FROM tenants t WHERE t.is_active = TRUE;

-- Table: transaction_returns (Return processing)
CREATE TABLE transaction_returns (
    return_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    return_number VARCHAR(100) NOT NULL,
    original_transaction_id UUID NOT NULL REFERENCES sales_transactions(transaction_id),
    return_transaction_id UUID REFERENCES sales_transactions(transaction_id),
    customer_id UUID REFERENCES customers(customer_id),
    return_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    return_reason_id UUID NOT NULL REFERENCES return_reasons(return_reason_id),
    return_type VARCHAR(50) DEFAULT 'FULL', -- FULL, PARTIAL, EXCHANGE
    
    -- Financial impact
    total_return_amount DECIMAL(15,2) NOT NULL,
    refund_amount DECIMAL(15,2) DEFAULT 0,
    store_credit_amount DECIMAL(15,2) DEFAULT 0,
    
    -- Processing information
    processed_by UUID NOT NULL REFERENCES users(user_id),
    approved_by UUID REFERENCES users(user_id),
    approved_at TIMESTAMPTZ,
    
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_transaction_returns_tenant_number UNIQUE(tenant_id, return_number),
    CONSTRAINT chk_transaction_returns_amounts CHECK (
        total_return_amount >= 0 AND
        refund_amount >= 0 AND
        store_credit_amount >= 0 AND
        refund_amount + store_credit_amount <= total_return_amount
    )
);

-- Table: transaction_return_items (Return line items)
CREATE TABLE transaction_return_items (
    return_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    return_id UUID NOT NULL REFERENCES transaction_returns(return_id),
    original_transaction_item_id UUID NOT NULL REFERENCES sales_transaction_items(transaction_item_id),
    
    -- Return quantities
    quantity_returned DECIMAL(10,2) NOT NULL,
    quantity_in_base_units DECIMAL(10,2) NOT NULL,
    
    -- Financial impact
    unit_price DECIMAL(12,2) NOT NULL,
    line_return_amount DECIMAL(15,2) GENERATED ALWAYS AS (
        quantity_returned * unit_price
    ) STORED,
    
    -- Inventory impact
    return_to_inventory BOOLEAN DEFAULT TRUE,
    new_batch_id UUID REFERENCES inventory_batches(batch_id), -- If creating new batch
    
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_return_items_quantities CHECK (
        quantity_returned > 0 AND
        quantity_in_base_units > 0
    ),
    CONSTRAINT chk_return_items_pricing CHECK (unit_price >= 0)
);

-- =====================================================
-- REPORTING AND ANALYTICS TABLES
-- =====================================================

-- Table: daily_sales_summary (Pre-calculated daily summaries)
CREATE TABLE daily_sales_summary (
    summary_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    summary_date DATE NOT NULL,
    
    -- Transaction counts
    total_transactions INTEGER DEFAULT 0,
    total_items_sold INTEGER DEFAULT 0,
    total_customers INTEGER DEFAULT 0,
    
    -- Financial summaries
    gross_sales DECIMAL(15,2) DEFAULT 0,
    total_discounts DECIMAL(15,2) DEFAULT 0,
    total_tax DECIMAL(15,2) DEFAULT 0,
    net_sales DECIMAL(15,2) DEFAULT 0,
    total_cost DECIMAL(15,2) DEFAULT 0,
    gross_profit DECIMAL(15,2) DEFAULT 0,
    
    -- Payment method breakdown
    cash_sales DECIMAL(15,2) DEFAULT 0,
    card_sales DECIMAL(15,2) DEFAULT 0,
    credit_sales DECIMAL(15,2) DEFAULT 0,
    insurance_sales DECIMAL(15,2) DEFAULT 0,
    
    -- Return information
    total_returns DECIMAL(15,2) DEFAULT 0,
    return_count INTEGER DEFAULT 0,
    
    -- Loyalty program
    loyalty_points_issued DECIMAL(10,2) DEFAULT 0,
    loyalty_points_redeemed DECIMAL(10,2) DEFAULT 0,
    
    -- Processing information
    calculated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    calculated_by UUID,
    
    CONSTRAINT uk_daily_summary_tenant_date UNIQUE(tenant_id, summary_date)
);

-- Table: medicine_sales_summary (Medicine performance tracking)
CREATE TABLE medicine_sales_summary (
    summary_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    summary_date DATE NOT NULL,
    
    -- Sales metrics
    quantity_sold DECIMAL(10,2) DEFAULT 0,
    total_revenue DECIMAL(15,2) DEFAULT 0,
    total_cost DECIMAL(15,2) DEFAULT 0,
    gross_profit DECIMAL(15,2) DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    
    -- Inventory metrics
    opening_stock DECIMAL(10,2) DEFAULT 0,
    closing_stock DECIMAL(10,2) DEFAULT 0,
    stock_movement DECIMAL(10,2) DEFAULT 0,
    
    -- Performance indicators
    days_of_stock DECIMAL(5,2), -- Days of inventory remaining
    turnover_rate DECIMAL(8,4), -- Inventory turnover
    
    calculated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_medicine_summary_tenant_medicine_date UNIQUE(tenant_id, medicine_id, summary_date)
);

-- Indexes for reporting tables
CREATE INDEX idx_daily_summary_tenant_date ON daily_sales_summary(tenant_id, summary_date);
CREATE INDEX idx_medicine_summary_tenant_date ON medicine_sales_summary(tenant_id, summary_date);
CREATE INDEX idx_medicine_summary_medicine_date ON medicine_sales_summary(medicine_id, summary_date);
