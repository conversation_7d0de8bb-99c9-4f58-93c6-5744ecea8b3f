#!/bin/bash

# =====================================================
# PHARMACY MANAGEMENT SYSTEM V2 - COMPREHENSIVE TEST RUNNER
# =====================================================
# This script runs all 29 tests on existing database
# =====================================================

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
LOG_DIR="$SCRIPT_DIR/test_logs"
LOG_FILE="$LOG_DIR/comprehensive_test_$TIMESTAMP.log"

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_USER="${DB_USER:-haint}"
DB_PASSWORD="${DB_PASSWORD:-H@iDu0ng}"
DB_NAME="${DB_NAME:-pharmacy_management_v2_test}"

# Export password for psql if provided
if [ -n "$DB_PASSWORD" ]; then
    export PGPASSWORD="$DB_PASSWORD"
fi

# Test configuration
EXPECTED_TOTAL_TESTS=19

# Don't exit on error - we want to handle errors gracefully
set +e

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print headers
print_header() {
    local message=$1
    echo ""
    print_color $CYAN "========================================"
    print_color $CYAN "$message"
    print_color $CYAN "========================================"
    echo ""
}

# Function to log messages
log_message() {
    local message=$1
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $message" >> "$LOG_FILE"
}

# Function to run SQL file safely
run_sql_file() {
    local file=$1
    local description=$2

    if [ -f "$SCRIPT_DIR/$file" ]; then
        print_color $YELLOW "   📄 Running: $file"
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            -f "$SCRIPT_DIR/$file" >> "$LOG_FILE" 2>&1; then
            print_color $GREEN "      ✅ $file completed"
            return 0
        else
            print_color $YELLOW "      ⚠️  $file had issues (continuing)"
            return 1
        fi
    else
        print_color $YELLOW "   ⚠️  File not found: $file (skipping)"
        return 1
    fi
}




# Main execution
main() {
    print_header "PHARMACY MANAGEMENT SYSTEM V2 - COMPREHENSIVE TEST RUNNER"

    print_color $CYAN "🚀 Starting comprehensive test execution..."
    echo ""
    print_color $BLUE "Configuration:"
    print_color $BLUE "  Database: $DB_HOST:$DB_PORT/$DB_NAME"
    print_color $BLUE "  User: $DB_USER"
    print_color $BLUE "  Expected Tests: $EXPECTED_TOTAL_TESTS"
    echo ""

    # Create log directory
    mkdir -p "$LOG_DIR"

    # Initialize log file
    echo "=========================================" > "$LOG_FILE"
    echo "PHARMACY MANAGEMENT V2 - TEST EXECUTION LOG" >> "$LOG_FILE"
    echo "Started: $(date)" >> "$LOG_FILE"
    echo "=========================================" >> "$LOG_FILE"

    log_message "Starting comprehensive test execution"
    log_message "Database: $DB_HOST:$DB_PORT/$DB_NAME"
    log_message "User: $DB_USER"

    # Test database connection
    print_header "TESTING DATABASE CONNECTION"
    print_color $BLUE "🔌 Testing connection to PostgreSQL..."

    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "SELECT version();" >> "$LOG_FILE" 2>&1; then
        print_color $GREEN "✅ Database connection successful"
        log_message "Database connection successful"
    else
        print_color $RED "❌ Cannot connect to database"
        print_color $YELLOW "   Please check database connection and try again"
        log_message "Database connection failed"
        exit 1
    fi

    # Apply schema and setup
    print_header "SETTING UP DATABASE SCHEMA"
    print_color $BLUE "🔧 Creating database schema and setup..."

    # Apply schema files in order
    run_sql_file "01_core_schema.sql" "Core schema and tables"
    run_sql_file "02_inventory_schema.sql" "Inventory management schema"
    run_sql_file "03_customer_sales_schema.sql" "Customer and sales schema"
    run_sql_file "04_sales_transactions_schema.sql" "Sales transactions schema"
    run_sql_file "05_indexes_constraints.sql" "Indexes and constraints"
    run_sql_file "06_core_procedures.sql" "Core procedures and functions"
    run_sql_file "07_views_reporting.sql" "Views and reporting"
    run_sql_file "06_missing_tables.sql" "Missing tables and data"
    run_sql_file "test_fixes.sql" "Test environment fixes"

    print_color $GREEN "✅ Database schema and setup completed"

    # Run tests
    print_header "RUNNING COMPREHENSIVE TEST SUITE"
    print_color $BLUE "🧪 Executing all $EXPECTED_TOTAL_TESTS test scenarios..."

    # Create temporary output file
    local test_output_file="$LOG_DIR/test_output_$TIMESTAMP.log"

    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -f "$SCRIPT_DIR/08_test_suite.sql" > "$test_output_file" 2>&1; then

        # Display test output
        cat "$test_output_file"

        # Append to main log
        cat "$test_output_file" >> "$LOG_FILE"

        # Parse results
        local total_tests=$(grep "Total Tests:" "$test_output_file" | tail -1 | sed 's/.*Total Tests: \([0-9]\+\).*/\1/' || echo "0")
        local passed_tests=$(grep "Passed:" "$test_output_file" | tail -1 | sed 's/.*Passed: \([0-9]\+\).*/\1/' || echo "0")
        local failed_tests=$(grep "Failed:" "$test_output_file" | tail -1 | sed 's/.*Failed: \([0-9]\+\).*/\1/' || echo "0")
        local success_rate=$(grep "Success Rate:" "$test_output_file" | tail -1 | sed 's/.*Success Rate: \([0-9.]\+\)%.*/\1/' || echo "0")

        # Results summary
        print_header "TEST RESULTS SUMMARY"
        print_color $BLUE "📊 Results:"
        print_color $BLUE "   Total Tests: $total_tests (Expected: $EXPECTED_TOTAL_TESTS)"
        print_color $GREEN "   Passed: $passed_tests"
        print_color $RED "   Failed: $failed_tests"
        print_color $YELLOW "   Success Rate: $success_rate%"
        echo ""

        # Final status
        if [ "$failed_tests" -eq 0 ] && [ "$total_tests" -eq "$EXPECTED_TOTAL_TESTS" ]; then
            print_color $GREEN "🎉 ALL $EXPECTED_TOTAL_TESTS TESTS PASSED!"
            print_color $GREEN "   System is ready for production deployment!"
            log_message "All tests passed successfully"

            # Show features implemented
            print_color $CYAN "✅ FEATURES VERIFIED:"
            print_color $GREEN "   • Purchase Order Management"
            print_color $GREEN "   • Prescription Processing"
            print_color $GREEN "   • Inventory Management"
            print_color $GREEN "   • Return Processing"
            print_color $GREEN "   • Daily Reporting"
            print_color $GREEN "   • Data Backup"
            print_color $GREEN "   • Drug Interaction Checking"
            print_color $GREEN "   • Insurance Processing"

            exit 0
        else
            print_color $YELLOW "⚠️  Issues detected:"
            if [ "$failed_tests" -gt 0 ]; then
                print_color $RED "   - $failed_tests tests failed"
                echo ""
                print_color $CYAN "📋 Failed tests:"
                grep "✗.*FAILED" "$test_output_file" | head -5 | while read line; do
                    print_color $RED "   $line"
                done
            fi

            if [ "$total_tests" -ne "$EXPECTED_TOTAL_TESTS" ]; then
                print_color $YELLOW "   - Test count mismatch (expected $EXPECTED_TOTAL_TESTS, got $total_tests)"
            fi

            log_message "Some tests failed"
            exit 1
        fi
    else
        print_color $RED "❌ Test execution failed"
        if [ -f "$test_output_file" ]; then
            print_color $YELLOW "Error details:"
            tail -10 "$test_output_file" | while read line; do
                print_color $RED "   $line"
            done
            cat "$test_output_file" >> "$LOG_FILE"
        fi
        log_message "Test execution failed"
        exit 1
    fi
}

# Execute main function
main "$@"
