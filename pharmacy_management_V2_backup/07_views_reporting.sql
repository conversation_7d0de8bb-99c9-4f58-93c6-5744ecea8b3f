-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - VIEWS & REPORTING
-- =====================================================
-- Optimized views for common queries and reporting needs
-- =====================================================

-- =====================================================
-- MEDICINE INFORMATION VIEWS
-- =====================================================

-- View: medicine_details (Complete medicine information)
CREATE VIEW v_medicine_details AS
SELECT
    m.medicine_id,
    m.tenant_id,
    m.medicine_code,
    m.medicine_name,
    m.generic_name,
    m.brand_name,
    m.registration_number,
    m.barcode,
    m.strength,
    m.base_unit,
    m.pack_size,
    m.is_prescription_required,
    m.is_controlled_substance,
    m.vat_rate,

    -- Category information
    mc.category_name,
    mc.therapeutic_class,

    -- Manufacturer information
    mf.manufacturer_name,
    mf.country_of_origin,

    -- Dosage form information
    df.form_name as dosage_form,

    -- Current stock information
    COALESCE(stock.total_available, 0) as current_stock,
    COALESCE(stock.total_reserved, 0) as reserved_stock,
    stock.nearest_expiry_date,
    stock.oldest_batch_date,

    -- Pricing information
    COALESCE(price.min_price, 0) as min_selling_price,
    COALESCE(price.max_price, 0) as max_selling_price,
    COALESCE(price.avg_price, 0) as avg_selling_price,

    m.is_active,
    m.created_at,
    m.updated_at
FROM medicines m
LEFT JOIN medicine_categories mc ON m.category_id = mc.category_id
LEFT JOIN manufacturers mf ON m.manufacturer_id = mf.manufacturer_id
LEFT JOIN dosage_forms df ON m.dosage_form_id = df.dosage_form_id
LEFT JOIN (
    -- Stock summary subquery
    SELECT
        ib.medicine_id,
        ib.tenant_id,
        SUM(ib.quantity_available) as total_available,
        SUM(ib.quantity_reserved) as total_reserved,
        MIN(ib.expiry_date) as nearest_expiry_date,
        MIN(ib.received_date) as oldest_batch_date
    FROM inventory_batches ib
    WHERE ib.quality_status = 'GOOD'
    AND ib.expiry_date > CURRENT_DATE
    AND NOT ib.is_recalled
    GROUP BY ib.medicine_id, ib.tenant_id
) stock ON m.medicine_id = stock.medicine_id AND m.tenant_id = stock.tenant_id
LEFT JOIN (
    -- Price summary subquery
    SELECT
        ib.medicine_id,
        ib.tenant_id,
        MIN(ib.selling_price) as min_price,
        MAX(ib.selling_price) as max_price,
        AVG(ib.selling_price) as avg_price
    FROM inventory_batches ib
    WHERE ib.selling_price IS NOT NULL
    AND ib.quantity_available > 0
    GROUP BY ib.medicine_id, ib.tenant_id
) price ON m.medicine_id = price.medicine_id AND m.tenant_id = price.tenant_id;

-- View: low_stock_medicines (Medicines with low stock levels)
CREATE VIEW v_low_stock_medicines AS
SELECT
    md.*,
    CASE
        WHEN md.current_stock = 0 THEN 'OUT_OF_STOCK'
        WHEN md.current_stock <= 10 THEN 'CRITICAL'
        WHEN md.current_stock <= 50 THEN 'LOW'
        ELSE 'NORMAL'
    END as stock_status,
    CASE
        WHEN md.nearest_expiry_date <= CURRENT_DATE + INTERVAL '30 days' THEN TRUE
        ELSE FALSE
    END as near_expiry
FROM v_medicine_details md
WHERE md.is_active = TRUE
AND (md.current_stock <= 50 OR md.nearest_expiry_date <= CURRENT_DATE + INTERVAL '90 days');

-- =====================================================
-- INVENTORY VIEWS
-- =====================================================

-- View: batch_details (Complete batch information with medicine details)
CREATE VIEW v_batch_details AS
SELECT
    ib.batch_id,
    ib.tenant_id,
    ib.medicine_id,
    m.medicine_name,
    m.generic_name,
    m.medicine_code,
    m.base_unit,
    ib.batch_number,
    ib.internal_batch_code,
    ib.manufacturing_date,
    ib.expiry_date,
    ib.received_date,

    -- Quantities
    ib.quantity_received,
    ib.quantity_available,
    ib.quantity_reserved,
    ib.quantity_sold,
    ib.quantity_adjusted,

    -- Financial
    ib.unit_cost,
    ib.selling_price,
    (ib.quantity_available * ib.selling_price) as inventory_value,

    -- Status and location
    ib.quality_status,
    ib.location_code,
    ib.is_recalled,
    ib.recall_reason,

    -- Supplier information
    s.supplier_name,
    s.supplier_code,

    -- Days until expiry
    (ib.expiry_date - CURRENT_DATE) as days_until_expiry,

    -- Stock status
    CASE
        WHEN ib.expiry_date <= CURRENT_DATE THEN 'EXPIRED'
        WHEN ib.expiry_date <= CURRENT_DATE + INTERVAL '30 days' THEN 'NEAR_EXPIRY'
        WHEN ib.quantity_available = 0 THEN 'EMPTY'
        WHEN ib.is_recalled THEN 'RECALLED'
        WHEN ib.quality_status != 'GOOD' THEN 'QUALITY_ISSUE'
        ELSE 'GOOD'
    END as batch_status,

    ib.created_at,
    ib.updated_at
FROM inventory_batches ib
JOIN medicines m ON ib.medicine_id = m.medicine_id
LEFT JOIN suppliers s ON ib.supplier_id = s.supplier_id;

-- View: expiring_batches (Batches nearing expiry)
CREATE VIEW v_expiring_batches AS
SELECT
    bd.*,
    CASE
        WHEN bd.days_until_expiry <= 0 THEN 'EXPIRED'
        WHEN bd.days_until_expiry <= 30 THEN 'CRITICAL'
        WHEN bd.days_until_expiry <= 90 THEN 'WARNING'
        ELSE 'NORMAL'
    END as expiry_urgency
FROM v_batch_details bd
WHERE bd.quantity_available > 0
AND bd.days_until_expiry <= 180
ORDER BY bd.days_until_expiry ASC;

-- =====================================================
-- CUSTOMER VIEWS
-- =====================================================

-- View: customer_summary (Customer information with purchase history)
CREATE VIEW v_customer_summary AS
SELECT
    c.customer_id,
    c.tenant_id,
    c.customer_code,
    c.full_name,
    c.phone_number,
    c.email,
    c.date_of_birth,
    c.gender,
    c.loyalty_points,
    c.total_purchases,
    c.last_purchase_date,
    c.registration_date,
    c.current_balance,
    c.credit_limit,

    -- Customer type information
    ct.type_name as customer_type,
    ct.default_discount_percentage,

    -- Loyalty tier information
    lt.tier_name as loyalty_tier,
    lt.discount_percentage as tier_discount,

    -- Purchase statistics
    stats.total_transactions,
    stats.avg_transaction_amount,
    stats.last_30_days_purchases,
    stats.last_90_days_purchases,
    stats.last_year_purchases,

    -- Customer status
    CASE
        WHEN c.last_purchase_date >= CURRENT_DATE - INTERVAL '30 days' THEN 'ACTIVE'
        WHEN c.last_purchase_date >= CURRENT_DATE - INTERVAL '90 days' THEN 'RECENT'
        WHEN c.last_purchase_date >= CURRENT_DATE - INTERVAL '365 days' THEN 'INACTIVE'
        ELSE 'DORMANT'
    END as customer_status,

    c.is_active,
    c.created_at
FROM customers c
LEFT JOIN customer_types ct ON c.customer_type_id = ct.customer_type_id
LEFT JOIN loyalty_tiers lt ON c.loyalty_tier_id = lt.tier_id
LEFT JOIN (
    -- Purchase statistics subquery
    SELECT
        st.customer_id,
        st.tenant_id,
        COUNT(*) as total_transactions,
        AVG(st.total_amount) as avg_transaction_amount,
        SUM(CASE WHEN st.transaction_date >= CURRENT_DATE - INTERVAL '30 days' THEN st.total_amount ELSE 0 END) as last_30_days_purchases,
        SUM(CASE WHEN st.transaction_date >= CURRENT_DATE - INTERVAL '90 days' THEN st.total_amount ELSE 0 END) as last_90_days_purchases,
        SUM(CASE WHEN st.transaction_date >= CURRENT_DATE - INTERVAL '365 days' THEN st.total_amount ELSE 0 END) as last_year_purchases
    FROM sales_transactions st
    WHERE st.status = 'COMPLETED'
    GROUP BY st.customer_id, st.tenant_id
) stats ON c.customer_id = stats.customer_id AND c.tenant_id = stats.tenant_id;

-- =====================================================
-- SALES REPORTING VIEWS
-- =====================================================

-- View: transaction_details (Complete transaction information)
CREATE VIEW v_transaction_details AS
SELECT
    st.transaction_id,
    st.tenant_id,
    st.transaction_number,
    st.transaction_type,
    st.transaction_date,
    st.status,
    st.payment_status,

    -- Customer information
    c.customer_code,
    c.full_name as customer_name,
    c.phone_number as customer_phone,

    -- Staff information
    cashier.full_name as cashier_name,
    pharmacist.full_name as pharmacist_name,

    -- Financial totals
    st.subtotal_amount,
    st.discount_amount,
    st.tax_amount,
    st.total_amount,
    st.amount_paid,
    st.amount_due,
    st.change_amount,

    -- Loyalty information
    st.loyalty_points_earned,
    st.loyalty_points_redeemed,
    st.loyalty_discount_amount,

    -- Item counts
    items.item_count,
    items.total_quantity,

    -- Prescription information
    p.prescription_number,
    p.doctor_id,

    st.notes,
    st.created_at
FROM sales_transactions st
LEFT JOIN customers c ON st.customer_id = c.customer_id
LEFT JOIN users cashier ON st.cashier_id = cashier.user_id
LEFT JOIN users pharmacist ON st.pharmacist_id = pharmacist.user_id
LEFT JOIN prescriptions p ON st.prescription_id = p.prescription_id
LEFT JOIN (
    -- Item summary subquery
    SELECT
        sti.transaction_id,
        COUNT(*) as item_count,
        SUM(sti.quantity_sold) as total_quantity
    FROM sales_transaction_items sti
    GROUP BY sti.transaction_id
) items ON st.transaction_id = items.transaction_id;

-- View: daily_sales_report (Daily sales summary)
CREATE VIEW v_daily_sales_report AS
SELECT
    st.tenant_id,
    st.transaction_date::date as sale_date,

    -- Transaction counts
    COUNT(*) as total_transactions,
    COUNT(DISTINCT st.customer_id) as unique_customers,
    COUNT(CASE WHEN st.prescription_id IS NOT NULL THEN 1 END) as prescription_sales,
    COUNT(CASE WHEN st.prescription_id IS NULL THEN 1 END) as otc_sales,

    -- Financial summary
    SUM(st.subtotal_amount) as gross_sales,
    SUM(st.discount_amount) as total_discounts,
    SUM(st.tax_amount) as total_tax,
    SUM(st.total_amount) as net_sales,
    AVG(st.total_amount) as avg_transaction_value,

    -- Payment method breakdown
    SUM(CASE WHEN pm.is_cash THEN st.total_amount ELSE 0 END) as cash_sales,
    SUM(CASE WHEN NOT pm.is_cash AND NOT pm.is_credit THEN st.total_amount ELSE 0 END) as card_sales,
    SUM(CASE WHEN pm.is_credit THEN st.total_amount ELSE 0 END) as credit_sales,

    -- Loyalty program
    SUM(st.loyalty_points_earned) as points_issued,
    SUM(st.loyalty_points_redeemed) as points_redeemed,
    SUM(st.loyalty_discount_amount) as loyalty_discounts,

    -- Item statistics
    SUM(items.total_quantity) as total_items_sold,
    AVG(items.total_quantity) as avg_items_per_transaction

FROM sales_transactions st
LEFT JOIN transaction_payments tp ON st.transaction_id = tp.transaction_id AND tp.payment_sequence = 1
LEFT JOIN payment_methods pm ON tp.payment_method_id = pm.payment_method_id
LEFT JOIN (
    SELECT
        sti.transaction_id,
        SUM(sti.quantity_sold) as total_quantity
    FROM sales_transaction_items sti
    GROUP BY sti.transaction_id
) items ON st.transaction_id = items.transaction_id
WHERE st.status = 'COMPLETED'
GROUP BY st.tenant_id, st.transaction_date::date;

-- =====================================================
-- MEDICINE PERFORMANCE VIEWS
-- =====================================================

-- View: medicine_sales_performance (Medicine sales analytics)
CREATE VIEW v_medicine_sales_performance AS
SELECT
    m.medicine_id,
    m.tenant_id,
    m.medicine_code,
    m.medicine_name,
    m.generic_name,
    mc.category_name,

    -- Sales metrics (last 30 days)
    COALESCE(sales_30d.quantity_sold, 0) as qty_sold_30d,
    COALESCE(sales_30d.revenue, 0) as revenue_30d,
    COALESCE(sales_30d.transaction_count, 0) as transactions_30d,

    -- Sales metrics (last 90 days)
    COALESCE(sales_90d.quantity_sold, 0) as qty_sold_90d,
    COALESCE(sales_90d.revenue, 0) as revenue_90d,
    COALESCE(sales_90d.transaction_count, 0) as transactions_90d,

    -- Sales metrics (last 365 days)
    COALESCE(sales_365d.quantity_sold, 0) as qty_sold_365d,
    COALESCE(sales_365d.revenue, 0) as revenue_365d,
    COALESCE(sales_365d.transaction_count, 0) as transactions_365d,

    -- Current inventory
    COALESCE(stock.current_stock, 0) as current_stock,
    COALESCE(stock.inventory_value, 0) as inventory_value,

    -- Performance indicators
    CASE
        WHEN COALESCE(stock.current_stock, 0) = 0 THEN 0
        WHEN COALESCE(sales_30d.quantity_sold, 0) = 0 THEN 999
        ELSE ROUND(COALESCE(stock.current_stock, 0) / (COALESCE(sales_30d.quantity_sold, 0) / 30.0), 1)
    END as days_of_stock,

    CASE
        WHEN COALESCE(stock.inventory_value, 0) = 0 THEN 0
        ELSE ROUND(COALESCE(sales_30d.revenue, 0) / COALESCE(stock.inventory_value, 1) * 12, 2)
    END as annual_turnover_rate

FROM medicines m
LEFT JOIN medicine_categories mc ON m.category_id = mc.category_id
LEFT JOIN (
    -- 30-day sales
    SELECT
        sti.medicine_id,
        st.tenant_id,
        SUM(sti.quantity_sold) as quantity_sold,
        SUM(sti.line_total) as revenue,
        COUNT(DISTINCT st.transaction_id) as transaction_count
    FROM sales_transaction_items sti
    JOIN sales_transactions st ON sti.transaction_id = st.transaction_id
    WHERE st.transaction_date >= CURRENT_DATE - INTERVAL '30 days'
    AND st.status = 'COMPLETED'
    GROUP BY sti.medicine_id, st.tenant_id
) sales_30d ON m.medicine_id = sales_30d.medicine_id AND m.tenant_id = sales_30d.tenant_id
LEFT JOIN (
    -- 90-day sales
    SELECT
        sti.medicine_id,
        st.tenant_id,
        SUM(sti.quantity_sold) as quantity_sold,
        SUM(sti.line_total) as revenue,
        COUNT(DISTINCT st.transaction_id) as transaction_count
    FROM sales_transaction_items sti
    JOIN sales_transactions st ON sti.transaction_id = st.transaction_id
    WHERE st.transaction_date >= CURRENT_DATE - INTERVAL '90 days'
    AND st.status = 'COMPLETED'
    GROUP BY sti.medicine_id, st.tenant_id
) sales_90d ON m.medicine_id = sales_90d.medicine_id AND m.tenant_id = sales_90d.tenant_id
LEFT JOIN (
    -- 365-day sales
    SELECT
        sti.medicine_id,
        st.tenant_id,
        SUM(sti.quantity_sold) as quantity_sold,
        SUM(sti.line_total) as revenue,
        COUNT(DISTINCT st.transaction_id) as transaction_count
    FROM sales_transaction_items sti
    JOIN sales_transactions st ON sti.transaction_id = st.transaction_id
    WHERE st.transaction_date >= CURRENT_DATE - INTERVAL '365 days'
    AND st.status = 'COMPLETED'
    GROUP BY sti.medicine_id, st.tenant_id
) sales_365d ON m.medicine_id = sales_365d.medicine_id AND m.tenant_id = sales_365d.tenant_id
LEFT JOIN (
    -- Current stock
    SELECT
        ib.medicine_id,
        ib.tenant_id,
        SUM(ib.quantity_available) as current_stock,
        SUM(ib.quantity_available * ib.selling_price) as inventory_value
    FROM inventory_batches ib
    WHERE ib.quality_status = 'GOOD'
    AND ib.expiry_date > CURRENT_DATE
    AND NOT ib.is_recalled
    GROUP BY ib.medicine_id, ib.tenant_id
) stock ON m.medicine_id = stock.medicine_id AND m.tenant_id = stock.tenant_id
WHERE m.is_active = TRUE;

-- =====================================================
-- TENANT SETTINGS VIEWS
-- =====================================================

-- View: v_tenant_settings_summary (Settings overview by category)
CREATE OR REPLACE VIEW v_tenant_settings_summary AS
SELECT
    ts.tenant_id,
    t.tenant_name,
    ts.setting_category,
    COUNT(*) as total_settings,
    COUNT(CASE WHEN ts.is_active THEN 1 END) as active_settings,
    COUNT(CASE WHEN ts.is_system_setting THEN 1 END) as system_settings,
    COUNT(CASE WHEN NOT ts.is_system_setting THEN 1 END) as custom_settings,
    MAX(ts.updated_at) as last_updated
FROM tenant_settings ts
JOIN tenants t ON ts.tenant_id = t.tenant_id
GROUP BY ts.tenant_id, t.tenant_name, ts.setting_category
ORDER BY ts.tenant_id, ts.setting_category;

-- View: v_sales_policy_settings (Sales policy configuration)
CREATE OR REPLACE VIEW v_sales_policy_settings AS
SELECT
    ts.tenant_id,
    t.tenant_name,
    MAX(CASE WHEN ts.setting_key = 'ALLOW_ZERO_PRICE_SALES' THEN ts.setting_value END) as allow_zero_price_sales,
    MAX(CASE WHEN ts.setting_key = 'ALLOW_100_PERCENT_DISCOUNT' THEN ts.setting_value END) as allow_100_percent_discount,
    MAX(CASE WHEN ts.setting_key = 'MAX_DISCOUNT_PERCENTAGE' THEN ts.setting_value END) as max_discount_percentage,
    MAX(CASE WHEN ts.setting_key = 'REQUIRE_MANAGER_APPROVAL_ZERO_SALES' THEN ts.setting_value END) as require_manager_approval,
    MAX(CASE WHEN ts.setting_key = 'REQUIRE_REASON_ZERO_SALES' THEN ts.setting_value END) as require_reason_zero_sales,
    COUNT(*) as total_sales_settings,
    MAX(ts.updated_at) as last_policy_update
FROM tenant_settings ts
JOIN tenants t ON ts.tenant_id = t.tenant_id
WHERE ts.setting_category = 'SALES'
AND ts.is_active = TRUE
GROUP BY ts.tenant_id, t.tenant_name
ORDER BY ts.tenant_id;

-- View: v_tenant_configuration (Complete tenant configuration)
CREATE OR REPLACE VIEW v_tenant_configuration AS
SELECT
    ts.tenant_id,
    t.tenant_name,
    ts.setting_category,
    ts.setting_key,
    ts.setting_value,
    ts.setting_type,
    ts.description,
    ts.is_system_setting,
    ts.is_active,
    ts.updated_at,
    u.username as updated_by_user
FROM tenant_settings ts
JOIN tenants t ON ts.tenant_id = t.tenant_id
LEFT JOIN users u ON ts.updated_by = u.user_id
WHERE ts.is_active = TRUE
ORDER BY ts.tenant_id, ts.setting_category, ts.setting_key;
