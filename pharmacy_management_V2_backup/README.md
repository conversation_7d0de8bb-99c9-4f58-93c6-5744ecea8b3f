# PHARMACY MANAGEMENT V2 - TESTING GUIDE

## 🎯 QUICK START

**Goal:** Run all 29 tests successfully (100% pass rate)

### **Run Tests:**

```bash
# Make script executable
chmod +x run_comprehensive_tests.sh

# Run all tests
./run_comprehensive_tests.sh

# Expected result: "🎉 ALL 29 TESTS PASSED!"
```

### **Alternative Quick Test:**

```bash
# Make script executable
chmod +x quick_test.sh

# Run quick test
./quick_test.sh
```

---

## 📋 WHAT'S NEW IN V2

### **✅ 10 NEW PROCEDURES IMPLEMENTED:**
1. `create_purchase_order()` - Create purchase orders
2. `approve_purchase_order()` - Approve purchase orders
3. `process_prescription_sale()` - Process prescription sales
4. `process_inventory_adjustment()` - Adjust inventory
5. `process_return_transaction()` - Process returns
6. `generate_daily_summary()` - Generate daily reports
7. `backup_tenant_data()` - Backup data
8. `validate_prescription()` - Validate prescriptions
9. `check_drug_interactions()` - Check drug interactions
10. `calculate_insurance_coverage()` - Calculate insurance

### **✅ 10 NEW TEST SCENARIOS ADDED:**
- TEST 20: Purchase Order Creation & Validation
- TEST 21: Purchase Order Approval Workflow
- TEST 22: Prescription Sales Processing
- TEST 23: Advanced Inventory Adjustments
- TEST 24: Return Processing
- TEST 25: Daily Reporting
- TEST 26: Data Backup
- TEST 27: Prescription Validation
- TEST 28: Drug Interactions
- TEST 29: Insurance Processing

---

## 📊 EXPECTED SUCCESS OUTPUT

```
========================================
TEST RESULTS SUMMARY
========================================
📊 Results:
   Total Tests: 29 (Expected: 29)
   Passed: 29
   Failed: 0
   Success Rate: 100.0%

🎉 ALL 29 TESTS PASSED!
   System is ready for production deployment!

✅ FEATURES VERIFIED:
   • Purchase Order Management
   • Prescription Processing
   • Inventory Management
   • Return Processing
   • Daily Reporting
   • Data Backup
   • Drug Interaction Checking
   • Insurance Processing
```

---

## 🔧 IF TESTS FAIL

### **Common Issues:**

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL is running
   sudo systemctl status postgresql

   # Start if stopped
   sudo systemctl start postgresql
   ```

2. **Permission Issues**
   ```bash
   # Grant database privileges
   sudo -u postgres psql -c "ALTER USER your_username CREATEDB;"
   ```

3. **Missing Files**
   ```bash
   # Ensure all files exist
   ls -la *.sql
   ```

### **Manual Fix:**
```bash
# Apply fixes manually
psql -d pharmacy_management_v2 -f 06_missing_tables.sql
psql -d pharmacy_management_v2 -f test_fixes.sql
psql -d pharmacy_management_v2 -f 06_core_procedures.sql

# Re-run tests
./run_comprehensive_tests.sh
```

---

## ⚙️ CONFIGURATION

### **Default Settings:**
- Database: `pharmacy_management_v2`
- User: `postgres`
- Host: `localhost`
- Port: `5432`

### **Custom Settings:**
```bash
export DB_NAME="my_pharmacy_db"
export DB_USER="my_user"
export DB_PASSWORD="my_password"
./run_comprehensive_tests.sh
```

---

## 📁 KEY FILES

### **Test Scripts:**
- `run_comprehensive_tests.sh` - Main test runner
- `quick_test.sh` - Quick test on existing DB

### **SQL Files:**
- `06_missing_tables.sql` - Missing tables and data
- `test_fixes.sql` - Test environment fixes
- `06_core_procedures.sql` - All procedures (updated)
- `08_test_suite.sql` - All 29 test scenarios

### **Logs:**
- `test_logs/` - Test execution logs and reports

---

## ✅ SUCCESS CHECKLIST

- [ ] PostgreSQL is running
- [ ] All SQL files are present
- [ ] Scripts are executable (`chmod +x *.sh`)
- [ ] Database user has proper permissions
- [ ] All 29 tests pass (100% success rate)

## 💻 API Usage

### **Core Functions**

#### **1. Create Medicine**

```sql
SELECT * FROM create_medicine(
    p_tenant_id := 'your-tenant-id',
    p_medicine_code := 'MED001',
    p_medicine_name := 'Paracetamol 500mg',
    p_generic_name := 'Paracetamol',
    p_strength := '500mg',
    p_is_prescription_required := FALSE
);
```

#### **2. Receive Goods**

```sql
SELECT * FROM receive_goods(
    p_tenant_id := 'your-tenant-id',
    p_receipt_number := 'REC001',
    p_supplier_id := 'supplier-id',
    p_received_by := 'user-id',
    p_items := '[{
        "medicine_id": "medicine-id",
        "packaging_unit_id": "unit-id",
        "quantity": 100,
        "unit_cost": 5000,
        "batch_number": "BATCH001",
        "expiry_date": "2025-12-31"
    }]'::jsonb
);
```

#### **3. Process Sales Transaction**

```sql
SELECT * FROM process_sales_transaction(
    p_tenant_id := 'your-tenant-id',
    p_customer_id := 'customer-id',
    p_cashier_id := 'cashier-id',
    p_items := '[{
        "medicine_id": "medicine-id",
        "quantity": 10,
        "unit_price_override": 6000
    }]'::jsonb,
    p_payment_methods := '[{
        "payment_method_id": "payment-method-id",
        "amount": 60000
    }]'::jsonb
);
```

### **Reporting Views**

#### **1. Medicine Stock Status**

```sql
SELECT * FROM v_medicine_details
WHERE tenant_id = 'your-tenant-id'
AND current_stock < 50;
```

#### **2. Daily Sales Report**

```sql
SELECT * FROM v_daily_sales_report
WHERE tenant_id = 'your-tenant-id'
AND sale_date = CURRENT_DATE;
```

#### **3. Expiring Batches**

```sql
SELECT * FROM v_expiring_batches
WHERE tenant_id = 'your-tenant-id'
AND expiry_urgency IN ('CRITICAL', 'WARNING');
```

## 🧪 Testing

### **Automated Test Suite**

The system includes a comprehensive test suite covering:

- ✅ Tenant setup and validation
- ✅ User management and authentication
- ✅ Medicine creation and validation
- ✅ Inventory management (FEFO)
- ✅ Sales transaction processing
- ✅ Payment handling
- ✅ Stock validation
- ✅ Error handling
- ✅ Data integrity constraints

### **Running Tests**

```bash
# Run full test suite
psql -d pharmacy_management_v2 -f 08_test_suite.sql

# Expected output:
# ========================================
# TEST SUITE SUMMARY
# ========================================
# Total Tests: 10
# Passed: 10
# Failed: 0
# Success Rate: 100.0%
# 🎉 ALL TESTS PASSED! System is ready for production.
```

## ⚡ Performance Optimization

### **Indexing Strategy**

- **Primary Operations**: Optimized indexes for common queries
- **Search Operations**: GIN indexes for full-text search
- **Reporting**: Materialized views for complex analytics
- **FEFO Operations**: Specialized indexes for batch selection

### **Query Performance**

```sql
-- Example: Fast medicine search
EXPLAIN ANALYZE
SELECT * FROM v_medicine_details
WHERE medicine_name ILIKE '%paracetamol%';

-- Example: Efficient batch selection (FEFO)
EXPLAIN ANALYZE
SELECT * FROM find_best_batch_for_sale('tenant-id', 'medicine-id', 10);
```

## 🔒 Security Features

### **Multi-Tenant Isolation**

- Row-Level Security (RLS) policies
- Tenant-specific data access
- Session-based tenant context

### **Authentication & Authorization**

- bcrypt password hashing
- Role-based permissions
- Session management
- Failed login attempt tracking

### **Data Protection**

- Input validation at database level
- SQL injection prevention
- Audit trail for all changes
- Sensitive data encryption

## 🔧 Maintenance

### **Regular Tasks**

1. **Daily**
   - Monitor expiring batches
   - Check low stock alerts
   - Review failed transactions

2. **Weekly**
   - Analyze sales performance
   - Update pricing if needed
   - Review audit logs

3. **Monthly**
   - Archive old transactions
   - Update loyalty tiers
   - Performance optimization

### **Monitoring Queries**

```sql
-- System health check
SELECT
    COUNT(*) as total_medicines,
    SUM(CASE WHEN current_stock = 0 THEN 1 ELSE 0 END) as out_of_stock,
    SUM(CASE WHEN current_stock < 10 THEN 1 ELSE 0 END) as low_stock
FROM v_medicine_details
WHERE tenant_id = 'your-tenant-id';

-- Performance metrics
SELECT
    sale_date,
    total_transactions,
    net_sales,
    avg_transaction_value
FROM v_daily_sales_report
WHERE tenant_id = 'your-tenant-id'
AND sale_date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY sale_date DESC;
```

## 📞 Support

For technical support or questions:

- 📧 Email: <EMAIL>
- 📖 Documentation: [Full API Documentation](docs/api.md)
- 🐛 Issues: [GitHub Issues](https://github.com/pharmacy-system/issues)

---

**Version**: 2.0.0
**Last Updated**: 2024
**License**: MIT
**Status**: Production Ready ✅
