# Pharmacy Management System V2 - Production Deployment Guide

## 🎯 Overview

This guide provides step-by-step instructions for deploying the Pharmacy Management System V2 to a production environment with high availability, security, and performance considerations.

## 📋 Prerequisites

### **System Requirements**

- **Database**: PostgreSQL 13+ (recommended: PostgreSQL 15)
- **Memory**: Minimum 8GB RAM (recommended: 16GB+)
- **Storage**: SSD with at least 100GB free space
- **CPU**: 4+ cores (recommended: 8+ cores)
- **Network**: Stable internet connection with SSL support

### **Software Dependencies**

```bash
# PostgreSQL with required extensions
postgresql-15
postgresql-contrib-15
postgresql-15-uuid-ossp
postgresql-15-pgcrypto

# Backup and monitoring tools
pg_dump
pg_restore
pgbouncer (connection pooling)
```

## 🚀 Production Deployment Steps

### **Step 1: Environment Setup**

#### **1.1 Create Production Database**

```bash
# Switch to postgres user
sudo -u postgres psql

# Create production database and user
CREATE DATABASE pharmacy_production;
CREATE USER pharmacy_prod WITH PASSWORD 'STRONG_PRODUCTION_PASSWORD';

# Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE pharmacy_production TO pharmacy_prod;

# Connect to the database
\c pharmacy_production;

# Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

#### **1.2 Configure PostgreSQL for Production**

Edit `/etc/postgresql/15/main/postgresql.conf`:

```ini
# Memory Configuration
shared_buffers = 4GB                    # 25% of total RAM
effective_cache_size = 12GB             # 75% of total RAM
work_mem = 256MB                        # For complex queries
maintenance_work_mem = 1GB              # For maintenance operations

# Connection Settings
max_connections = 200                   # Adjust based on load
superuser_reserved_connections = 3

# Write-Ahead Logging (WAL)
wal_level = replica                     # For replication
max_wal_size = 4GB
min_wal_size = 1GB
checkpoint_completion_target = 0.9

# Query Planner
random_page_cost = 1.1                  # For SSD storage
effective_io_concurrency = 200          # For SSD storage

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_duration_statement = 1000       # Log slow queries (1 second)
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
```

Edit `/etc/postgresql/15/main/pg_hba.conf`:

```ini
# Production security settings
local   all             postgres                                peer
local   all             pharmacy_prod                           md5
host    pharmacy_production pharmacy_prod    127.0.0.1/32       md5
host    pharmacy_production pharmacy_prod    ::1/128            md5

# Application connections (adjust IP ranges as needed)
host    pharmacy_production pharmacy_prod    10.0.0.0/8         md5
```

### **Step 2: Schema Deployment**

#### **2.1 Execute Schema Files**

```bash
# Set environment variables
export PGHOST=localhost
export PGPORT=5432
export PGDATABASE=pharmacy_production
export PGUSER=pharmacy_prod
export PGPASSWORD=STRONG_PRODUCTION_PASSWORD

# Execute schema files in correct order
psql -f 01_core_schema.sql
psql -f 02_inventory_schema.sql
psql -f 03_customer_sales_schema.sql
psql -f 04_sales_transactions_schema.sql
psql -f 05_indexes_constraints.sql
psql -f 06_core_procedures.sql
psql -f 07_views_reporting.sql

# Verify deployment
echo "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | psql
```

#### **2.2 Run Production Tests**

```bash
# Execute test suite
psql -f 08_test_suite.sql

# Expected output should show all tests passing
# If any tests fail, DO NOT proceed to production
```

### **Step 3: Security Hardening**

#### **3.1 Database Security**

```sql
-- Create application-specific roles
CREATE ROLE pharmacy_read;
CREATE ROLE pharmacy_write;
CREATE ROLE pharmacy_admin;

-- Grant appropriate permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO pharmacy_read;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO pharmacy_write;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pharmacy_admin;

-- Create application users
CREATE USER pharmacy_app_read WITH PASSWORD 'READ_USER_PASSWORD';
CREATE USER pharmacy_app_write WITH PASSWORD 'WRITE_USER_PASSWORD';
CREATE USER pharmacy_app_admin WITH PASSWORD 'ADMIN_USER_PASSWORD';

-- Assign roles
GRANT pharmacy_read TO pharmacy_app_read;
GRANT pharmacy_write TO pharmacy_app_write;
GRANT pharmacy_admin TO pharmacy_app_admin;

-- Enable Row Level Security on all tenant tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE medicines ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_transactions ENABLE ROW LEVEL SECURITY;
-- ... (enable for all tenant-specific tables)
```

#### **3.2 SSL Configuration**

```bash
# Generate SSL certificates (use proper CA in production)
sudo openssl req -new -x509 -days 365 -nodes -text \
  -out /etc/ssl/certs/postgresql.crt \
  -keyout /etc/ssl/private/postgresql.key \
  -subj "/CN=pharmacy-db.yourdomain.com"

# Set proper permissions
sudo chmod 600 /etc/ssl/private/postgresql.key
sudo chown postgres:postgres /etc/ssl/private/postgresql.key
sudo chown postgres:postgres /etc/ssl/certs/postgresql.crt
```

Add to `postgresql.conf`:

```ini
ssl = on
ssl_cert_file = '/etc/ssl/certs/postgresql.crt'
ssl_key_file = '/etc/ssl/private/postgresql.key'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on
```

### **Step 4: Performance Optimization**

#### **4.1 Connection Pooling with PgBouncer**

Install and configure PgBouncer:

```bash
# Install PgBouncer
sudo apt-get install pgbouncer

# Configure /etc/pgbouncer/pgbouncer.ini
[databases]
pharmacy_production = host=localhost port=5432 dbname=pharmacy_production

[pgbouncer]
listen_port = 6432
listen_addr = 127.0.0.1
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 50
max_db_connections = 200
```

#### **4.2 Monitoring Setup**

Create monitoring views:

```sql
-- Create monitoring schema
CREATE SCHEMA monitoring;

-- Active connections view
CREATE VIEW monitoring.active_connections AS
SELECT 
    datname,
    usename,
    application_name,
    client_addr,
    state,
    query_start,
    state_change,
    query
FROM pg_stat_activity 
WHERE state != 'idle';

-- Database statistics view
CREATE VIEW monitoring.database_stats AS
SELECT 
    datname,
    numbackends,
    xact_commit,
    xact_rollback,
    blks_read,
    blks_hit,
    tup_returned,
    tup_fetched,
    tup_inserted,
    tup_updated,
    tup_deleted
FROM pg_stat_database;

-- Table statistics view
CREATE VIEW monitoring.table_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_live_tup,
    n_dead_tup,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables;
```

### **Step 5: Backup Strategy**

#### **5.1 Automated Backup Script**

Create `/opt/pharmacy/backup.sh`:

```bash
#!/bin/bash

# Configuration
DB_NAME="pharmacy_production"
DB_USER="pharmacy_prod"
BACKUP_DIR="/opt/pharmacy/backups"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Full database backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME \
  --format=custom --compress=9 \
  --file="$BACKUP_DIR/pharmacy_full_$DATE.backup"

# Schema-only backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME \
  --schema-only --format=plain \
  --file="$BACKUP_DIR/pharmacy_schema_$DATE.sql"

# Cleanup old backups
find $BACKUP_DIR -name "*.backup" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.sql" -mtime +$RETENTION_DAYS -delete

# Log backup completion
echo "$(date): Backup completed successfully" >> /var/log/pharmacy_backup.log
```

#### **5.2 Setup Cron Job**

```bash
# Add to crontab (daily backup at 2 AM)
sudo crontab -e

# Add this line:
0 2 * * * /opt/pharmacy/backup.sh
```

### **Step 6: Application Configuration**

#### **6.1 Environment Variables**

Create `.env` file for your application:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=6432  # PgBouncer port
DB_NAME=pharmacy_production
DB_USER=pharmacy_app_write
DB_PASSWORD=WRITE_USER_PASSWORD
DB_SSL_MODE=require

# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_LOG_LEVEL=info

# Security
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key
SESSION_TIMEOUT=3600

# Features
ENABLE_AUDIT_LOG=true
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=100
```

#### **6.2 Connection String Examples**

```python
# Python (SQLAlchemy)
DATABASE_URL = "postgresql://pharmacy_app_write:password@localhost:6432/pharmacy_production?sslmode=require"

# Node.js (pg)
const config = {
  host: 'localhost',
  port: 6432,
  database: 'pharmacy_production',
  user: 'pharmacy_app_write',
  password: 'password',
  ssl: { rejectUnauthorized: false },
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

# Java (JDBC)
*******************************************************************************************************
```

### **Step 7: Health Checks & Monitoring**

#### **7.1 Health Check Endpoint**

```sql
-- Create health check function
CREATE OR REPLACE FUNCTION health_check()
RETURNS TABLE(
    status TEXT,
    database_size TEXT,
    active_connections INTEGER,
    last_backup_age INTERVAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'healthy'::TEXT as status,
        pg_size_pretty(pg_database_size(current_database()))::TEXT as database_size,
        (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')::INTEGER as active_connections,
        (SELECT CURRENT_TIMESTAMP - MAX(created_at) FROM audit_logs)::INTERVAL as last_backup_age;
END;
$$ LANGUAGE plpgsql;
```

#### **7.2 Monitoring Queries**

```sql
-- Check for long-running queries
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- Check database locks
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

### **Step 8: Go-Live Checklist**

#### **Pre-Deployment**

- [ ] All tests pass successfully
- [ ] SSL certificates configured
- [ ] Backup strategy implemented
- [ ] Monitoring setup complete
- [ ] Security hardening applied
- [ ] Performance tuning done
- [ ] Connection pooling configured

#### **Deployment**

- [ ] Database schema deployed
- [ ] Initial data loaded (if any)
- [ ] Application configuration updated
- [ ] Health checks passing
- [ ] Backup verification completed

#### **Post-Deployment**

- [ ] Monitor system performance for 24 hours
- [ ] Verify all functionality works correctly
- [ ] Check backup completion
- [ ] Review security logs
- [ ] Document any issues and resolutions

## 🚨 Troubleshooting

### **Common Issues**

1. **Connection Issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check connections
   sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
   ```

2. **Performance Issues**
   ```sql
   -- Check slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC LIMIT 10;
   ```

3. **Lock Issues**
   ```sql
   -- Kill blocking queries
   SELECT pg_terminate_backend(pid) 
   FROM pg_stat_activity 
   WHERE state = 'active' AND query_start < now() - interval '10 minutes';
   ```

## 📞 Support

For production support:

- **Emergency**: +1-XXX-XXX-XXXX
- **Email**: <EMAIL>
- **Monitoring**: [Dashboard URL]
- **Documentation**: [Internal Wiki]

---

**Document Version**: 1.0  
**Last Updated**: 2024  
**Review Date**: Every 6 months
