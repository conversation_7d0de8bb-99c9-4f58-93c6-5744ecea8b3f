-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - INDEXES & CONSTRAINTS
-- =====================================================
-- Performance optimization and data integrity enforcement
-- =====================================================

-- =====================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- =====================================================

-- Medicine-related indexes for fast searching
CREATE INDEX idx_medicines_tenant_active ON medicines(tenant_id, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_medicines_registration_number ON medicines(registration_number) WHERE registration_number IS NOT NULL;
CREATE INDEX idx_medicines_barcode ON medicines(barcode) WHERE barcode IS NOT NULL;
CREATE INDEX idx_medicines_generic_name ON medicines(generic_name) WHERE generic_name IS NOT NULL;
CREATE INDEX idx_medicines_brand_name ON medicines(brand_name) WHERE brand_name IS NOT NULL;
CREATE INDEX idx_medicines_category ON medicines(category_id);
CREATE INDEX idx_medicines_manufacturer ON medicines(manufacturer_id);
CREATE INDEX idx_medicines_prescription_required ON medicines(tenant_id, is_prescription_required);

-- Full-text search indexes for medicine names
CREATE INDEX idx_medicines_name_gin ON medicines USING gin(to_tsvector('english', medicine_name));
CREATE INDEX idx_medicines_generic_gin ON medicines USING gin(to_tsvector('english', generic_name));

-- Inventory batch indexes for FEFO and stock management
CREATE INDEX idx_batches_fefo ON inventory_batches(tenant_id, medicine_id, expiry_date, quantity_available)
    WHERE quantity_available > 0 AND quality_status = 'GOOD';
CREATE INDEX idx_batches_low_stock ON inventory_batches(tenant_id, medicine_id, quantity_available)
    WHERE quantity_available > 0 AND quantity_available < 10;
CREATE INDEX idx_batches_near_expiry ON inventory_batches(expiry_date, tenant_id)
    WHERE quantity_available > 0;

-- Customer search indexes
CREATE INDEX idx_customers_search_name ON customers USING gin(to_tsvector('english', full_name));
CREATE INDEX idx_customers_phone_partial ON customers(tenant_id, phone_number) WHERE phone_number IS NOT NULL;
CREATE INDEX idx_customers_loyalty_points ON customers(tenant_id, loyalty_points DESC) WHERE loyalty_points > 0;

-- Sales performance indexes
CREATE INDEX idx_sales_date_range ON sales_transactions(tenant_id, transaction_date, status);
CREATE INDEX idx_sales_customer_date ON sales_transactions(customer_id, transaction_date) WHERE customer_id IS NOT NULL;
CREATE INDEX idx_sales_cashier_date ON sales_transactions(cashier_id, transaction_date);
CREATE INDEX idx_sales_amount_range ON sales_transactions(tenant_id, total_amount, transaction_date);

-- Prescription indexes
CREATE INDEX idx_prescriptions_customer_date ON prescriptions(customer_id, prescription_date) WHERE customer_id IS NOT NULL;
CREATE INDEX idx_prescriptions_doctor_date ON prescriptions(doctor_id, prescription_date) WHERE doctor_id IS NOT NULL;
CREATE INDEX idx_prescriptions_status_date ON prescriptions(tenant_id, status, prescription_date);
CREATE INDEX idx_prescriptions_valid_until ON prescriptions(valid_until) WHERE valid_until IS NOT NULL AND status = 'ACTIVE';

-- Audit trail indexes
CREATE INDEX idx_audit_logs_table_date ON audit_logs(tenant_id, table_name, created_at);
CREATE INDEX idx_audit_logs_user_date ON audit_logs(user_id, created_at) WHERE user_id IS NOT NULL;

-- =====================================================
-- BUSINESS LOGIC CONSTRAINTS
-- =====================================================

-- Ensure only one default price list per tenant and customer type
CREATE UNIQUE INDEX uk_price_lists_default
ON price_lists(tenant_id, customer_type_id)
WHERE is_default = TRUE;

-- Ensure only one default packaging unit per medicine per type
CREATE UNIQUE INDEX uk_packaging_default_purchase
ON packaging_units(tenant_id, medicine_id)
WHERE is_default_purchase_unit = TRUE;

CREATE UNIQUE INDEX uk_packaging_default_sale
ON packaging_units(tenant_id, medicine_id)
WHERE is_default_sale_unit = TRUE;

CREATE UNIQUE INDEX uk_packaging_default_dispensing
ON packaging_units(tenant_id, medicine_id)
WHERE is_default_dispensing_unit = TRUE;

-- Ensure medicine prices don't overlap for same conditions
CREATE UNIQUE INDEX uk_medicine_prices_no_overlap
ON medicine_prices(tenant_id, price_list_id, medicine_id, packaging_unit_id,
                   COALESCE(effective_to, '9999-12-31'::date))
WHERE is_active = TRUE;

-- Ensure batch numbers are unique per medicine within tenant
CREATE UNIQUE INDEX uk_batches_medicine_batch_number
ON inventory_batches(tenant_id, medicine_id, batch_number);

-- =====================================================
-- DATA VALIDATION CONSTRAINTS
-- =====================================================

-- Add check constraints for business rules
ALTER TABLE medicines ADD CONSTRAINT chk_medicines_strength_format
CHECK (strength ~ '^[0-9]+(\.[0-9]+)?(mg|g|ml|mcg|IU|%)?(/[0-9]+(\.[0-9]+)?(mg|g|ml|mcg|IU|%)?)?$');

ALTER TABLE customers ADD CONSTRAINT chk_customers_phone_format
CHECK (phone_number ~ '^[+]?[0-9\s\-\(\)]{10,20}$');

ALTER TABLE customers ADD CONSTRAINT chk_customers_email_format
CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE users ADD CONSTRAINT chk_users_username_format
CHECK (username ~ '^[a-zA-Z0-9_]{3,50}$');

ALTER TABLE users ADD CONSTRAINT chk_users_password_strength
CHECK (LENGTH(password_hash) >= 60); -- bcrypt hash length

-- Ensure expiry dates are reasonable (not more than 10 years in future)
ALTER TABLE inventory_batches ADD CONSTRAINT chk_batches_expiry_reasonable
CHECK (expiry_date <= CURRENT_DATE + INTERVAL '10 years');

-- Ensure manufacturing date is not in the future
ALTER TABLE inventory_batches ADD CONSTRAINT chk_batches_manufacturing_date
CHECK (manufacturing_date IS NULL OR manufacturing_date <= CURRENT_DATE);

-- Ensure prescription valid_until is reasonable (not more than 1 year)
ALTER TABLE prescriptions ADD CONSTRAINT chk_prescriptions_valid_until_reasonable
CHECK (valid_until IS NULL OR valid_until <= prescription_date + INTERVAL '1 year');

-- =====================================================
-- FOREIGN KEY CONSTRAINTS WITH PROPER CASCADE RULES
-- =====================================================

-- Add missing foreign key constraints with appropriate cascade rules
ALTER TABLE users ADD CONSTRAINT fk_users_tenant
FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE;

ALTER TABLE users ADD CONSTRAINT fk_users_role
FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE SET NULL;

ALTER TABLE medicines ADD CONSTRAINT fk_medicines_tenant
FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE;

ALTER TABLE inventory_batches ADD CONSTRAINT fk_batches_supplier
FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id) ON DELETE SET NULL;

-- Prevent deletion of medicines that have active batches
ALTER TABLE inventory_batches ADD CONSTRAINT fk_batches_medicine_restrict
FOREIGN KEY (medicine_id) REFERENCES medicines(medicine_id) ON DELETE RESTRICT;

-- Prevent deletion of customers with outstanding balances
ALTER TABLE customers ADD CONSTRAINT chk_customers_delete_balance
CHECK (NOT (is_active = FALSE AND current_balance != 0));

-- =====================================================
-- TRIGGERS FOR BUSINESS LOGIC
-- =====================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update timestamp triggers to all relevant tables
CREATE TRIGGER trg_tenants_updated_at BEFORE UPDATE ON tenants
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_users_updated_at BEFORE UPDATE ON users
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_medicines_updated_at BEFORE UPDATE ON medicines
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_customers_updated_at BEFORE UPDATE ON customers
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_suppliers_updated_at BEFORE UPDATE ON suppliers
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_inventory_batches_updated_at BEFORE UPDATE ON inventory_batches
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_sales_transactions_updated_at BEFORE UPDATE ON sales_transactions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to prevent negative inventory
CREATE OR REPLACE FUNCTION check_inventory_negative()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.quantity_available < 0 THEN
        RAISE EXCEPTION 'Inventory quantity cannot be negative. Batch: %, Available: %',
                        NEW.batch_id, NEW.quantity_available;
    END IF;

    IF NEW.quantity_reserved < 0 THEN
        RAISE EXCEPTION 'Reserved quantity cannot be negative. Batch: %, Reserved: %',
                        NEW.batch_id, NEW.quantity_reserved;
    END IF;

    IF NEW.quantity_reserved > NEW.quantity_available THEN
        RAISE EXCEPTION 'Reserved quantity cannot exceed available quantity. Batch: %, Available: %, Reserved: %',
                        NEW.batch_id, NEW.quantity_available, NEW.quantity_reserved;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_inventory_batches_check_negative
BEFORE INSERT OR UPDATE ON inventory_batches
FOR EACH ROW EXECUTE FUNCTION check_inventory_negative();

-- Function to update customer loyalty points
CREATE OR REPLACE FUNCTION update_customer_loyalty_points()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.status = 'COMPLETED' THEN
        -- Add points for new completed transaction
        UPDATE customers
        SET loyalty_points = loyalty_points + NEW.loyalty_points_earned - NEW.loyalty_points_redeemed,
            total_purchases = total_purchases + NEW.total_amount,
            last_purchase_date = NEW.transaction_date::date
        WHERE customer_id = NEW.customer_id;

    ELSIF TG_OP = 'UPDATE' THEN
        -- Handle status changes
        IF OLD.status != 'COMPLETED' AND NEW.status = 'COMPLETED' THEN
            -- Transaction completed
            UPDATE customers
            SET loyalty_points = loyalty_points + NEW.loyalty_points_earned - NEW.loyalty_points_redeemed,
                total_purchases = total_purchases + NEW.total_amount,
                last_purchase_date = NEW.transaction_date::date
            WHERE customer_id = NEW.customer_id;

        ELSIF OLD.status = 'COMPLETED' AND NEW.status != 'COMPLETED' THEN
            -- Transaction cancelled/returned
            UPDATE customers
            SET loyalty_points = loyalty_points - OLD.loyalty_points_earned + OLD.loyalty_points_redeemed,
                total_purchases = total_purchases - OLD.total_amount
            WHERE customer_id = OLD.customer_id;

        ELSIF NEW.status = 'COMPLETED' AND (
            OLD.loyalty_points_earned != NEW.loyalty_points_earned OR
            OLD.loyalty_points_redeemed != NEW.loyalty_points_redeemed
        ) THEN
            -- Loyalty points updated on completed transaction
            UPDATE customers
            SET loyalty_points = loyalty_points + (NEW.loyalty_points_earned - OLD.loyalty_points_earned) - (NEW.loyalty_points_redeemed - OLD.loyalty_points_redeemed)
            WHERE customer_id = NEW.customer_id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_sales_transactions_loyalty_points
AFTER INSERT OR UPDATE ON sales_transactions
FOR EACH ROW
WHEN (NEW.customer_id IS NOT NULL)
EXECUTE FUNCTION update_customer_loyalty_points();

-- Function to update inventory after sales
CREATE OR REPLACE FUNCTION update_inventory_after_sale()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Reduce available quantity
        UPDATE inventory_batches
        SET quantity_available = quantity_available - NEW.quantity_in_base_units,
            quantity_sold = quantity_sold + NEW.quantity_in_base_units
        WHERE batch_id = NEW.batch_id;

        -- Check if update was successful
        IF NOT FOUND THEN
            RAISE EXCEPTION 'Batch not found: %', NEW.batch_id;
        END IF;

    ELSIF TG_OP = 'UPDATE' THEN
        -- Handle quantity changes
        UPDATE inventory_batches
        SET quantity_available = quantity_available + OLD.quantity_in_base_units - NEW.quantity_in_base_units,
            quantity_sold = quantity_sold - OLD.quantity_in_base_units + NEW.quantity_in_base_units
        WHERE batch_id = NEW.batch_id;

    ELSIF TG_OP = 'DELETE' THEN
        -- Restore quantity on deletion
        UPDATE inventory_batches
        SET quantity_available = quantity_available + OLD.quantity_in_base_units,
            quantity_sold = quantity_sold - OLD.quantity_in_base_units
        WHERE batch_id = OLD.batch_id;

        RETURN OLD;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_sales_transaction_items_inventory
AFTER INSERT OR UPDATE OR DELETE ON sales_transaction_items
FOR EACH ROW EXECUTE FUNCTION update_inventory_after_sale();

-- =====================================================
-- SECURITY CONSTRAINTS
-- =====================================================

-- Row Level Security (RLS) for tenant isolation
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE medicines ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_batches ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (example for multi-tenant isolation)
-- Note: These policies require setting app.current_tenant_id in session
-- Example: SET app.current_tenant_id = 'your-tenant-uuid';

-- CREATE POLICY tenant_isolation_policy ON users
-- FOR ALL TO PUBLIC
-- USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- CREATE POLICY tenant_isolation_policy ON medicines
-- FOR ALL TO PUBLIC
-- USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- CREATE POLICY tenant_isolation_policy ON customers
-- FOR ALL TO PUBLIC
-- USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- CREATE POLICY tenant_isolation_policy ON sales_transactions
-- FOR ALL TO PUBLIC
-- USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- CREATE POLICY tenant_isolation_policy ON inventory_batches
-- FOR ALL TO PUBLIC
-- USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- Note: RLS policies are commented out for testing.
-- Uncomment and configure properly for production use.
