-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - CORE SCHEMA
-- =====================================================
-- Refactored for production readiness with improved:
-- - Simplified architecture (single PK + tenant isolation)
-- - Better performance with proper indexes
-- - Enhanced data validation and constraints
-- - Comprehensive audit trail
-- - Security improvements
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto"; -- For password hashing

-- =====================================================
-- CORE TENANT MANAGEMENT
-- =====================================================

-- Table: tenants (Simplified tenant management)
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_code VARCHAR(20) NOT NULL UNIQUE, -- Short code for easy reference
    tenant_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255) UNIQUE,
    phone_number VARCHAR(20),
    address TEXT,
    tax_code VARCHAR(50), -- Mã số thuế
    license_number VARCHAR(100), -- Số giấy phép kinh doanh dược
    subscription_plan VARCHAR(50) DEFAULT 'BASIC', -- BASIC, PREMIUM, ENTERPRISE
    subscription_start_date DATE NOT NULL DEFAULT CURRENT_DATE,
    subscription_end_date DATE,
    max_users INTEGER DEFAULT 10,
    max_locations INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- Indexes for tenants
CREATE INDEX idx_tenants_code ON tenants(tenant_code);
CREATE INDEX idx_tenants_active ON tenants(is_active);
CREATE INDEX idx_tenants_subscription ON tenants(subscription_end_date) WHERE is_active = TRUE;

-- =====================================================
-- TENANT CONFIGURATION SYSTEM
-- =====================================================

-- Table: tenant_settings (Flexible configuration system)
CREATE TABLE tenant_settings (
    setting_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    setting_category VARCHAR(50) NOT NULL, -- SALES, INVENTORY, PRICING, LOYALTY, SECURITY
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(20) DEFAULT 'STRING', -- STRING, INTEGER, DECIMAL, BOOLEAN, JSON
    description TEXT,
    is_system_setting BOOLEAN DEFAULT FALSE, -- Cannot be deleted
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_tenant_settings_key UNIQUE(tenant_id, setting_category, setting_key),
    CONSTRAINT chk_tenant_settings_type CHECK (
        setting_type IN ('STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN', 'JSON')
    )
);

-- Insert default sales policy settings for each tenant
INSERT INTO tenant_settings (tenant_id, setting_category, setting_key, setting_value, setting_type, description, is_system_setting)
SELECT
    t.tenant_id,
    unnest(ARRAY[
        'SALES', 'SALES', 'SALES', 'SALES', 'SALES',
        'PRICING', 'PRICING', 'PRICING', 'PRICING',
        'INVENTORY', 'INVENTORY', 'INVENTORY',
        'LOYALTY', 'LOYALTY', 'LOYALTY',
        'SECURITY', 'SECURITY'
    ]),
    unnest(ARRAY[
        'ALLOW_ZERO_PRICE_SALES', 'ALLOW_100_PERCENT_DISCOUNT', 'MAX_DISCOUNT_PERCENTAGE', 'REQUIRE_MANAGER_APPROVAL_ZERO_SALES', 'REQUIRE_REASON_ZERO_SALES',
        'ALLOW_NEGATIVE_PRICING', 'REQUIRE_COST_VALIDATION', 'MAX_MARKUP_PERCENTAGE', 'MIN_PROFIT_MARGIN',
        'ALLOW_NEGATIVE_STOCK', 'REQUIRE_BATCH_TRACKING', 'AUTO_REORDER_ENABLED',
        'POINTS_PER_CURRENCY_UNIT', 'POINTS_EXPIRY_MONTHS', 'ALLOW_POINTS_REDEMPTION',
        'REQUIRE_STRONG_PASSWORDS', 'SESSION_TIMEOUT_MINUTES'
    ]),
    unnest(ARRAY[
        'false', 'false', '50.0', 'true', 'true',
        'false', 'true', '300.0', '10.0',
        'false', 'true', 'false',
        '0.001', '12', 'true',
        'true', '30'
    ]),
    unnest(ARRAY[
        'BOOLEAN', 'BOOLEAN', 'DECIMAL', 'BOOLEAN', 'BOOLEAN',
        'BOOLEAN', 'BOOLEAN', 'DECIMAL', 'DECIMAL',
        'BOOLEAN', 'BOOLEAN', 'BOOLEAN',
        'DECIMAL', 'INTEGER', 'BOOLEAN',
        'BOOLEAN', 'INTEGER'
    ]),
    unnest(ARRAY[
        'Allow selling items at 0 price',
        'Allow 100% discount on transactions',
        'Maximum discount percentage allowed',
        'Require manager approval for zero-price sales',
        'Require reason for zero-price sales',
        'Allow negative pricing for special cases',
        'Validate selling price against cost',
        'Maximum markup percentage allowed',
        'Minimum profit margin required (%)',
        'Allow negative inventory quantities',
        'Require batch tracking for all items',
        'Enable automatic reorder when stock low',
        'Loyalty points earned per currency unit',
        'Loyalty points expiry in months',
        'Allow customers to redeem loyalty points',
        'Require strong password policy',
        'User session timeout in minutes'
    ]),
    TRUE
FROM tenants t WHERE t.is_active = TRUE;

-- Indexes for tenant_settings
CREATE INDEX idx_tenant_settings_tenant ON tenant_settings(tenant_id);
CREATE INDEX idx_tenant_settings_category ON tenant_settings(tenant_id, setting_category);
CREATE INDEX idx_tenant_settings_key ON tenant_settings(tenant_id, setting_category, setting_key);
CREATE INDEX idx_tenant_settings_active ON tenant_settings(tenant_id, is_active) WHERE is_active = TRUE;

-- =====================================================
-- AUDIT TRAIL SYSTEM
-- =====================================================

-- Table: audit_logs (Comprehensive audit trail)
CREATE TABLE audit_logs (
    audit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[], -- Array of changed field names
    user_id UUID,
    user_ip INET,
    user_agent TEXT,
    session_id UUID,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for audit_logs
CREATE INDEX idx_audit_tenant_table ON audit_logs(tenant_id, table_name);
CREATE INDEX idx_audit_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_user ON audit_logs(user_id, created_at);
CREATE INDEX idx_audit_created_at ON audit_logs(created_at);

-- =====================================================
-- USER MANAGEMENT & SECURITY
-- =====================================================

-- Table: roles (System-wide roles with tenant isolation)
CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    role_code VARCHAR(50) NOT NULL, -- ADMIN, PHARMACIST, CASHIER, MANAGER
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]', -- Array of permission codes
    is_system_role BOOLEAN DEFAULT FALSE, -- Cannot be deleted
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_roles_tenant_code UNIQUE(tenant_id, role_code)
);

-- Table: users (Simplified user management)
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    role_id UUID REFERENCES roles(role_id),
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    password_hash TEXT NOT NULL, -- bcrypt hash
    password_salt TEXT NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(10) CHECK (gender IN ('MALE', 'FEMALE', 'OTHER')),
    address TEXT,
    employee_code VARCHAR(50), -- Mã nhân viên
    hire_date DATE,
    termination_date DATE,
    last_login_at TIMESTAMPTZ,
    last_login_ip INET,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    password_changed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    must_change_password BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_users_tenant_employee_code UNIQUE(tenant_id, employee_code)
);

-- Indexes for users
CREATE INDEX idx_users_tenant ON users(tenant_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_employee_code ON users(tenant_id, employee_code);
CREATE INDEX idx_users_active ON users(tenant_id, is_active);

-- =====================================================
-- MEDICINE MASTER DATA
-- =====================================================

-- Table: medicine_categories (Enhanced categories)
CREATE TABLE medicine_categories (
    category_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    category_code VARCHAR(50) NOT NULL,
    category_name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_category_id UUID REFERENCES medicine_categories(category_id),
    therapeutic_class VARCHAR(100), -- Nhóm điều trị
    is_prescription_category BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_categories_tenant_code UNIQUE(tenant_id, category_code)
);

-- Table: manufacturers (Separate manufacturer management)
CREATE TABLE manufacturers (
    manufacturer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    manufacturer_code VARCHAR(50) NOT NULL UNIQUE,
    manufacturer_name VARCHAR(255) NOT NULL,
    country_of_origin VARCHAR(100),
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone_number VARCHAR(20),
    address TEXT,
    website VARCHAR(255),
    license_number VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Table: dosage_forms (Standardized dosage forms)
CREATE TABLE dosage_forms (
    dosage_form_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_code VARCHAR(20) NOT NULL UNIQUE, -- TAB, CAP, SYR, INJ, etc.
    form_name VARCHAR(100) NOT NULL, -- Tablet, Capsule, Syrup, Injection
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

-- Insert standard dosage forms
INSERT INTO dosage_forms (form_code, form_name, description) VALUES
('TAB', 'Tablet', 'Viên nén'),
('CAP', 'Capsule', 'Viên nang'),
('SYR', 'Syrup', 'Siro'),
('INJ', 'Injection', 'Tiêm'),
('CRE', 'Cream', 'Kem'),
('OIN', 'Ointment', 'Thuốc mỡ'),
('DRO', 'Drops', 'Thuốc nhỏ'),
('SPR', 'Spray', 'Xịt'),
('SUP', 'Suppository', 'Thuốc đặt'),
('POW', 'Powder', 'Bột');

-- =====================================================
-- ENHANCED MEDICINE MANAGEMENT
-- =====================================================

-- Table: medicines (Comprehensive medicine information)
CREATE TABLE medicines (
    medicine_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    medicine_code VARCHAR(50) NOT NULL, -- Internal code
    medicine_name VARCHAR(255) NOT NULL,
    generic_name VARCHAR(255), -- Tên hoạt chất
    brand_name VARCHAR(255), -- Tên thương mại
    registration_number VARCHAR(100), -- Số đăng ký (longer for Vietnam)
    barcode VARCHAR(50), -- EAN/UPC barcode
    category_id UUID REFERENCES medicine_categories(category_id),
    manufacturer_id UUID REFERENCES manufacturers(manufacturer_id),
    dosage_form_id UUID REFERENCES dosage_forms(dosage_form_id),
    strength VARCHAR(100), -- Nồng độ: "500mg", "250mg/5ml"
    active_ingredients TEXT, -- JSON array of active ingredients
    inactive_ingredients TEXT,
    base_unit VARCHAR(50) NOT NULL, -- Smallest dispensing unit
    pack_size INTEGER, -- Number of base units in standard pack
    therapeutic_class VARCHAR(100),
    atc_code VARCHAR(20), -- WHO ATC classification
    storage_conditions TEXT,
    storage_temperature_min DECIMAL(5,2), -- Celsius
    storage_temperature_max DECIMAL(5,2), -- Celsius
    contraindications TEXT,
    side_effects TEXT,
    drug_interactions TEXT,
    dosage_instructions TEXT,
    pregnancy_category VARCHAR(10), -- A, B, C, D, X
    is_prescription_required BOOLEAN DEFAULT FALSE,
    is_controlled_substance BOOLEAN DEFAULT FALSE,
    is_refrigerated BOOLEAN DEFAULT FALSE,
    is_narcotic BOOLEAN DEFAULT FALSE,
    shelf_life_months INTEGER, -- Shelf life in months
    vat_rate DECIMAL(5,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    CONSTRAINT uk_medicines_tenant_code UNIQUE(tenant_id, medicine_code),
    CONSTRAINT uk_medicines_tenant_registration UNIQUE(tenant_id, registration_number),
    CONSTRAINT uk_medicines_tenant_barcode UNIQUE(tenant_id, barcode),
    CONSTRAINT chk_medicines_vat_rate CHECK (vat_rate >= 0 AND vat_rate <= 100),
    CONSTRAINT chk_medicines_shelf_life CHECK (shelf_life_months > 0)
);
