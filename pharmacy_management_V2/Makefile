# =====================================================
# PHARMACY MANAGEMENT SYSTEM V2 - MAKEFILE
# =====================================================
# Convenient commands for testing and deployment
# =====================================================

# Default configuration
DB_HOST ?= localhost
DB_PORT ?= 5432
DB_NAME ?= pharmacy_test_v2
DB_USER ?= postgres
DB_PASSWORD ?=
VERBOSE ?= false
CLEANUP ?= true

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
CYAN := \033[0;36m
NC := \033[0m

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)Pharmacy Management System V2 - Available Commands$(NC)"
	@echo "=================================================="
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(BLUE)%-20s$(NC) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(YELLOW)Configuration (can be overridden):$(NC)"
	@echo "  DB_HOST=$(DB_HOST)"
	@echo "  DB_PORT=$(DB_PORT)"
	@echo "  DB_NAME=$(DB_NAME)"
	@echo "  DB_USER=$(DB_USER)"
	@echo "  VERBOSE=$(VERBOSE)"
	@echo "  CLEANUP=$(CLEANUP)"
	@echo ""
	@echo "$(YELLOW)Examples:$(NC)"
	@echo "  make test                           # Run all tests with defaults"
	@echo "  make test-verbose                   # Run tests with verbose output"
	@echo "  make test-keep-db                   # Run tests and keep database"
	@echo "  make test DB_HOST=myserver          # Run tests on custom host"
	@echo "  make deploy-schema                  # Deploy schema only"
	@echo "  make clean                          # Clean up test artifacts"

# Default target
.DEFAULT_GOAL := help

# Check prerequisites
.PHONY: check-prereq
check-prereq: ## Check if all prerequisites are installed
	@echo "$(CYAN)Checking Prerequisites...$(NC)"
	@command -v psql >/dev/null 2>&1 || { echo "$(RED)❌ PostgreSQL client (psql) not found$(NC)"; exit 1; }
	@echo "$(GREEN)✅ PostgreSQL client found$(NC)"
	@command -v python3 >/dev/null 2>&1 || { echo "$(YELLOW)⚠️  Python3 not found (optional)$(NC)"; }
	@test -f 01_core_schema.sql || { echo "$(RED)❌ Schema files not found$(NC)"; exit 1; }
	@echo "$(GREEN)✅ All prerequisites met$(NC)"

# Test database connection
.PHONY: test-connection
test-connection: ## Test database connection
	@echo "$(CYAN)Testing Database Connection...$(NC)"
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -c "SELECT version();" >/dev/null 2>&1 || { echo "$(RED)❌ Cannot connect to database$(NC)"; exit 1; }
	@echo "$(GREEN)✅ Database connection successful$(NC)"

# Create test database
.PHONY: create-test-db
create-test-db: test-connection ## Create test database
	@echo "$(CYAN)Creating Test Database...$(NC)"
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -c "DROP DATABASE IF EXISTS $(DB_NAME);" >/dev/null 2>&1
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -c "CREATE DATABASE $(DB_NAME);" >/dev/null 2>&1
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" >/dev/null 2>&1
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) -c "CREATE EXTENSION IF NOT EXISTS \"pgcrypto\";" >/dev/null 2>&1
	@echo "$(GREEN)✅ Test database created: $(DB_NAME)$(NC)"

# Deploy schema
.PHONY: deploy-schema
deploy-schema: create-test-db ## Deploy database schema to test database
	@echo "$(CYAN)Deploying Database Schema...$(NC)"
	@for file in 01_core_schema.sql 02_inventory_schema.sql 03_customer_sales_schema.sql 04_sales_transactions_schema.sql 05_indexes_constraints.sql 06_core_procedures.sql 07_views_reporting.sql; do \
		echo "$(YELLOW)📄 Executing: $$file$(NC)"; \
		PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) -f $$file >/dev/null 2>&1 || { echo "$(RED)❌ Failed to execute $$file$(NC)"; exit 1; }; \
		echo "$(GREEN)   ✅ $$file executed successfully$(NC)"; \
	done
	@echo "$(GREEN)✅ Schema deployment completed$(NC)"

# Run tests using shell script
.PHONY: test
test: check-prereq ## Run complete test suite using shell script
	@chmod +x run_comprehensive_tests.sh
	@DB_HOST=$(DB_HOST) DB_PORT=$(DB_PORT) DB_NAME=$(DB_NAME) DB_USER=$(DB_USER) DB_PASSWORD=$(DB_PASSWORD) VERBOSE=$(VERBOSE) CLEANUP_AFTER_TESTS=$(CLEANUP) ./run_comprehensive_tests.sh

# Run tests with verbose output
.PHONY: test-verbose
test-verbose: ## Run tests with verbose output
	@$(MAKE) test VERBOSE=true

# Run tests and keep database
.PHONY: test-keep-db
test-keep-db: ## Run tests and keep test database for inspection
	@$(MAKE) test CLEANUP=false

# Run tests using Python script (if available)
.PHONY: test-python
test-python: check-prereq ## Run tests using Python script (advanced features)
	@command -v python3 >/dev/null 2>&1 || { echo "$(RED)❌ Python3 not found$(NC)"; exit 1; }
	@python3 -c "import psycopg2" 2>/dev/null || { echo "$(YELLOW)⚠️  Installing psycopg2...$(NC)"; pip3 install psycopg2-binary; }
	@python3 run_tests.py --db-host $(DB_HOST) --db-port $(DB_PORT) --db-name $(DB_NAME) --db-user $(DB_USER) --db-password $(DB_PASSWORD) $(if $(filter true,$(VERBOSE)),--verbose) $(if $(filter false,$(CLEANUP)),--keep-db)

# Run only schema deployment and basic validation
.PHONY: test-schema-only
test-schema-only: deploy-schema ## Deploy schema and run basic validation
	@echo "$(CYAN)Running Basic Schema Validation...$(NC)"
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" >/dev/null 2>&1
	@echo "$(GREEN)✅ Schema validation completed$(NC)"

# Run performance tests only
.PHONY: test-performance
test-performance: deploy-schema ## Run performance benchmarks only
	@echo "$(CYAN)Running Performance Benchmarks...$(NC)"
	@echo "\\timing on" > test_logs/perf_test.sql
	@echo "SELECT COUNT(*) FROM v_medicine_details;" >> test_logs/perf_test.sql
	@echo "SELECT * FROM v_batch_details LIMIT 100;" >> test_logs/perf_test.sql
	@echo "\\timing off" >> test_logs/perf_test.sql
	@mkdir -p test_logs
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) -f test_logs/perf_test.sql
	@rm -f test_logs/perf_test.sql
	@echo "$(GREEN)✅ Performance benchmarks completed$(NC)"

# Run tests in CI/CD environment
.PHONY: test-ci
test-ci: ## Run tests suitable for CI/CD (non-interactive)
	@echo "$(CYAN)Running CI/CD Tests...$(NC)"
	@$(MAKE) test VERBOSE=false CLEANUP=true

# Generate test data for manual testing
.PHONY: generate-test-data
generate-test-data: deploy-schema ## Generate sample test data
	@echo "$(CYAN)Generating Test Data...$(NC)"
	@echo "-- Sample test data generation" > test_logs/test_data.sql
	@echo "INSERT INTO tenants (tenant_code, tenant_name, email) VALUES ('DEMO', 'Demo Pharmacy', '<EMAIL>');" >> test_logs/test_data.sql
	@echo "-- Add more test data as needed" >> test_logs/test_data.sql
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) -f test_logs/test_data.sql >/dev/null 2>&1
	@echo "$(GREEN)✅ Test data generated$(NC)"

# Backup test database
.PHONY: backup-test-db
backup-test-db: ## Backup test database
	@echo "$(CYAN)Backing up Test Database...$(NC)"
	@mkdir -p test_logs
	@PGPASSWORD=$(DB_PASSWORD) pg_dump -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) --format=custom --compress=9 --file=test_logs/$(DB_NAME)_backup_$(shell date +%Y%m%d_%H%M%S).backup
	@echo "$(GREEN)✅ Database backup completed$(NC)"

# Restore test database from backup
.PHONY: restore-test-db
restore-test-db: ## Restore test database from latest backup
	@echo "$(CYAN)Restoring Test Database...$(NC)"
	@BACKUP_FILE=$$(ls -t test_logs/$(DB_NAME)_backup_*.backup 2>/dev/null | head -1); \
	if [ -z "$$BACKUP_FILE" ]; then \
		echo "$(RED)❌ No backup file found$(NC)"; \
		exit 1; \
	fi; \
	echo "$(YELLOW)Restoring from: $$BACKUP_FILE$(NC)"; \
	PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -c "DROP DATABASE IF EXISTS $(DB_NAME);" >/dev/null 2>&1; \
	PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -c "CREATE DATABASE $(DB_NAME);" >/dev/null 2>&1; \
	PGPASSWORD=$(DB_PASSWORD) pg_restore -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME) $$BACKUP_FILE >/dev/null 2>&1
	@echo "$(GREEN)✅ Database restore completed$(NC)"

# Clean up test artifacts
.PHONY: clean
clean: ## Clean up test logs and temporary files
	@echo "$(CYAN)Cleaning up test artifacts...$(NC)"
	@rm -rf test_logs/*.log test_logs/*.html test_logs/*.json test_logs/*.sql 2>/dev/null || true
	@echo "$(GREEN)✅ Cleanup completed$(NC)"

# Clean up everything including test database
.PHONY: clean-all
clean-all: clean ## Clean up everything including test database
	@echo "$(CYAN)Cleaning up test database...$(NC)"
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -c "DROP DATABASE IF EXISTS $(DB_NAME);" >/dev/null 2>&1 || true
	@echo "$(GREEN)✅ Complete cleanup finished$(NC)"

# Show test logs
.PHONY: show-logs
show-logs: ## Show recent test logs
	@echo "$(CYAN)Recent Test Logs:$(NC)"
	@ls -la test_logs/ 2>/dev/null || echo "$(YELLOW)No test logs found$(NC)"

# Validate SQL syntax
.PHONY: validate-sql
validate-sql: ## Validate SQL file syntax
	@echo "$(CYAN)Validating SQL Syntax...$(NC)"
	@for file in *.sql; do \
		echo "$(YELLOW)Checking: $$file$(NC)"; \
		PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres --set=ON_ERROR_STOP=1 --single-transaction --dry-run -f $$file >/dev/null 2>&1 || { echo "$(RED)❌ Syntax error in $$file$(NC)"; exit 1; }; \
	done
	@echo "$(GREEN)✅ All SQL files have valid syntax$(NC)"

# Show database info
.PHONY: db-info
db-info: test-connection ## Show database information
	@echo "$(CYAN)Database Information:$(NC)"
	@echo "Host: $(DB_HOST):$(DB_PORT)"
	@echo "Database: $(DB_NAME)"
	@echo "User: $(DB_USER)"
	@echo ""
	@echo "$(CYAN)PostgreSQL Version:$(NC)"
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d postgres -t -c "SELECT version();" 2>/dev/null

# Interactive database shell
.PHONY: db-shell
db-shell: ## Open interactive database shell
	@echo "$(CYAN)Opening database shell for $(DB_NAME)...$(NC)"
	@PGPASSWORD=$(DB_PASSWORD) psql -h $(DB_HOST) -p $(DB_PORT) -U $(DB_USER) -d $(DB_NAME)

# Quick test (fast validation)
.PHONY: quick-test
quick-test: ## Run quick validation tests
	@echo "$(CYAN)Running Quick Tests...$(NC)"
	@$(MAKE) check-prereq
	@$(MAKE) test-connection
	@$(MAKE) validate-sql
	@echo "$(GREEN)✅ Quick tests completed$(NC)"

# Full test suite with all options
.PHONY: full-test
full-test: ## Run comprehensive test suite with all features
	@echo "$(CYAN)Running Full Test Suite...$(NC)"
	@$(MAKE) clean
	@$(MAKE) test-verbose
	@$(MAKE) backup-test-db
	@echo "$(GREEN)✅ Full test suite completed$(NC)"

# Development setup
.PHONY: dev-setup
dev-setup: ## Setup development environment
	@echo "$(CYAN)Setting up Development Environment...$(NC)"
	@$(MAKE) deploy-schema
	@$(MAKE) generate-test-data
	@echo "$(GREEN)✅ Development environment ready$(NC)"
	@echo "$(BLUE)You can now connect to: $(DB_NAME) on $(DB_HOST):$(DB_PORT)$(NC)"

# Production deployment check
.PHONY: prod-check
prod-check: ## Run production readiness checks
	@echo "$(CYAN)Running Production Readiness Checks...$(NC)"
	@$(MAKE) test
	@$(MAKE) test-performance
	@echo "$(GREEN)✅ Production readiness checks completed$(NC)"
	@echo "$(BLUE)System is ready for production deployment$(NC)"
