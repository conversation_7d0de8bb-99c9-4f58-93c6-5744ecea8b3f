# Pharmacy Management System V2 - Testing Guide

## 🧪 Overview

This guide provides comprehensive instructions for running tests on the Pharmacy Management System V2. The system includes multiple test runners and automation scripts to ensure thorough validation before production deployment.

## 📋 Test Suite Components

### **Test Coverage**

The test suite includes **18 comprehensive test scenarios**:

1. ✅ **Tenant Setup and Validation**
2. ✅ **User Management and Authentication**
3. ✅ **Medicine Creation and Validation**
4. ✅ **Packaging Units Management**
5. ✅ **Supplier and Inventory Management**
6. ✅ **Customer Management**
7. ✅ **Sales Transaction Processing**
8. ✅ **Stock Validation and FEFO**
9. ✅ **Insufficient Stock Handling**
10. ✅ **Data Integrity and Constraints**
11. ✅ **Price List Management**
12. ✅ **Inventory Adjustment**
13. ✅ **Prescription Processing**
14. ✅ **Loyalty Points Calculation**
15. ✅ **Return Processing**
16. ✅ **Audit Trail Verification**
17. ✅ **Performance Benchmark**
18. ✅ **Data Validation and Constraints**

## 🚀 Quick Start

### **Option 1: Using Makefile (Recommended)**

```bash
# Show all available commands
make help

# Run complete test suite
make test

# Run tests with verbose output
make test-verbose

# Run tests and keep database for inspection
make test-keep-db
```

### **Option 2: Using Shell Script (Linux/macOS)**

```bash
# Make script executable
chmod +x run_tests.sh

# Run with default settings
./run_tests.sh

# Run with custom database settings
./run_tests.sh --db-host myserver --db-user testuser --verbose
```

### **Option 3: Using Batch Script (Windows)**

```cmd
# Run with default settings
run_tests.bat

# Set custom environment variables
set DB_HOST=myserver
set DB_USER=testuser
set VERBOSE=true
run_tests.bat
```

### **Option 4: Using Python Script (Advanced)**

```bash
# Install dependencies (if needed)
pip install psycopg2-binary

# Run with default settings
python run_tests.py

# Run with custom options
python run_tests.py --db-host myserver --verbose --keep-db
```

## ⚙️ Configuration Options

### **Environment Variables**

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | localhost | Database host |
| `DB_PORT` | 5432 | Database port |
| `DB_NAME` | pharmacy_test_v2 | Test database name |
| `DB_USER` | postgres | Database user |
| `DB_PASSWORD` | (empty) | Database password |
| `VERBOSE` | false | Enable verbose output |
| `CLEANUP_AFTER_TESTS` | true | Cleanup test database |
| `PERFORMANCE_TESTS` | true | Run performance benchmarks |

### **Command Line Options**

#### **Shell Script Options:**
```bash
./run_tests.sh [OPTIONS]

Options:
  -h, --help              Show help message
  -v, --verbose           Enable verbose output
  -k, --keep-db           Keep test database after tests
  --no-performance        Skip performance tests
  --db-host HOST          Database host
  --db-port PORT          Database port
  --db-name NAME          Test database name
  --db-user USER          Database user
  --db-password PASS      Database password
```

#### **Python Script Options:**
```bash
python run_tests.py [OPTIONS]

Options:
  --db-host HOST          Database host
  --db-port PORT          Database port
  --db-name NAME          Test database name
  --db-user USER          Database user
  --db-password PASS      Database password
  --verbose               Enable verbose output
  --keep-db               Keep test database after tests
  --no-performance        Skip performance tests
```

## 📊 Test Execution Examples

### **Basic Testing**

```bash
# Quick validation
make quick-test

# Full test suite
make test

# Schema validation only
make test-schema-only

# Performance benchmarks only
make test-performance
```

### **Development Testing**

```bash
# Setup development environment
make dev-setup

# Run tests and keep database for debugging
make test-keep-db

# Generate test data for manual testing
make generate-test-data

# Open database shell for inspection
make db-shell
```

### **CI/CD Testing**

```bash
# Non-interactive testing for CI/CD
make test-ci

# Production readiness check
make prod-check

# Validate SQL syntax only
make validate-sql
```

### **Custom Database Testing**

```bash
# Test against custom database
make test DB_HOST=testserver DB_USER=testuser DB_PASSWORD=secret

# Test with custom database name
make test DB_NAME=my_test_db

# Test with verbose output
make test-verbose DB_HOST=myserver
```

## 📈 Understanding Test Results

### **Success Output**

```
========================================
TEST SUITE SUMMARY
========================================
Total Tests: 18
Passed: 18
Failed: 0
Success Rate: 100.0%
🎉 ALL TESTS PASSED! System is ready for production.
```

### **Failure Output**

```
========================================
TEST SUITE SUMMARY
========================================
Total Tests: 18
Passed: 15
Failed: 3
Success Rate: 83.3%
⚠️  Some tests failed. Please review the results.
```

### **Individual Test Results**

```
✓ TEST 1 PASSED: Tenant created successfully
✓ TEST 2 PASSED: User created successfully
✓ TEST 3 PASSED: Medicine created successfully
✗ TEST 4 FAILED: Packaging units creation failed
```

## 📁 Test Artifacts

### **Log Files Location**

All test artifacts are stored in the `test_logs/` directory:

```
test_logs/
├── test_run_20241201_143022.log      # Main execution log
├── test_output_20241201_143022.log   # Detailed test output
├── performance_20241201_143022.log   # Performance benchmarks
├── test_report_20241201_143022.html  # HTML report
├── test_report_20241201_143022.json  # JSON report
└── pharmacy_test_v2_backup_*.backup  # Database backups
```

### **Report Contents**

#### **HTML Report Includes:**
- Test execution summary
- Individual test results
- Performance benchmarks
- Configuration details
- System information

#### **JSON Report Includes:**
- Machine-readable test results
- Performance metrics
- Configuration data
- Timestamps and metadata

## 🔧 Troubleshooting

### **Common Issues**

#### **1. Database Connection Failed**

```bash
❌ Cannot connect to database
   Host: localhost:5432
   User: postgres
```

**Solutions:**
- Check if PostgreSQL is running
- Verify connection parameters
- Check firewall settings
- Ensure user has proper permissions

#### **2. Permission Denied**

```bash
❌ Failed to create test database
```

**Solutions:**
- Ensure user has CREATEDB privilege
- Run as database superuser
- Check pg_hba.conf configuration

#### **3. Missing Dependencies**

```bash
❌ PostgreSQL client (psql) not found
```

**Solutions:**
- Install PostgreSQL client tools
- Add psql to system PATH
- Use package manager: `apt install postgresql-client`

#### **4. Schema Execution Failed**

```bash
❌ Failed to execute 01_core_schema.sql
```

**Solutions:**
- Check SQL syntax
- Verify file permissions
- Review detailed error logs
- Ensure extensions are available

### **Debug Mode**

#### **Enable Verbose Logging**

```bash
# Shell script
./run_tests.sh --verbose

# Makefile
make test-verbose

# Python script
python run_tests.py --verbose
```

#### **Keep Database for Inspection**

```bash
# Keep test database after tests
make test-keep-db

# Connect to inspect
make db-shell
```

#### **Manual Schema Deployment**

```bash
# Deploy schema only
make deploy-schema

# Validate schema
make test-schema-only
```

## 🎯 Performance Benchmarks

### **Performance Metrics**

The test suite measures performance for:

- **Medicine Search**: Full-text search queries
- **Inventory Lookup**: FEFO batch selection
- **Sales Reporting**: Complex aggregation queries
- **Customer Lookup**: Customer data retrieval

### **Expected Performance**

| Operation | Target Time | Acceptable Range |
|-----------|-------------|------------------|
| Medicine Search | < 50ms | < 100ms |
| Inventory Lookup | < 30ms | < 80ms |
| Sales Reporting | < 200ms | < 500ms |
| Customer Lookup | < 40ms | < 100ms |

### **Performance Testing**

```bash
# Run performance tests only
make test-performance

# Full test with performance monitoring
python run_tests.py --verbose
```

## 🔄 Continuous Integration

### **GitHub Actions Example**

```yaml
name: Database Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v2
    - name: Run Tests
      run: |
        cd pharmacy_management_V2
        make test-ci
      env:
        DB_HOST: localhost
        DB_USER: postgres
        DB_PASSWORD: postgres
```

### **Jenkins Pipeline Example**

```groovy
pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                dir('pharmacy_management_V2') {
                    sh 'make test-ci'
                }
            }
        }
    }
    post {
        always {
            archiveArtifacts artifacts: 'pharmacy_management_V2/test_logs/*'
        }
    }
}
```

## 📞 Support

### **Getting Help**

If tests fail or you encounter issues:

1. **Check the logs** in `test_logs/` directory
2. **Review error messages** for specific guidance
3. **Run with verbose output** for detailed information
4. **Keep test database** for manual inspection
5. **Contact support** with log files if needed

### **Reporting Issues**

When reporting test failures, please include:

- Test execution logs
- System information (OS, PostgreSQL version)
- Configuration used
- Steps to reproduce
- Expected vs actual behavior

---

**Happy Testing! 🧪**

The comprehensive test suite ensures your Pharmacy Management System V2 is production-ready and performs optimally.
