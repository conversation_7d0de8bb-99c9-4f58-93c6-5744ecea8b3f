-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - INVENTORY SCHEMA
-- =====================================================
-- Enhanced inventory management with proper constraints
-- and business logic validation
-- =====================================================

-- =====================================================
-- PACKAGING & UNITS MANAGEMENT
-- =====================================================

-- Table: packaging_units (Standardized packaging units)
CREATE TABLE packaging_units (
    packaging_unit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    unit_code VARCHAR(20) NOT NULL, -- BOX, STRIP, BOTTLE, etc.
    unit_name VARCHAR(100) NOT NULL, -- "Hộp 100 viên", "Vỉ 10 viên"
    quantity_per_unit INTEGER NOT NULL, -- Base units in this package
    is_default_purchase_unit BOOLEAN DEFAULT FALSE,
    is_default_sale_unit BOOLEAN DEFAULT FALSE,
    is_default_dispensing_unit BOOLEAN DEFAULT FALSE,
    conversion_factor DECIMAL(10,4) DEFAULT 1.0, -- For complex conversions
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT uk_packaging_tenant_medicine_code UNIQUE(tenant_id, medicine_id, unit_code),
    CONSTRAINT chk_packaging_quantity CHECK (quantity_per_unit > 0),
    CONSTRAINT chk_packaging_conversion CHECK (conversion_factor > 0)
);

-- Indexes for packaging_units
CREATE INDEX idx_packaging_medicine ON packaging_units(medicine_id);
CREATE INDEX idx_packaging_tenant_active ON packaging_units(tenant_id, is_active);

-- =====================================================
-- SUPPLIER MANAGEMENT
-- =====================================================

-- Table: suppliers (Enhanced supplier information)
CREATE TABLE suppliers (
    supplier_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    supplier_code VARCHAR(50) NOT NULL,
    supplier_name VARCHAR(255) NOT NULL,
    supplier_type VARCHAR(50) DEFAULT 'DISTRIBUTOR', -- MANUFACTURER, DISTRIBUTOR, WHOLESALER
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone_number VARCHAR(20),
    fax_number VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Vietnam',
    tax_code VARCHAR(50),
    license_number VARCHAR(100),
    bank_account VARCHAR(50),
    bank_name VARCHAR(255),
    payment_terms VARCHAR(100), -- "Net 30", "COD", etc.
    credit_limit DECIMAL(15,2) DEFAULT 0,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    lead_time_days INTEGER DEFAULT 7,
    minimum_order_amount DECIMAL(15,2) DEFAULT 0,
    website VARCHAR(255),
    notes TEXT,
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    is_preferred BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT uk_suppliers_tenant_code UNIQUE(tenant_id, supplier_code),
    CONSTRAINT chk_suppliers_credit_limit CHECK (credit_limit >= 0),
    CONSTRAINT chk_suppliers_discount CHECK (discount_percentage >= 0 AND discount_percentage <= 100)
);

-- Indexes for suppliers
CREATE INDEX idx_suppliers_tenant ON suppliers(tenant_id);
CREATE INDEX idx_suppliers_code ON suppliers(tenant_id, supplier_code);
CREATE INDEX idx_suppliers_active ON suppliers(tenant_id, is_active);
CREATE INDEX idx_suppliers_preferred ON suppliers(tenant_id, is_preferred) WHERE is_preferred = TRUE;

-- =====================================================
-- PURCHASE MANAGEMENT
-- =====================================================

-- Table: purchase_orders (Purchase order headers)
CREATE TABLE purchase_orders (
    purchase_order_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    po_number VARCHAR(100) NOT NULL,
    supplier_id UUID NOT NULL REFERENCES suppliers(supplier_id),
    order_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expected_delivery_date DATE,
    delivery_address TEXT,
    status VARCHAR(50) DEFAULT 'DRAFT', -- DRAFT, SENT, CONFIRMED, PARTIAL, COMPLETED, CANCELLED
    subtotal_amount DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    shipping_cost DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'VND',
    payment_terms VARCHAR(100),
    notes TEXT,
    approved_by UUID REFERENCES users(user_id),
    approved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT uk_po_tenant_number UNIQUE(tenant_id, po_number),
    CONSTRAINT chk_po_amounts CHECK (
        subtotal_amount >= 0 AND 
        tax_amount >= 0 AND 
        discount_amount >= 0 AND 
        shipping_cost >= 0 AND
        total_amount >= 0
    )
);

-- Table: purchase_order_items (Purchase order line items)
CREATE TABLE purchase_order_items (
    po_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    purchase_order_id UUID NOT NULL REFERENCES purchase_orders(purchase_order_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    packaging_unit_id UUID NOT NULL REFERENCES packaging_units(packaging_unit_id),
    quantity_ordered DECIMAL(10,2) NOT NULL,
    quantity_received DECIMAL(10,2) DEFAULT 0,
    unit_cost DECIMAL(12,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (
        (quantity_ordered * unit_cost) - discount_amount
    ) STORED,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_po_items_quantities CHECK (
        quantity_ordered > 0 AND 
        quantity_received >= 0 AND 
        quantity_received <= quantity_ordered
    ),
    CONSTRAINT chk_po_items_costs CHECK (
        unit_cost >= 0 AND 
        discount_percentage >= 0 AND 
        discount_percentage <= 100 AND
        discount_amount >= 0
    )
);

-- =====================================================
-- RECEIVING & BATCH MANAGEMENT
-- =====================================================

-- Table: goods_receipts (Goods receipt headers)
CREATE TABLE goods_receipts (
    receipt_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    receipt_number VARCHAR(100) NOT NULL,
    purchase_order_id UUID REFERENCES purchase_orders(purchase_order_id),
    supplier_id UUID NOT NULL REFERENCES suppliers(supplier_id),
    supplier_invoice_number VARCHAR(100),
    receipt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    delivery_note_number VARCHAR(100),
    received_by UUID NOT NULL REFERENCES users(user_id),
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, COMPLETED, CANCELLED
    total_amount DECIMAL(15,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT uk_receipt_tenant_number UNIQUE(tenant_id, receipt_number)
);

-- Table: goods_receipt_items (Goods receipt line items)
CREATE TABLE goods_receipt_items (
    receipt_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    receipt_id UUID NOT NULL REFERENCES goods_receipts(receipt_id),
    po_item_id UUID REFERENCES purchase_order_items(po_item_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    packaging_unit_id UUID NOT NULL REFERENCES packaging_units(packaging_unit_id),
    quantity_received DECIMAL(10,2) NOT NULL,
    quantity_in_base_units DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(12,2) NOT NULL,
    line_total DECIMAL(15,2) GENERATED ALWAYS AS (
        quantity_received * unit_cost
    ) STORED,
    batch_number VARCHAR(100) NOT NULL,
    manufacturing_date DATE,
    expiry_date DATE NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_receipt_items_quantities CHECK (
        quantity_received > 0 AND 
        quantity_in_base_units > 0
    ),
    CONSTRAINT chk_receipt_items_dates CHECK (
        manufacturing_date IS NULL OR manufacturing_date <= CURRENT_DATE
    ),
    CONSTRAINT chk_receipt_expiry_date CHECK (expiry_date > CURRENT_DATE)
);

-- =====================================================
-- INVENTORY BATCHES
-- =====================================================

-- Table: inventory_batches (Enhanced batch tracking)
CREATE TABLE inventory_batches (
    batch_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    medicine_id UUID NOT NULL REFERENCES medicines(medicine_id),
    supplier_id UUID REFERENCES suppliers(supplier_id),
    receipt_item_id UUID REFERENCES goods_receipt_items(receipt_item_id),
    batch_number VARCHAR(100) NOT NULL,
    internal_batch_code VARCHAR(50), -- Internal tracking code
    manufacturing_date DATE,
    expiry_date DATE NOT NULL,
    received_date DATE NOT NULL DEFAULT CURRENT_DATE,
    quantity_received DECIMAL(10,2) NOT NULL,
    quantity_available DECIMAL(10,2) NOT NULL,
    quantity_reserved DECIMAL(10,2) DEFAULT 0,
    quantity_sold DECIMAL(10,2) DEFAULT 0,
    quantity_adjusted DECIMAL(10,2) DEFAULT 0,
    unit_cost DECIMAL(12,2) NOT NULL,
    selling_price DECIMAL(12,2),
    location_code VARCHAR(50), -- Storage location
    storage_conditions TEXT,
    quality_status VARCHAR(50) DEFAULT 'GOOD', -- GOOD, DAMAGED, EXPIRED, QUARANTINE
    is_recalled BOOLEAN DEFAULT FALSE,
    recall_reason TEXT,
    recall_date DATE,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT uk_batches_tenant_medicine_batch UNIQUE(tenant_id, medicine_id, batch_number),
    CONSTRAINT chk_batches_quantities CHECK (
        quantity_received >= 0 AND
        quantity_available >= 0 AND
        quantity_reserved >= 0 AND
        quantity_sold >= 0 AND
        quantity_available + quantity_sold + quantity_adjusted = quantity_received AND
        quantity_reserved <= quantity_available
    ),
    CONSTRAINT chk_batches_dates CHECK (
        manufacturing_date IS NULL OR manufacturing_date <= received_date
    ),
    CONSTRAINT chk_batches_expiry CHECK (expiry_date > received_date),
    CONSTRAINT chk_batches_costs CHECK (
        unit_cost >= 0 AND 
        (selling_price IS NULL OR selling_price >= 0)
    )
);

-- Indexes for inventory_batches
CREATE INDEX idx_batches_tenant_medicine ON inventory_batches(tenant_id, medicine_id);
CREATE INDEX idx_batches_expiry ON inventory_batches(expiry_date) WHERE quantity_available > 0;
CREATE INDEX idx_batches_batch_number ON inventory_batches(tenant_id, batch_number);
CREATE INDEX idx_batches_supplier ON inventory_batches(supplier_id);
CREATE INDEX idx_batches_quality ON inventory_batches(quality_status) WHERE quality_status != 'GOOD';
CREATE INDEX idx_batches_available ON inventory_batches(tenant_id, medicine_id, quantity_available) 
    WHERE quantity_available > 0 AND quality_status = 'GOOD';

-- =====================================================
-- INVENTORY ADJUSTMENTS
-- =====================================================

-- Table: adjustment_types (Standardized adjustment types)
CREATE TABLE adjustment_types (
    adjustment_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    type_code VARCHAR(20) NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    adjustment_direction VARCHAR(20) NOT NULL, -- INCREASE, DECREASE, SET_TO
    requires_approval BOOLEAN DEFAULT FALSE,
    is_system_type BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    CONSTRAINT uk_adjustment_types_tenant_code UNIQUE(tenant_id, type_code),
    CONSTRAINT chk_adjustment_direction CHECK (
        adjustment_direction IN ('INCREASE', 'DECREASE', 'SET_TO')
    )
);

-- Insert standard adjustment types
INSERT INTO adjustment_types (tenant_id, type_code, type_name, description, adjustment_direction, is_system_type) 
SELECT 
    t.tenant_id,
    unnest(ARRAY['DAMAGE', 'EXPIRY', 'THEFT', 'COUNT', 'RETURN', 'TRANSFER']),
    unnest(ARRAY['Damaged Goods', 'Expired Items', 'Theft/Loss', 'Stock Count Adjustment', 'Customer Return', 'Transfer']),
    unnest(ARRAY['Items damaged during handling', 'Expired medications', 'Stolen or lost items', 'Physical count adjustment', 'Items returned by customers', 'Transfer between locations']),
    unnest(ARRAY['DECREASE', 'DECREASE', 'DECREASE', 'SET_TO', 'INCREASE', 'DECREASE']),
    TRUE
FROM tenants t WHERE t.is_active = TRUE;

-- Table: inventory_adjustments (Enhanced adjustment tracking)
CREATE TABLE inventory_adjustments (
    adjustment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    adjustment_number VARCHAR(100) NOT NULL,
    batch_id UUID NOT NULL REFERENCES inventory_batches(batch_id),
    adjustment_type_id UUID NOT NULL REFERENCES adjustment_types(adjustment_type_id),
    adjustment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    quantity_before DECIMAL(10,2) NOT NULL,
    quantity_change DECIMAL(10,2) NOT NULL,
    quantity_after DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(12,2),
    total_cost_impact DECIMAL(15,2) GENERATED ALWAYS AS (
        quantity_change * COALESCE(unit_cost, 0)
    ) STORED,
    reason TEXT NOT NULL,
    reference_document VARCHAR(100),
    approved_by UUID REFERENCES users(user_id),
    approved_at TIMESTAMPTZ,
    performed_by UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    
    CONSTRAINT uk_adjustment_tenant_number UNIQUE(tenant_id, adjustment_number),
    CONSTRAINT chk_adjustment_quantities CHECK (
        quantity_before >= 0 AND
        quantity_after >= 0 AND
        quantity_after = quantity_before + quantity_change
    )
);

-- Indexes for inventory_adjustments
CREATE INDEX idx_adjustments_tenant_date ON inventory_adjustments(tenant_id, adjustment_date);
CREATE INDEX idx_adjustments_batch ON inventory_adjustments(batch_id);
CREATE INDEX idx_adjustments_type ON inventory_adjustments(adjustment_type_id);
CREATE INDEX idx_adjustments_performed_by ON inventory_adjustments(performed_by);
