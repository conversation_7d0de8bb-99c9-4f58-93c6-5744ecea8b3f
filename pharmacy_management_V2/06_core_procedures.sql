-- =====================================================
-- PHARMACY MANAGEMENT SYSTEM V2 - CORE PROCEDURES
-- =====================================================
-- Enhanced stored procedures with proper error handling,
-- validation, and business logic implementation
-- =====================================================

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to generate next sequence number for various entities
CREATE OR REPLACE FUNCTION get_next_sequence_number(
    p_tenant_id UUID,
    p_sequence_type VARCHAR(50),
    p_prefix VARCHAR(10) DEFAULT '',
    p_date_format VARCHAR(20) DEFAULT 'YYYYMMDD'
)
RETURNS VARCHAR(100) AS $$
DECLARE
    v_date_part VARCHAR(20);
    v_sequence_key VARCHAR(100);
    v_next_number INTEGER;
    v_formatted_number VARCHAR(100);
BEGIN
    -- Generate date part
    v_date_part := TO_CHAR(CURRENT_DATE, p_date_format);

    -- Create sequence key
    v_sequence_key := p_tenant_id::text || '_' || p_sequence_type || '_' || v_date_part;

    -- Get next number based on sequence type
    IF p_sequence_type = 'TRANSACTION' THEN
        SELECT COALESCE(MAX(CAST(SUBSTRING(transaction_number FROM LENGTH(p_prefix || v_date_part) + 1) AS INTEGER)), 0) + 1
        INTO v_next_number
        FROM sales_transactions
        WHERE tenant_id = p_tenant_id
        AND transaction_number LIKE p_prefix || v_date_part || '%';
    ELSIF p_sequence_type = 'PRESCRIPTION' THEN
        SELECT COALESCE(MAX(CAST(SUBSTRING(prescription_number FROM LENGTH(p_prefix || v_date_part) + 1) AS INTEGER)), 0) + 1
        INTO v_next_number
        FROM prescriptions
        WHERE tenant_id = p_tenant_id
        AND prescription_number LIKE p_prefix || v_date_part || '%';
    ELSIF p_sequence_type = 'RECEIPT' THEN
        SELECT COALESCE(MAX(CAST(SUBSTRING(receipt_number FROM LENGTH(p_prefix || v_date_part) + 1) AS INTEGER)), 0) + 1
        INTO v_next_number
        FROM goods_receipts
        WHERE tenant_id = p_tenant_id
        AND receipt_number LIKE p_prefix || v_date_part || '%';
    ELSE
        v_next_number := 1;
    END IF;

    -- Format the final number
    v_formatted_number := p_prefix || v_date_part || LPAD(v_next_number::text, 4, '0');

    RETURN v_formatted_number;
END;
$$ LANGUAGE plpgsql;

-- Function to validate tenant access
CREATE OR REPLACE FUNCTION validate_tenant_access(p_tenant_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM tenants
        WHERE tenant_id = p_tenant_id
        AND is_active = TRUE
        AND (subscription_end_date IS NULL OR subscription_end_date >= CURRENT_DATE)
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get effective price for a medicine
CREATE OR REPLACE FUNCTION get_medicine_price(
    p_tenant_id UUID,
    p_medicine_id UUID,
    p_packaging_unit_id UUID,
    p_customer_type_id UUID DEFAULT NULL,
    p_quantity DECIMAL(10,2) DEFAULT 1,
    p_price_date DATE DEFAULT CURRENT_DATE
)
RETURNS DECIMAL(12,2) AS $$
DECLARE
    v_price DECIMAL(12,2);
    v_price_list_id UUID;
BEGIN
    -- Find applicable price list
    SELECT pl.price_list_id INTO v_price_list_id
    FROM price_lists pl
    WHERE pl.tenant_id = p_tenant_id
    AND pl.is_active = TRUE
    AND pl.effective_from <= p_price_date
    AND (pl.effective_to IS NULL OR pl.effective_to >= p_price_date)
    AND (pl.customer_type_id = p_customer_type_id OR pl.customer_type_id IS NULL)
    ORDER BY
        CASE WHEN pl.customer_type_id = p_customer_type_id THEN 1 ELSE 2 END,
        pl.effective_from DESC
    LIMIT 1;

    -- Get price from the price list
    SELECT mp.unit_price INTO v_price
    FROM medicine_prices mp
    WHERE mp.tenant_id = p_tenant_id
    AND mp.price_list_id = v_price_list_id
    AND mp.medicine_id = p_medicine_id
    AND mp.packaging_unit_id = p_packaging_unit_id
    AND mp.is_active = TRUE
    AND mp.effective_from <= p_price_date
    AND (mp.effective_to IS NULL OR mp.effective_to >= p_price_date)
    AND mp.min_quantity <= p_quantity
    AND (mp.max_quantity IS NULL OR mp.max_quantity >= p_quantity)
    ORDER BY mp.effective_from DESC
    LIMIT 1;

    -- If no specific price found, try to get from batch selling price
    IF v_price IS NULL THEN
        SELECT AVG(ib.selling_price) INTO v_price
        FROM inventory_batches ib
        JOIN packaging_units pu ON pu.medicine_id = ib.medicine_id
        WHERE ib.tenant_id = p_tenant_id
        AND ib.medicine_id = p_medicine_id
        AND pu.packaging_unit_id = p_packaging_unit_id
        AND ib.quantity_available > 0
        AND ib.quality_status = 'GOOD'
        AND ib.selling_price IS NOT NULL;
    END IF;

    RETURN COALESCE(v_price, 0);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MEDICINE MANAGEMENT PROCEDURES
-- =====================================================

-- Procedure to create a new medicine
CREATE OR REPLACE FUNCTION create_medicine(
    p_tenant_id UUID,
    p_medicine_code VARCHAR(50),
    p_medicine_name VARCHAR(255),
    p_generic_name VARCHAR(255) DEFAULT NULL,
    p_brand_name VARCHAR(255) DEFAULT NULL,
    p_registration_number VARCHAR(100) DEFAULT NULL,
    p_barcode VARCHAR(50) DEFAULT NULL,
    p_category_id UUID DEFAULT NULL,
    p_manufacturer_id UUID DEFAULT NULL,
    p_dosage_form_id UUID DEFAULT NULL,
    p_strength VARCHAR(100) DEFAULT NULL,
    p_base_unit VARCHAR(50) DEFAULT 'UNIT',
    p_pack_size INTEGER DEFAULT 1,
    p_is_prescription_required BOOLEAN DEFAULT FALSE,
    p_created_by UUID DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    medicine_id UUID,
    message TEXT
) AS $$
DECLARE
    v_medicine_id UUID;
    v_message TEXT;
BEGIN
    -- Validate tenant
    IF NOT validate_tenant_access(p_tenant_id) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid or inactive tenant'::TEXT;
        RETURN;
    END IF;

    -- Validate required fields
    IF p_medicine_code IS NULL OR TRIM(p_medicine_code) = '' THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Medicine code is required'::TEXT;
        RETURN;
    END IF;

    IF p_medicine_name IS NULL OR TRIM(p_medicine_name) = '' THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Medicine name is required'::TEXT;
        RETURN;
    END IF;

    -- Check for duplicates
    IF EXISTS (
        SELECT 1 FROM medicines
        WHERE tenant_id = p_tenant_id
        AND medicine_code = p_medicine_code
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Medicine code already exists'::TEXT;
        RETURN;
    END IF;

    -- Validate foreign key references
    IF p_category_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM medicine_categories
        WHERE category_id = p_category_id AND tenant_id = p_tenant_id
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid category ID'::TEXT;
        RETURN;
    END IF;

    IF p_manufacturer_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM manufacturers WHERE manufacturer_id = p_manufacturer_id
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid manufacturer ID'::TEXT;
        RETURN;
    END IF;

    IF p_dosage_form_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM dosage_forms WHERE dosage_form_id = p_dosage_form_id
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid dosage form ID'::TEXT;
        RETURN;
    END IF;

    -- Insert the medicine
    INSERT INTO medicines (
        tenant_id, medicine_code, medicine_name, generic_name, brand_name,
        registration_number, barcode, category_id, manufacturer_id, dosage_form_id,
        strength, base_unit, pack_size, is_prescription_required, created_by
    ) VALUES (
        p_tenant_id, p_medicine_code, p_medicine_name, p_generic_name, p_brand_name,
        p_registration_number, p_barcode, p_category_id, p_manufacturer_id, p_dosage_form_id,
        p_strength, p_base_unit, p_pack_size, p_is_prescription_required, p_created_by
    ) RETURNING medicines.medicine_id INTO v_medicine_id;

    v_message := 'Medicine created successfully';

    RETURN QUERY SELECT TRUE, v_medicine_id, v_message;

EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, ('Error creating medicine: ' || SQLERRM)::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INVENTORY MANAGEMENT PROCEDURES
-- =====================================================

-- Function to find best batch for sale (FEFO - First Expired, First Out)
CREATE OR REPLACE FUNCTION find_best_batch_for_sale(
    p_tenant_id UUID,
    p_medicine_id UUID,
    p_quantity_needed DECIMAL(10,2)
)
RETURNS TABLE(
    batch_id UUID,
    available_quantity DECIMAL(10,2),
    expiry_date DATE,
    unit_cost DECIMAL(12,2),
    selling_price DECIMAL(12,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ib.batch_id,
        ib.quantity_available,
        ib.expiry_date,
        ib.unit_cost,
        ib.selling_price
    FROM inventory_batches ib
    WHERE ib.tenant_id = p_tenant_id
    AND ib.medicine_id = p_medicine_id
    AND ib.quantity_available >= p_quantity_needed
    AND ib.quality_status = 'GOOD'
    AND ib.expiry_date > CURRENT_DATE
    AND NOT ib.is_recalled
    ORDER BY
        ib.expiry_date ASC,  -- FEFO: First Expired, First Out
        ib.received_date ASC -- Then FIFO: First In, First Out
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to get available stock for a medicine
CREATE OR REPLACE FUNCTION get_available_stock(
    p_tenant_id UUID,
    p_medicine_id UUID
)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    v_total_stock DECIMAL(10,2);
BEGIN
    SELECT COALESCE(SUM(quantity_available), 0)
    INTO v_total_stock
    FROM inventory_batches
    WHERE tenant_id = p_tenant_id
    AND medicine_id = p_medicine_id
    AND quality_status = 'GOOD'
    AND expiry_date > CURRENT_DATE
    AND NOT is_recalled;

    RETURN v_total_stock;
END;
$$ LANGUAGE plpgsql;

-- Procedure to receive goods and create batches
CREATE OR REPLACE FUNCTION receive_goods(
    p_tenant_id UUID,
    p_receipt_number VARCHAR(100),
    p_supplier_id UUID,
    p_received_by UUID,
    p_items JSONB, -- Array of {medicine_id, packaging_unit_id, quantity, unit_cost, batch_number, expiry_date}
    p_notes TEXT DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    receipt_id UUID,
    message TEXT
) AS $$
DECLARE
    v_receipt_id UUID;
    v_item JSONB;
    v_medicine_id UUID;
    v_packaging_unit_id UUID;
    v_quantity DECIMAL(10,2);
    v_unit_cost DECIMAL(12,2);
    v_batch_number VARCHAR(100);
    v_expiry_date DATE;
    v_quantity_in_base_units DECIMAL(10,2);
    v_conversion_factor DECIMAL(10,4);
    v_batch_id UUID;
    v_message TEXT;
BEGIN
    -- Validate tenant
    IF NOT validate_tenant_access(p_tenant_id) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid or inactive tenant'::TEXT;
        RETURN;
    END IF;

    -- Validate supplier
    IF NOT EXISTS (
        SELECT 1 FROM suppliers
        WHERE supplier_id = p_supplier_id AND tenant_id = p_tenant_id AND is_active = TRUE
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid or inactive supplier'::TEXT;
        RETURN;
    END IF;

    -- Validate received_by user
    IF NOT EXISTS (
        SELECT 1 FROM users
        WHERE user_id = p_received_by AND tenant_id = p_tenant_id AND is_active = TRUE
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid user'::TEXT;
        RETURN;
    END IF;

    -- Create goods receipt header
    INSERT INTO goods_receipts (
        tenant_id, receipt_number, supplier_id, received_by, notes
    ) VALUES (
        p_tenant_id, p_receipt_number, p_supplier_id, p_received_by, p_notes
    ) RETURNING goods_receipts.receipt_id INTO v_receipt_id;

    -- Process each item
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Extract item data
        v_medicine_id := (v_item->>'medicine_id')::UUID;
        v_packaging_unit_id := (v_item->>'packaging_unit_id')::UUID;
        v_quantity := (v_item->>'quantity')::DECIMAL(10,2);
        v_unit_cost := (v_item->>'unit_cost')::DECIMAL(12,2);
        v_batch_number := v_item->>'batch_number';
        v_expiry_date := (v_item->>'expiry_date')::DATE;

        -- Get conversion factor to base units
        SELECT pu.quantity_per_unit INTO v_conversion_factor
        FROM packaging_units pu
        WHERE pu.packaging_unit_id = v_packaging_unit_id
        AND pu.medicine_id = v_medicine_id
        AND pu.tenant_id = p_tenant_id;

        IF NOT FOUND THEN
            RETURN QUERY SELECT FALSE, NULL::UUID,
                ('Invalid packaging unit for medicine: ' || v_medicine_id::text)::TEXT;
            RETURN;
        END IF;

        v_quantity_in_base_units := v_quantity * v_conversion_factor;

        -- Create goods receipt item
        INSERT INTO goods_receipt_items (
            tenant_id, receipt_id, medicine_id, packaging_unit_id,
            quantity_received, quantity_in_base_units, unit_cost,
            batch_number, expiry_date
        ) VALUES (
            p_tenant_id, v_receipt_id, v_medicine_id, v_packaging_unit_id,
            v_quantity, v_quantity_in_base_units, v_unit_cost,
            v_batch_number, v_expiry_date
        );

        -- Create or update inventory batch
        INSERT INTO inventory_batches (
            tenant_id, medicine_id, supplier_id, batch_number,
            expiry_date, quantity_received, quantity_available,
            unit_cost, created_by
        ) VALUES (
            p_tenant_id, v_medicine_id, p_supplier_id, v_batch_number,
            v_expiry_date, v_quantity_in_base_units, v_quantity_in_base_units,
            v_unit_cost / v_conversion_factor, p_received_by
        ) ON CONFLICT (tenant_id, medicine_id, batch_number)
        DO UPDATE SET
            quantity_received = inventory_batches.quantity_received + v_quantity_in_base_units,
            quantity_available = inventory_batches.quantity_available + v_quantity_in_base_units,
            updated_at = CURRENT_TIMESTAMP;
    END LOOP;

    -- Update receipt status
    UPDATE goods_receipts
    SET status = 'COMPLETED',
        total_amount = (
            SELECT SUM(gri.line_total)
            FROM goods_receipt_items gri
            WHERE gri.receipt_id = v_receipt_id
        )
    WHERE goods_receipts.receipt_id = v_receipt_id;

    v_message := 'Goods received successfully';

    RETURN QUERY SELECT TRUE, v_receipt_id, v_message;

EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, ('Error receiving goods: ' || SQLERRM)::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SALES PROCESSING PROCEDURES
-- =====================================================

-- Function to process a sales transaction
CREATE OR REPLACE FUNCTION process_sales_transaction(
    p_tenant_id UUID,
    p_cashier_id UUID,
    p_items JSONB, -- Array of {medicine_id, quantity, unit_price_override, batch_id_override}
    p_payment_methods JSONB, -- Array of {payment_method_id, amount, reference_number}
    p_customer_id UUID DEFAULT NULL,
    p_prescription_id UUID DEFAULT NULL,
    p_pharmacist_id UUID DEFAULT NULL,
    p_discount_amount DECIMAL(15,2) DEFAULT 0,
    p_notes TEXT DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    transaction_id UUID,
    transaction_number VARCHAR(100),
    total_amount DECIMAL(15,2),
    message TEXT
) AS $$
DECLARE
    v_transaction_id UUID;
    v_transaction_number VARCHAR(100);
    v_item JSONB;
    v_payment JSONB;
    v_medicine_id UUID;
    v_quantity DECIMAL(10,2);
    v_unit_price DECIMAL(12,2);
    v_batch_id UUID;
    v_available_quantity DECIMAL(10,2);
    v_unit_cost DECIMAL(12,2);
    v_subtotal DECIMAL(15,2) := 0;
    v_total_amount DECIMAL(15,2);
    v_total_payments DECIMAL(15,2) := 0;
    v_line_number INTEGER := 1;
    v_customer_type_id UUID;
    v_default_packaging_unit_id UUID;
    v_message TEXT;

    -- Settings validation variables
    v_allow_zero_price BOOLEAN;
    v_allow_100_percent_discount BOOLEAN;
    v_max_discount_percentage DECIMAL(5,2);
    v_require_manager_approval BOOLEAN;
    v_require_reason BOOLEAN;
    v_discount_percentage DECIMAL(5,2);
BEGIN
    -- Validate tenant
    IF NOT validate_tenant_access(p_tenant_id) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL, 'Invalid or inactive tenant'::TEXT;
        RETURN;
    END IF;

    -- Validate cashier
    IF NOT EXISTS (
        SELECT 1 FROM users
        WHERE user_id = p_cashier_id AND tenant_id = p_tenant_id AND is_active = TRUE
    ) THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL, 'Invalid cashier'::TEXT;
        RETURN;
    END IF;

    -- Load tenant settings for sales validation
    v_allow_zero_price := get_tenant_setting_boolean(p_tenant_id, 'SALES', 'ALLOW_ZERO_PRICE_SALES', FALSE);
    v_allow_100_percent_discount := get_tenant_setting_boolean(p_tenant_id, 'SALES', 'ALLOW_100_PERCENT_DISCOUNT', FALSE);
    v_max_discount_percentage := get_tenant_setting_decimal(p_tenant_id, 'SALES', 'MAX_DISCOUNT_PERCENTAGE', 50.0);
    v_require_manager_approval := get_tenant_setting_boolean(p_tenant_id, 'SALES', 'REQUIRE_MANAGER_APPROVAL_ZERO_SALES', TRUE);
    v_require_reason := get_tenant_setting_boolean(p_tenant_id, 'SALES', 'REQUIRE_REASON_ZERO_SALES', TRUE);

    -- Get customer type for pricing
    IF p_customer_id IS NOT NULL THEN
        SELECT customer_type_id INTO v_customer_type_id
        FROM customers
        WHERE customer_id = p_customer_id AND tenant_id = p_tenant_id;
    END IF;

    -- Generate transaction number
    v_transaction_number := get_next_sequence_number(p_tenant_id, 'TRANSACTION', 'TXN');

    -- Create transaction header
    INSERT INTO sales_transactions (
        tenant_id, transaction_number, customer_id, prescription_id,
        cashier_id, pharmacist_id, discount_amount, notes
    ) VALUES (
        p_tenant_id, v_transaction_number, p_customer_id, p_prescription_id,
        p_cashier_id, p_pharmacist_id, p_discount_amount, p_notes
    ) RETURNING sales_transactions.transaction_id INTO v_transaction_id;

    -- Process each item
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        v_medicine_id := (v_item->>'medicine_id')::UUID;
        v_quantity := (v_item->>'quantity')::DECIMAL(10,2);
        v_unit_price := COALESCE((v_item->>'unit_price_override')::DECIMAL(12,2), 0);
        v_batch_id := (v_item->>'batch_id_override')::UUID;

        -- Get default packaging unit if not specified
        SELECT packaging_unit_id INTO v_default_packaging_unit_id
        FROM packaging_units
        WHERE medicine_id = v_medicine_id
        AND tenant_id = p_tenant_id
        AND is_default_sale_unit = TRUE
        LIMIT 1;

        -- If no price override, get standard price
        IF v_unit_price = 0 THEN
            v_unit_price := get_medicine_price(
                p_tenant_id, v_medicine_id, v_default_packaging_unit_id,
                v_customer_type_id, v_quantity
            );
        END IF;

        -- Find best batch if not specified
        IF v_batch_id IS NULL THEN
            SELECT fb.batch_id, fb.available_quantity, fb.unit_cost
            INTO v_batch_id, v_available_quantity, v_unit_cost
            FROM find_best_batch_for_sale(p_tenant_id, v_medicine_id, v_quantity) fb;

            IF v_batch_id IS NULL THEN
                RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
                    ('Insufficient stock for medicine: ' || v_medicine_id::text)::TEXT;
                RETURN;
            END IF;
        ELSE
            -- Validate specified batch
            SELECT quantity_available, unit_cost
            INTO v_available_quantity, v_unit_cost
            FROM inventory_batches
            WHERE batch_id = v_batch_id
            AND tenant_id = p_tenant_id
            AND quality_status = 'GOOD'
            AND expiry_date > CURRENT_DATE;

            IF NOT FOUND OR v_available_quantity < v_quantity THEN
                RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
                    ('Invalid or insufficient batch: ' || v_batch_id::text)::TEXT;
                RETURN;
            END IF;
        END IF;

        -- Create transaction item
        INSERT INTO sales_transaction_items (
            tenant_id, transaction_id, line_number, medicine_id, batch_id,
            packaging_unit_id, quantity_sold, quantity_in_base_units,
            unit_price, unit_cost
        ) VALUES (
            p_tenant_id, v_transaction_id, v_line_number, v_medicine_id, v_batch_id,
            v_default_packaging_unit_id, v_quantity, v_quantity,
            v_unit_price, v_unit_cost
        );

        v_subtotal := v_subtotal + (v_quantity * v_unit_price);
        v_line_number := v_line_number + 1;
    END LOOP;

    -- Calculate total amount
    v_total_amount := v_subtotal - p_discount_amount;

    -- Validate sales policies
    -- Check discount percentage
    IF v_subtotal > 0 THEN
        v_discount_percentage := (p_discount_amount / v_subtotal) * 100;
    ELSE
        v_discount_percentage := 0;
    END IF;

    -- Check if discount exceeds maximum allowed
    IF v_discount_percentage > v_max_discount_percentage THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
            ('Discount percentage (' || v_discount_percentage::TEXT || '%) exceeds maximum allowed (' || v_max_discount_percentage::TEXT || '%)')::TEXT;
        RETURN;
    END IF;

    -- Check 100% discount policy
    IF v_discount_percentage >= 100 AND NOT v_allow_100_percent_discount THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
            'Policy violation: 100% discount not allowed. Enable ALLOW_100_PERCENT_DISCOUNT setting or reduce discount.'::TEXT;
        RETURN;
    END IF;

    -- Check zero price sales policy
    IF v_total_amount = 0 AND NOT v_allow_zero_price THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
            'Policy violation: Zero price sales not allowed. Enable ALLOW_ZERO_PRICE_SALES setting or adjust pricing.'::TEXT;
        RETURN;
    END IF;

    -- Check if manager approval required for zero sales
    IF v_total_amount = 0 AND v_require_manager_approval THEN
        -- In a real implementation, you would check if the current user has manager role
        -- For now, we'll add a note to the transaction
        IF p_notes IS NULL OR p_notes = '' THEN
            RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
                'Manager approval required for zero price sales. Please add approval note.'::TEXT;
            RETURN;
        END IF;
    END IF;

    -- Check if reason required for zero sales
    IF v_total_amount = 0 AND v_require_reason THEN
        IF p_notes IS NULL OR LENGTH(TRIM(p_notes)) < 10 THEN
            RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
                'Reason required for zero price sales. Please provide detailed explanation (minimum 10 characters).'::TEXT;
            RETURN;
        END IF;
    END IF;

    -- Process payments
    FOR v_payment IN SELECT * FROM jsonb_array_elements(p_payment_methods)
    LOOP
        INSERT INTO transaction_payments (
            tenant_id, transaction_id, payment_sequence, payment_method_id,
            payment_amount, reference_number, payment_status, processed_at
        ) VALUES (
            p_tenant_id, v_transaction_id,
            (SELECT COALESCE(MAX(tp.payment_sequence), 0) + 1 FROM transaction_payments tp WHERE tp.transaction_id = v_transaction_id),
            (v_payment->>'payment_method_id')::UUID,
            (v_payment->>'amount')::DECIMAL(15,2),
            v_payment->>'reference_number',
            'APPROVED',
            CURRENT_TIMESTAMP
        );

        v_total_payments := v_total_payments + (v_payment->>'amount')::DECIMAL(15,2);
    END LOOP;

    -- Update transaction totals
    UPDATE sales_transactions
    SET subtotal_amount = v_subtotal,
        amount_paid = v_total_payments,
        change_amount = GREATEST(v_total_payments - v_total_amount, 0),
        status = CASE WHEN v_total_payments >= v_total_amount THEN 'COMPLETED' ELSE 'PENDING' END,
        payment_status = CASE
            WHEN v_total_payments = 0 THEN 'UNPAID'
            WHEN v_total_payments < v_total_amount THEN 'PARTIAL'
            WHEN v_total_payments = v_total_amount THEN 'PAID'
            ELSE 'OVERPAID'
        END
    WHERE sales_transactions.transaction_id = v_transaction_id;

    v_message := 'Transaction processed successfully';

    RETURN QUERY SELECT TRUE, v_transaction_id, v_transaction_number, v_total_amount, v_message;

EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::VARCHAR, 0::DECIMAL,
            ('Error processing transaction: ' || SQLERRM)::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TENANT SETTINGS MANAGEMENT FUNCTIONS
-- =====================================================

-- Function: get_tenant_setting (Get setting value with type conversion)
CREATE OR REPLACE FUNCTION get_tenant_setting(
    p_tenant_id UUID,
    p_category VARCHAR(50),
    p_key VARCHAR(100),
    p_default_value TEXT DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    v_setting_value TEXT;
BEGIN
    SELECT setting_value INTO v_setting_value
    FROM tenant_settings
    WHERE tenant_id = p_tenant_id
    AND setting_category = p_category
    AND setting_key = p_key
    AND is_active = TRUE;

    RETURN COALESCE(v_setting_value, p_default_value);
END;
$$ LANGUAGE plpgsql;

-- Function: get_tenant_setting_boolean (Get boolean setting)
CREATE OR REPLACE FUNCTION get_tenant_setting_boolean(
    p_tenant_id UUID,
    p_category VARCHAR(50),
    p_key VARCHAR(100),
    p_default_value BOOLEAN DEFAULT FALSE
)
RETURNS BOOLEAN AS $$
DECLARE
    v_setting_value TEXT;
BEGIN
    v_setting_value := get_tenant_setting(p_tenant_id, p_category, p_key, p_default_value::TEXT);

    RETURN CASE
        WHEN LOWER(v_setting_value) IN ('true', '1', 'yes', 'on') THEN TRUE
        WHEN LOWER(v_setting_value) IN ('false', '0', 'no', 'off') THEN FALSE
        ELSE p_default_value
    END;
END;
$$ LANGUAGE plpgsql;

-- Function: get_tenant_setting_decimal (Get decimal setting)
CREATE OR REPLACE FUNCTION get_tenant_setting_decimal(
    p_tenant_id UUID,
    p_category VARCHAR(50),
    p_key VARCHAR(100),
    p_default_value DECIMAL DEFAULT 0
)
RETURNS DECIMAL AS $$
DECLARE
    v_setting_value TEXT;
BEGIN
    v_setting_value := get_tenant_setting(p_tenant_id, p_category, p_key, p_default_value::TEXT);

    BEGIN
        RETURN v_setting_value::DECIMAL;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN p_default_value;
    END;
END;
$$ LANGUAGE plpgsql;

-- Function: set_tenant_setting (Update or create setting)
CREATE OR REPLACE FUNCTION set_tenant_setting(
    p_tenant_id UUID,
    p_category VARCHAR(50),
    p_key VARCHAR(100),
    p_value TEXT,
    p_setting_type VARCHAR(20) DEFAULT 'STRING',
    p_description TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO tenant_settings (
        tenant_id, setting_category, setting_key, setting_value,
        setting_type, description, updated_by
    ) VALUES (
        p_tenant_id, p_category, p_key, p_value,
        p_setting_type, p_description, p_user_id
    )
    ON CONFLICT (tenant_id, setting_category, setting_key)
    DO UPDATE SET
        setting_value = EXCLUDED.setting_value,
        setting_type = EXCLUDED.setting_type,
        description = COALESCE(EXCLUDED.description, tenant_settings.description),
        updated_at = CURRENT_TIMESTAMP,
        updated_by = EXCLUDED.updated_by;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;